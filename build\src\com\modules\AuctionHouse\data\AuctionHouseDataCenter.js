import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { Connection } from "../../../net/Connection";
import { m_auction_house_op_tos } from "../../../proto/line/m_auction_house_op_tos";
import { LordTreasureDataCenter } from "../../LordTreasure/data/LordTreasureDataCenter";
import { GoodsManager } from "../../test_bag/GoodsManager";
export var EAuctionHouseOpType;
(function (EAuctionHouseOpType) {
    /** 上架 */
    EAuctionHouseOpType[EAuctionHouseOpType["LIST"] = 1] = "LIST";
    /** 查看拍卖信息 */
    EAuctionHouseOpType[EAuctionHouseOpType["VIEW_AUCTION_INFO"] = 2] = "VIEW_AUCTION_INFO";
    /** 参与竞拍 */
    EAuctionHouseOpType[EAuctionHouseOpType["JOIN"] = 3] = "JOIN";
    /** 一口价购买 */
    EAuctionHouseOpType[EAuctionHouseOpType["BUY_FIXED"] = 4] = "BUY_FIXED";
    /** 查看我的拍卖品 */
    EAuctionHouseOpType[EAuctionHouseOpType["VIEW_MY_ITEMS"] = 5] = "VIEW_MY_ITEMS";
    /** 查看交易记录 */
    EAuctionHouseOpType[EAuctionHouseOpType["VIEW_LOG"] = 6] = "VIEW_LOG";
    /** 下架 */
    EAuctionHouseOpType[EAuctionHouseOpType["DELIST"] = 7] = "DELIST";
    /** 我的账户查看对应物品池 */
    EAuctionHouseOpType[EAuctionHouseOpType["MY_ITEM_LIST"] = 8] = "MY_ITEM_LIST";
    /** 领取所得物品 */
    EAuctionHouseOpType[EAuctionHouseOpType["GET"] = 9] = "GET";
})(EAuctionHouseOpType || (EAuctionHouseOpType = {}));
/**拍卖分类 */
export var EMainAuctionPageType;
(function (EMainAuctionPageType) {
    /** 材料 */
    EMainAuctionPageType[EMainAuctionPageType["MATERIAL"] = 1] = "MATERIAL";
    /** 秘籍 */
    EMainAuctionPageType[EMainAuctionPageType["MANUAL"] = 2] = "MANUAL";
    /** 神临装备 */
    EMainAuctionPageType[EMainAuctionPageType["GOD_GEAR"] = 3] = "GOD_GEAR";
    /** 领主宝物 */
    EMainAuctionPageType[EMainAuctionPageType["LORD_TREASURE"] = 4] = "LORD_TREASURE";
})(EMainAuctionPageType || (EMainAuctionPageType = {}));
/**我的拍卖分类 */
export var EMyAuctionPageType;
(function (EMyAuctionPageType) {
    /** 竞拍 */
    EMyAuctionPageType[EMyAuctionPageType["BID"] = 1] = "BID";
    /** 上架 */
    EMyAuctionPageType[EMyAuctionPageType["LIST"] = 2] = "LIST";
})(EMyAuctionPageType || (EMyAuctionPageType = {}));
/**我的账户分类 */
export var EMyAccountPageType;
(function (EMyAccountPageType) {
    /** 卖出 */
    EMyAccountPageType[EMyAccountPageType["SELL"] = 1] = "SELL";
    /** 买入 */
    EMyAccountPageType[EMyAccountPageType["BUY"] = 2] = "BUY";
    /** 流拍 */
    EMyAccountPageType[EMyAccountPageType["EXPIRED"] = 3] = "EXPIRED";
})(EMyAccountPageType || (EMyAccountPageType = {}));
export class AuctionHouseDataCenter {
    constructor() {
        this.auctionInfo = new Map();
        this.logsList = new Array();
    }
    static get instance() {
        if (AuctionHouseDataCenter._instance == null) {
            AuctionHouseDataCenter._instance = new AuctionHouseDataCenter();
        }
        return AuctionHouseDataCenter._instance;
    }
    reset() {
        AuctionHouseDataCenter._instance = null;
    }
    setLogsList(logs) {
        this.logsList = logs;
        //排序
        if (this.logsList) {
            this.logsList.sort((t1, t2) => {
                return t2.time - t1.time;
            });
        }
    }
    /** 显示玩家拥有的可交易物品列表*/
    getAllCanAuctionItemList() {
        let list = [];
        // CfgCacheMapMgr.cfg_auction_houseCache.forEach((v, k) => {
        //     let totalGems = GoodsManager.instance.GetGoodsNumByTypeId(v.type_id);
        //     let goodsVo = GoodsVO.GetVoByTypeId(v.type_id, totalGems);
        //     list.push(goodsVo)
        // })
        let allBagList = GoodsManager.instance.GetBagEquipList(0);
        allBagList = allBagList.filter((v, k) => {
            const isCfgItem = CfgCacheMapMgr.cfg_auction_houseCache.has(v.typeId); //配置表有的装备
            if (v.IsDivine) {
                const isActivate = window.DivineDataCenter.instance.isExclusiveDivineEquip(v); //神临装备是否是已成为专属装备
                const isLock = window.DivineDataCenter.instance.isLockDivineEquip(v); //神临装备是否锁定
                return isCfgItem && !isActivate && !isLock;
            }
            else {
                return isCfgItem;
            }
        });
        allBagList = allBagList.sort((a, b) => {
            return a.typeId - b.typeId;
        });
        let idleList = LordTreasureDataCenter.instance.getAllTreasure(); //获得所有的领主宝物（除了模拟对战
        idleList = idleList.sort((a, b) => {
            return a.type_id - b.type_id;
        });
        list = allBagList;
        list = list.concat(idleList);
        return list;
    }
    //------------发送协议------------------
    /**
     *
     * @param OPType 操作类型
     * @param type 页签类型：
** 拍卖分类：1=材料，2=秘籍，3=神临装备，4=领主宝物
** 我的拍卖分类：1=竞拍，2=上架
** 我的账户分类：1=卖出，2=买入，3=流拍
     * @param goods_id 物品id
     * @param bidding_price 竞价价格
     * @param buyout_price 一口价
     * @param num 数量
     * @param lord_treasure_id 领主宝物id
     * @param params 额外参数
     */
    auctionHouseOpTOS(OPType = 0, type = 0, goods_id = 0, bidding_price = 0, buyout_price = 0, num = 0, lord_treasure_id = 0, params = []) {
        let msg = new m_auction_house_op_tos();
        msg.op_type = OPType;
        msg.type = type;
        msg.goods_id = goods_id;
        msg.bidding_price = bidding_price;
        msg.buyout_price = buyout_price;
        msg.num = num;
        msg.lord_treasure_id = lord_treasure_id;
        msg.params = params;
        Connection.instance.sendMessage(msg);
    }
}
AuctionHouseDataCenter._instance = null;
