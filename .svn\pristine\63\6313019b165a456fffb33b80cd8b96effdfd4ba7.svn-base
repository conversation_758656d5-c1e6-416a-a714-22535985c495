{"x": 0, "type": "BaseDialog", "selectedBox": 141, "selecteID": 173, "searchKey": "BaseDialog", "props": {"width": 720, "sceneColor": "#000000", "height": 1280}, "nodeParent": -1, "maxID": 174, "label": "BaseDialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 15, "type": "Box", "searchKey": "Box,mapParent,mapParent", "props": {"y": 0, "x": 0, "width": 720, "var": "mapParent", "name": "mapParent", "height": 1280}, "nodeParent": 2, "label": "mapParent", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 136, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "searchKey": "Image,box,box", "props": {"x": 0, "width": 720, "var": "box", "name": "box", "mouseThrough": true, "height": 101, "bottom": 0}, "nodeParent": 2, "label": "box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 90, "child": [{"x": 30, "type": "Image", "searchKey": "Image,tasksp,tasksp", "props": {"y": -129, "x": 0, "width": 336, "var": "tasksp", "skin": "stagecopy/cszl_16.png", "rotation": 0, "name": "tasksp", "height": 145}, "nodeParent": 90, "label": "tasksp", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 141, "child": [{"x": 45, "type": "Image", "searchKey": "Image,task_status", "props": {"y": 49, "x": 1, "var": "task_status", "skin": "stagecopy/cszl_43.png"}, "nodeParent": 141, "label": "task_status", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 155, "child": []}, {"x": 45, "type": "Label", "searchKey": "Label,lbtotalProgress", "props": {"y": 27, "x": 11, "var": "lbtotalProgress", "text": "今日秘宝任务进度（0/20）", "fontSize": 20, "color": "#ffffff"}, "nodeParent": 141, "label": "lbtotalProgress", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 163, "child": []}, {"x": 45, "type": "Label", "searchKey": "Label,lbtitle", "props": {"y": 60, "x": 36, "var": "lbtitle", "text": "当前任务：", "fontSize": 22, "color": "#ffffff"}, "nodeParent": 141, "label": "lbtitle", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 172, "child": []}, {"x": 45, "type": "Label", "searchKey": "Label,lbDesc", "props": {"y": 94, "x": 10, "width": 266, "var": "lbDesc", "text": "消灭发狂的豺狼人(0/1)", "height": 22, "fontSize": 22, "color": "#ffffff", "align": "center"}, "nodeParent": 141, "label": "lbDesc", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 173, "child": []}, {"x": 45, "type": "Poly", "searchKey": "Poly", "props": {"y": 32, "x": 84.5, "renderType": "hit", "points": "193,-14,195,61,192,96,-78,99,-81,-20", "lineWidth": 1, "lineColor": "#ff0000", "fillColor": "#00ffff"}, "nodeParent": 141, "label": "Poly", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 158, "child": [], "$HIDDEN": true}, {"x": 45, "type": "<PERSON><PERSON>", "searchKey": "Button,gotoBtn", "props": {"y": 45, "x": 227, "var": "gotoBtn", "stateNum": "1", "skin": "worldTreasure/text.png"}, "nodeParent": 141, "label": "gotoBtn", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 164, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,close,closeBtn", "props": {"x": 20, "var": "closeBtn", "stateNum": "1", "skin": "v2_common/btn_back.png", "rotation": 0, "name": "close", "bottom": 19}, "nodeParent": 90, "label": "close", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": [], "$LOCKED": false}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "Button,treasureMapBtn,treasureMapBtn", "props": {"y": -241, "x": 637, "var": "treasureMapBtn", "stateNum": "1", "skin": "worldTreasure/shijiemibao_8.png", "name": "treasureMapBtn"}, "nodeParent": 90, "label": "treasureMapBtn", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 162, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "searchKey": "<PERSON><PERSON>,helpbtn,helpbtn", "props": {"y": -325, "x": 637, "var": "helpbtn", "stateNum": "1", "skin": "worldTreasure/ic_help.png", "name": "helpbtn", "label": "1049"}, "nodeParent": 90, "label": "helpbtn", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 171, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Box", "searchKey": "Box,locationBox,locationBox", "props": {"y": 841, "x": 367, "width": 97, "visible": false, "var": "locationBox", "name": "locationBox", "height": 97, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "locationBox", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 160, "child": [{"type": "Image", "searchKey": "Image,imgLocation,imgLocation", "props": {"y": -153, "x": 41, "var": "imgLocation", "skin": "worldTreasure/arrow.png", "rotation": 0, "name": "imgLocation", "centerY": 0, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 160, "label": "imgLocation", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 161, "child": []}, {"type": "Image", "searchKey": "Image,imgIcon", "props": {"y": 18, "width": 120, "var": "imgIcon", "scaleY": 0.5, "scaleX": 0.5, "height": 120, "centerX": 0}, "nodeParent": 160, "label": "imgIcon", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 165, "child": [{"type": "Sprite", "searchKey": "Sprite", "props": {"y": 0, "x": 0, "width": 120, "renderType": "mask", "height": 120}, "nodeParent": 165, "label": "Sprite", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 170, "child": [{"type": "Circle", "searchKey": "Circle", "props": {"y": 60, "x": 60, "radius": 60, "lineWidth": 1, "fillColor": "#ff0000"}, "nodeParent": 170, "label": "Circle", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 169, "child": []}]}], "$HIDDEN": false}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}], "$LOCKED": true}