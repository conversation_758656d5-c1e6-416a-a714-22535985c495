{"x": 0, "type": "BaseDialog", "selectedBox": 2, "selecteID": 6, "searchKey": "BaseDialog", "referenceLines": [{"value": 213, "type": 0, "id": 1}], "props": {"width": 617, "sceneColor": "#a6a6a6", "height": 430}, "nodeParent": -1, "maxID": 92, "label": "BaseDialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 15, "type": "UIView", "source": "res/base/TopInsAuto.scene", "searchKey": "UIView,topIns", "props": {"y": 24, "x": -6, "width": 627, "var": "topIns", "height": 415}, "nodeParent": 2, "label": "topIns", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"x": 15, "type": "Image", "searchKey": "Image", "props": {"y": 83, "x": 32, "width": 551, "skin": "v2_common/sanji_2.png", "sizeGrid": "12,12,12,12", "height": 241}, "nodeParent": 2, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 73, "child": [], "$HIDDEN": false}, {"x": 15, "type": "UIView", "source": "res/base/GoodsSmallItem.scene", "searchKey": "UIView,goodsItem", "props": {"y": 120, "x": 98, "var": "goodsItem"}, "nodeParent": 2, "label": "goodsItem", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Label", "searchKey": "Label,lbName", "props": {"y": 115, "x": 237.5, "var": "lbName", "text": "名字名字名字名字", "fontSize": 24, "color": "#855033"}, "nodeParent": 2, "label": "lbName", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "searchKey": "Button,btnGet", "props": {"y": 341, "x": 211.5, "var": "btnGet", "skin": "v2_common/btn_yellow.png", "label": "领取"}, "nodeParent": 2, "label": "btnGet", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 48, "child": []}, {"x": 15, "type": "Box", "searchKey": "Box,butoutBox", "props": {"y": 242.5, "x": 295, "var": "butoutBox"}, "nodeParent": 2, "label": "butoutBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 75, "child": [{"x": 30, "type": "Label", "searchKey": "Label,lbFixedPrice", "props": {"y": -62, "x": -56, "width": 92, "var": "lbFixedPrice", "valign": "middle", "text": "一口价：", "height": 22, "fontSize": 24, "color": "#855033", "align": "left"}, "nodeParent": 75, "label": "lbFixedPrice", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 77, "child": []}, {"x": 30, "type": "Label", "searchKey": "Label,inputFixedPrice", "props": {"y": -69, "x": 112, "width": 164, "var": "inputFixedPrice", "valign": "middle", "type": "number", "text": "1", "sizeGrid": "0,15,0,15", "promptColor": "#ffffea", "padding": "0,0,0,0", "height": 36, "fontSize": 26, "color": "#855033", "align": "left"}, "nodeParent": 75, "label": "inputFixedPrice", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 78, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "searchKey": "Image,icon1", "props": {"y": -73, "x": 72, "width": 120, "var": "icon1", "skin": "common3/gold.png", "scaleY": 0.32, "scaleX": 0.32, "rotation": 0, "height": 120}, "nodeParent": 75, "label": "icon1", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 79, "child": []}]}, {"x": 15, "type": "Box", "searchKey": "Box,biddingBox", "props": {"y": 178, "x": 295, "var": "biddingBox"}, "nodeParent": 2, "label": "biddingBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 76, "child": [{"x": 30, "type": "Label", "searchKey": "Label,lbNowPrice", "props": {"y": -29, "x": -58, "width": 118, "var": "lbNowPrice", "valign": "middle", "text": "出售价格：", "height": 22, "fontSize": 24, "color": "#855033", "align": "left"}, "nodeParent": 76, "label": "lbNowPrice", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 80, "child": []}, {"x": 30, "type": "Label", "searchKey": "Label,inputBidPrice", "props": {"y": -35, "x": 112, "width": 164, "var": "inputBidPrice", "valign": "middle", "type": "number", "text": "1", "sizeGrid": "0,15,0,15", "promptColor": "#ffffea", "padding": "0,0,0,0", "height": 36, "fontSize": 24, "color": "#855033", "align": "left"}, "nodeParent": 76, "label": "inputBidPrice", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 81, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "searchKey": "Image,icon2", "props": {"y": -40, "x": 72, "width": 120, "var": "icon2", "skin": "common3/gold.png", "scaleY": 0.32, "scaleX": 0.32, "height": 120}, "nodeParent": 76, "label": "icon2", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 82, "child": []}]}, {"x": 15, "type": "Box", "searchKey": "Box,boxTipsPrice", "props": {"y": 242, "x": 295, "var": "boxTipsPrice"}, "nodeParent": 2, "label": "boxTipsPrice", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 83, "child": [{"x": 30, "type": "Label", "searchKey": "Label,lbTipsPrice", "props": {"y": -29, "x": -58, "width": 118, "var": "lbTipsPrice", "valign": "middle", "text": "扣除手续费：", "height": 22, "fontSize": 24, "color": "#855033", "align": "left"}, "nodeParent": 83, "label": "lbTipsPrice", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 84, "child": []}, {"x": 30, "type": "Label", "searchKey": "Label,lbTipsPriceNum", "props": {"y": -35, "x": 112, "width": 164, "var": "lbTipsPriceNum", "valign": "middle", "type": "number", "text": "1", "sizeGrid": "0,15,0,15", "promptColor": "#ffffea", "padding": "0,0,0,0", "height": 36, "fontSize": 24, "color": "#855033", "align": "left"}, "nodeParent": 83, "label": "lbTipsPriceNum", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 85, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "searchKey": "Image,icon3", "props": {"y": -40, "x": 72, "width": 120, "var": "icon3", "skin": "common3/gold.png", "scaleY": 0.32, "scaleX": 0.32, "height": 120}, "nodeParent": 83, "label": "icon3", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 86, "child": []}]}, {"x": 15, "type": "Box", "searchKey": "Box,boxFinal", "props": {"y": 312, "x": 251.5, "var": "boxFinal"}, "nodeParent": 2, "label": "boxFinal", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 87, "child": [{"x": 30, "type": "Label", "searchKey": "Label,lbFinalGet", "props": {"y": -29, "x": -58, "width": 118, "var": "lbFinalGet", "valign": "middle", "text": "最终获得：", "height": 22, "fontSize": 24, "color": "#855033", "align": "left"}, "nodeParent": 87, "label": "lbFinalGet", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 88, "child": []}, {"x": 30, "type": "Label", "searchKey": "Label,lbFinalGetNum", "props": {"y": -35, "x": 89, "width": 164, "var": "lbFinalGetNum", "valign": "middle", "type": "number", "text": "1", "sizeGrid": "0,15,0,15", "promptColor": "#ffffea", "padding": "0,0,0,0", "height": 36, "fontSize": 24, "color": "#855033", "align": "left"}, "nodeParent": 87, "label": "lbFinalGetNum", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 89, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "searchKey": "Image,icon4", "props": {"y": -40, "x": 49, "width": 120, "var": "icon4", "skin": "common3/gold.png", "scaleY": 0.32, "scaleX": 0.32, "height": 120}, "nodeParent": 87, "label": "icon4", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 90, "child": []}]}, {"x": 15, "type": "Label", "searchKey": "Label,lbTip", "props": {"y": 280, "x": 73.5, "width": 468, "visible": false, "var": "lbTip", "valign": "middle", "text": "物品已流拍，将返还物品和押金", "height": 22, "fontSize": 24, "color": "#e00000", "align": "center"}, "nodeParent": 2, "label": "lbTip", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 91, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}