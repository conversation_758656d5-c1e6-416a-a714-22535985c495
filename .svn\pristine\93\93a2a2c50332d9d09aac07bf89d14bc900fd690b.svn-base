import {Event} from "laya/events/Event";
import {Panel} from "laya/ui/Panel";
import {Handler} from "laya/utils/Handler";
import {GlobalConfig} from "../../../../game/GlobalConfig";
import {MiscConstAuto} from "../../../auto/MiscConstAuto";
import {CfgCacheMapMgr} from "../../../cfg/CfgCacheMapMgr";
import {p_fight_actor} from "../../../proto/common/p_fight_actor";
import {p_fight_hurt} from "../../../proto/common/p_fight_hurt";
import {p_step_skill} from "../../../proto/common/p_step_skill";
import {p_td_hero_fight} from "../../../proto/common/p_td_hero_fight";
import {ActorType, RoleCamp, SceneType} from "../../../scene2d/SceneConst";
import {com} from "../../../ui/layaMaxUI";
import {StringUtil} from "../../../util/StringUtil";
import {TipsUtil} from "../../../util/TipsUtil";
import {BaseDialog, DialogNavShow} from "../../BaseDialog";
import {FightStartAnimation} from "../../fight/view/FightStartAnimation";
import {PHero} from "../../hero/data/HeroConsts";
import {HeroDataCenter} from "../../hero/data/HeroDataCenter";
import {MiscConst} from "../../misc_config/MiscConst";
import {ModuleCommand} from "../../ModuleCommand";

import {Laya} from "Laya";
import {Point} from "laya/maths/Point";
import {Dialog} from "laya/ui/Dialog";
import {cfg_main_battle} from "../../../cfg/vo/cfg_main_battle";
import {cfg_td_main} from "../../../cfg/vo/cfg_td_main";
import {cfg_td_main_monster} from "../../../cfg/vo/cfg_td_main_monster";
import {ConfigManager} from "../../../managers/ConfigManager";
import {DispatchManager} from "../../../managers/DispatchManager";
import {GameSoundManager} from "../../../managers/GameSoundManager";
import {LayerManager} from "../../../managers/LayerManager";
import {p_first_pay_info} from "../../../proto/common/p_first_pay_info";
import {p_hero} from "../../../proto/common/p_hero";
import {p_simp_mission} from "../../../proto/common/p_simp_mission";
import {m_fight_simp_result_toc} from "../../../proto/line/m_fight_simp_result_toc";
import {ArrayUtil} from "../../../util/ArrayUtil";
import {ColorUtil} from "../../../util/ColorUtil";
import {DateUtil} from "../../../util/DateUtil";
import {GameUtil} from "../../../util/GameUtil";
import {MusicUtil} from "../../../util/MusicUtil";
import {TdUtil} from "../../../util/TdUtil";
import {UIUtil} from "../../../util/UIUtil";
import {FingerMoveScript} from "../../baseModules/scripts/FingerMoveScript";
import {GSkeleton} from "../../baseModules/skeleton/GSkeleton";
import {ESkeletonType} from "../../baseModules/skeleton/SkeletonData";
import {SkeletonManager} from "../../baseModules/skeleton/SkeletonManager";
import {UIHTMLDiv} from "../../baseModules/UIHTMLDiv";
import {DataCenter} from "../../DataCenter";
import {GameConst} from "../../GameConst";
import GoodsItem from "../../goods/GoodsItem";
import {GoodsVO} from "../../goods/GoodsVO";
import {GuaJiDataCenter} from "../../guaJi/data/GuaJiDataCenter";
import {GuideConst} from "../../guide/GuideConst";
import {GuideMgr} from "../../guide/GuideMgr";
import {LineUpDataCenter} from "../../lineUp/data/LineUpDataCenter";
import {LordDataCenter} from "../../Lord/data/LordDataCenter";
import MainTopIconLayer2 from "../../menu/view/MainTopIconLayer2";
import {MissionStatus} from "../../mission/MissionConst";
import {OnlineRewardDataCenter} from "../../onlineReward/data/OnlineRewardDataCenter";
import {MainIconOnlineRewardView} from "../../onlineReward/view/MainIconOnlineRewardView";
import {PanelEventConstants} from "../../PanelEventConstants";
import VipTeQuanUtil from "../../payment/VipTeQuanUtil";
import {PaymentVO} from "../../payment/vo/PaymentVO";
import {RechargeDataCenter} from "../../recharge/data/RechargeDataCenter";
import {RedPointMgr} from "../../redPoint/RedPointMgr";
import RedPointVo from "../../redPoint/RedPointVo";
import {SettingDataCenter} from "../../setting/data/SettingDataCenter";
import {TdEndType, TdMonsterType} from "../../tdTrial/dialog/TdTrialGameDialog";
import {TDRoleBase} from "../../tdTrial/game/actor/TDRoleBase";
import {TDRoleHero} from "../../tdTrial/game/actor/TDRoleHero";
import {TDRoleLord} from "../../tdTrial/game/actor/TDRoleLord";
import {TDRoleMonster} from "../../tdTrial/game/actor/TDRoleMonster";
import {TDFightHandler} from "../../tdTrial/game/data/TDFightHandler";
import {ETdMainOpType} from "../../tdTrial/game/ITdDataCenter";
import {TDSceneElementMgr} from "../../tdTrial/game/TDSceneElementMgr";
import {ETDPathPointType, TDPathPointVo} from "../../tdTrial/game/vo/TDPathPointVo";
import {YueKaDataCenter} from "../../welfare/data/YueKaDataCenter";
import {ETdMainGuideStep, TdMainDataCenter, TdMainFightOpType, TdMainLineUpOpType} from "../data/TdMainDataCenter";
import {TdMainFingerMoveScript} from "../scripts/TdMainFingerMoveScript";
import {TdMainGuajiScript} from "../scripts/TdMainGuajiScript";
import {TdMainMissionGuideScript} from "../scripts/TdMainMissionGuideScript";
import {TdMainMissionLotteryScript} from "../scripts/TdMainMissionLotteryScript";
import {TdMainNewHeroScript} from "../scripts/TdMainNewHeroScript";
import {TdMainLeftIconLayer2} from "../view/TdMainLeftIconLayer2";
import TdMainRightBottomIconLayer from "../view/TdMainRightBottomIconLayer";
import {TdNulltem} from "../view/TdNulltem";
import {ConsoleUtils} from "../../../util/ConsoleUtils";
import {TdTrialIcon} from "../../tdTrial/view/TdTrialIcon";
import MenuActivityIconView from "../../menu/view/MenuActivityIconView";
import {cfg_activity_icon} from "../../../cfg/vo/cfg_activity_icon";
import {MainIconConfig} from "../../menu/MainIconConfig";
import {TdMainLordTalk} from "../scripts/TdMainLordTalk";

export class TdMainDialog extends com.ui.res.tdMain.TdMainDialogUI {

    private static menuExpandState: Map<string, boolean> = new Map();


    private tdIcon: TdTrialIcon;

    public elementMgr: TDSceneElementMgr;
    public matchType = GameConst.MATCH_TYPE_TD_MAIN;

    private skBossTips: GSkeleton;

    private _roleDataList: p_fight_actor[];
    private _monsterDataList: p_fight_actor[];
    /**路点 */
    public _pathPointMap: Map<number, TDPathPointVo[]> = new Map<number, TDPathPointVo[]>();

    private mapPanel: Panel;

    private nowshowwave: number = 1;

    private _isAutoFight: boolean = false;

    private _isGameFighting: boolean = false; //是否正在进行战斗中
    public get isGameFighting(): boolean {
        return this._isGameFighting;
    }

    public set isGameFighting(value: boolean) {
        this._isGameFighting = value;
        TdMainDataCenter.ins.isGameFighting = value;
    }

    private nowWaveCfg: cfg_td_main_monster;
    private nowPassCfg: cfg_td_main;

    public monsterMoveList: TDRoleMonster[] = [];
    public monsterMoveTime: number = 0;

    public nowPass: number = 0
    public updateCountdown: number = 0;

    private nullShowMap: Map<number, TdNulltem> = new Map<number, TdNulltem>();

    private onlineRewardView: MainIconOnlineRewardView;
    public leftIconLayer: TdMainLeftIconLayer2;
    private rightBottomIconLayer: TdMainRightBottomIconLayer;
    private rightTopIconLayer: MainTopIconLayer2;
    // private uiProgress: UIProgressBarEx;
    private itemMission: GoodsItem;

    private skGuajiReward: GSkeleton;

    //宝箱
    private skBox: GSkeleton;

    // public isFingerGuide: boolean = true;

    /**非战斗挂机脚本 */
    public guajiScript: TdMainGuajiScript;
    /**任务引导脚本 */
    public missionGuideScript: TdMainMissionGuideScript;
    /**手指滑动脚本 */
    public fingerMoveScript: TdMainFingerMoveScript;
    /**千抽动画脚本 */
    public missionLotteryScript: TdMainMissionLotteryScript;
    /** 新英雄登场引导脚本 */
    public newHeroScript: TdMainNewHeroScript;
    /** 战斗失败的提示 */
    private tdLordTalk: TdMainLordTalk;

    /**通关奖励按钮 */
    public btnPassReward: MenuActivityIconView;
    private showDoorEffectList: GSkeleton[] = [];
    public get isNewHeroAppearFinish() {
        let isFinish = TdMainDataCenter.ins.isNewHeroAppearFinish;
        return isFinish;
    }

    public get isFirstGuideAllFinish() {
        let isFinish = TdMainDataCenter.ins.isFirstGuideAllFinish;
        return isFinish;
    }

    /** 毫秒 */
    private get startTime(): number {
        return this.elementMgr.fightStartTime;
    }

    private get now(): number {
        return this.elementMgr.now;
    }

    /** 毫秒 */
    private get elapsedTime(): number {
        return this.elementMgr.fightElapsedTime;
    }

    /**毫秒 */
    private get countdown(): number {
        return this.nowPassCfg.limit_time * 1000 - this.elapsedTime;
    }

    public get lord() {
        let lord: TDRoleLord = null;
        if (this.elementMgr) {
            let heroList = this.elementMgr.findRoleByCamp(RoleCamp.OUR);
            heroList.some(hero => {
                if (hero.actorType == ActorType.Lord) {
                    lord = hero as TDRoleLord;
                    return true;
                }
            })
        }

        if (!lord || lord.destroyed) {
            return null;
        } else {
            return lord;
        }
    }

    // public get tempLineUpList(): number[] {
    //     return TdMainDataCenter.ins.tempLineUpList;
    // }
    // public set tempLineUpList(value: number[]) {
    //     TdMainDataCenter.ins.tempLineUpList = value;
    // }
    public get currLineUpList(): number[] {
        return TdMainDataCenter.ins.currLineUpList.concat([]);
    }

    constructor(param: any = null) {
        super();
        this.navShow = DialogNavShow.BOTH;
        this.width = this.relativeWidth;
        this.isFullScreen = true;
        this.isShowDebugTitle = false;
        // this["_hold_"] = true;
        // this.isSingle = true;

    }

    get width(): number {
        return super.width;
    }

    set width(value: number) {
        super.width = this.relativeWidth;
    }

    get isBackMode(): boolean {
        return TdMainDataCenter.ins.isBackMode;
    }

    set isBackMode(value: boolean) {
        this.isSingle = value;
        this["_hold_"] = value;
        TdMainDataCenter.ins.isBackMode = value;
    }

    initData() {
        super.initData(null);
    }

    initUI(): void {

        UIHTMLDiv.SetUIHTMLDiv(this.levelTxt, 20, "#ffffce");
        UIHTMLDiv.SetUIHTMLDiv(this.remainnum, 20, "#ffffce");

        UIHTMLDiv.SetUIHTMLDiv(this.htmlMissionDesc, 20, ColorUtil.GOLD_COLOR);
        UIHTMLDiv.setAlignCenter(this.htmlMissionDesc);

        UIHTMLDiv.SetUIHTMLDiv(this.htmlMissionProgress, 20, ColorUtil.GOLD_COLOR);
        UIHTMLDiv.setAlignCenter(this.htmlMissionProgress);

        // UIHTMLDiv.SetUIHTMLDiv(this.htmlGuideFinger, 20, ColorUtil.FONT_NORAML, 5);
        // this.htmlGuideFinger.innerHTML = window.iLang.L2_AN_ZHU_HERO_TUO_DONG_ch31_KE_JIN_XING_ZHAN_WEI_TIAO_ZHENG_O_ch01.il();
        // UIHTMLDiv.setAlignCenterAndValignMid(this.htmlGuideFinger);
        // this.boxGuideFinger.visible = false;

        // this.boxGuideFinger.mouseEnabled = false;

        this.y = 0;
        this.height = this.relativeHeight;
        this.mouseThrough = false;

        this.topbox.top = DataCenter.excursion_y + 170;
        this.topbox.mouseThrough = true;
        this.addChild(this.btnBack);
        this.btnBack.visible = false;
        this.btnQuickFight.visible = GlobalConfig.showRecharge;

        this.boxLeftTop.mouseThrough = true;

        let eff_lotter = SkeletonManager.ins.createSkeleton("zhengzhan_recruit", ESkeletonType.UI_EFFECT, {isLoop: true});
        eff_lotter.pos(this.lotterbtn.width / 2, this.lotterbtn.height / 2);
        this.lotterbtn.addChild(eff_lotter);
    }

    private debugTest() {
        if (GlobalConfig.IsDebug == false) {
            return;
        }
        // if (GuideMgr.ins.finishMissions.indexOf(GuideConst.growId) == -1){
        //     GuideMgr.ins.setGranchGuideId(GuideConst.growId);
        // }

        // let colIndex = 0;
        // let rowIndex = 0;
        // for(let i = 0; i < 10; i++){
        // 	// let show3d = new W73DHeroView_Battle(1024, 1, false);
        // 	let show3d = SkeletonManager.ins.createSkeleton("H205_jiansheng_241214_153945", ESkeletonType.EFFECT);
        // 	// show3d.loadHero(1132014);
        // 	show3d.x = 100 + 80 * colIndex;
        // 	show3d.y = 200 + 100 * rowIndex;
        // 	show3d.playAction("idle");

        // 	if(show3d.y > 1000){
        // 		colIndex++;
        // 		rowIndex = 0;
        // 	}else{
        // 		rowIndex++;
        // 	}

        // 	// show3d.sp3Scale(0.3)
        // 	this.addChild(show3d);


        // }

        // let sk = SkeletonManager.ins.createSkeleton("H205_jiansheng_241214_153431", ESkeletonType.EFFECT);
        // let sk = SkeletonManager.ins.createSkeleton("08_H208_niutourenqiuzhang", ESkeletonType.EFFECT);
        // sk.playNameOrIndex(2);
        // this.addChild(sk);
        // sk.x = 300;
        // sk.y = 600;

        this.tdIcon = new TdTrialIcon();
        this.tdIcon.name = "tdIcon";
        this.tdIcon.pos(660, this.relativeHeight - 270);
        this.tdIcon.open();
        this.tdIcon.scale(0.5, 0.5);
        this.addChild(this.tdIcon);
    }

    onOpen() {

        this.debugTest();

        this.callLater(this.readyGame);

        this.checkOpenState();

        //测试用
        // if (GlobalConfig.is_LOCAL_DEBUG) {
        //     TdMainDataCenter.ins.newGuideStep = ETdMainGuideStep.start;
        // }

        //容错外服老号
        if (HeroDataCenter.instance.hero_list.length < 3) {
            TdMainDataCenter.ins.newGuideStep = ETdMainGuideStep.allFinish;
        }

        if (this.isFirstGuideAllFinish == false) {
            TdMainDataCenter.ins.newGuideStep = ETdMainGuideStep.start;
        } else {
            //判断千抽引导是否完成
            let isFinish = GuideMgr.ins.isFinishMission(GuideConst.missionLotteryReward);
            if (isFinish == false) {
                this.dispatchEvent(ModuleCommand.OPEN_MISSION_LOTTRY_DIALOG);
            }
        }

        let isNewHeroGuide = this.isFirstGuideAllFinish == false;
        if (isNewHeroGuide) {
            this.newHeroScript = this.addComponent(TdMainNewHeroScript);
            this.newHeroScript.isNewHeroGuide = true;
            this.dispatchEvent(ModuleCommand.OPEN_MISSION_LOTTRY_PREVIEW_DIALOG);
        } else {
            this.tdLordTalk = new TdMainLordTalk(this);
        }

        this.missionLotteryScript = this.addComponent(TdMainMissionLotteryScript);
        this.missionGuideScript = this.addComponent(TdMainMissionGuideScript);
        this.guajiScript = this.addComponent(TdMainGuajiScript);

        this.onBackModeChange();
        this.updateMissionProgress();
        this.showGameInfo(false);
        this.showRedPoint();
        if (GlobalConfig.is_majia_shenhe == false) {
            TdMainDataCenter.ins.m_td_main_fight_tos(TdMainFightOpType.GET_INFO);
        }
    }

    addClick(): void {
        this.addOnClick(this, this.startgamebtn, this.onStartGameClick);
        this.addOnClick(this, this.stopgamebtn, this.onStopGameClick);
        this.addOnClick(this, this.ratebtn, this.onClickRate);
        this.addOnClick(this, this.btnBack, this.onClickBack);
        this.addOnClick(this, this.btnQuickFight, this.onClickQuickFight);
        this.addOnClick(this, this.btnBox, this.onClickMainBattleBox);
        this.addOnClick(this, this.lotterbtn, this.onClickLotteryBtn);
        this.addOnClick(this, this.scaleBtn, this.onClickSetScale);

        if (GlobalConfig.is_LOCAL_DEBUG) {
            this.cbFightMode.visible = true;
            this.cbFightMode.labels = window.iLang.L2_QIAN_TAI_MO_SHI_ch32_MO_NI_HOU_TAI.il();
            this.cbFightMode.selectedIndex = 0;
            this.cbFightMode.on(Event.CHANGE, this, this.onClickFightMode);

        } else {
            this.cbFightMode.visible = false;
        }
        //this.cbFightMode.visible = false;

        this.addOnClick(this, this.boxMission, this.onClickMission);
        this.addOnClick(this, this.onekeyBtn, this.onOneKeyClick);
    }


    addEvent() {
        this.addEventListener(ModuleCommand.OPEN_TD_MAIN_START_FIGHT, this, this.startGame);
        this.addEventListener(ModuleCommand.OPEN_TD_MAIN_INFO_UPDATE, this, this.readyGame);
        // this.addEventListener(ModuleCommand.TD_NEXT_FIGHT, this, this.onStartGameClick);
        this.addEventListener(ModuleCommand.TD_POS_CHANGE, this, this.onHeroPosChange);
        this.addEventListener(ModuleCommand.UPDATE_GUA_JI_INFO, this, this.updateGuaJiInfo);
        this.addEventListener(ModuleCommand.TD_MAIN_NULL_CLICK, this, this.nullItemClick);

        this.addEventListener(ModuleCommand.TD_OUT_LINEUP, this, this.onOutLineUp);
        this.addEventListener(ModuleCommand.HERO_INFO_DIALOG_CLOSE, this, this.onCloseHeroInfoDialog);
        this.addEventListener(ModuleCommand.LEVEL_UP, this, this.onLevelUp);
        this.addEventListener(ModuleCommand.UPDATE_XUNZHANG_DIALOG, this, this.onLevelUp);
        this.addEventListener(ModuleCommand.UPDATE_MAIN_BATTLE_INFO, this, this.updateMainBattleInfo);
        // this.addEventListener(ModuleCommand.UPDATE_TRIAL_INFO, this, this.onLevelUp);
        this.addEventListener(ModuleCommand.TD_MAIN_LINE_UP_UPDATE, this, this.createOrRefreshRole);

        this.addEventListener(ModuleCommand.TD_MAIN_MISSION_UPDATE, this, this.updateMissionProgress);
        this.addEventListener(ModuleCommand.UPDATE_FIGHT_SIMP_RESULT, this, this.onFightResult);

        this.addEventListener(ModuleCommand.ON_CLOSE_MISSION_LOTTRY_PREVIEW_DIALOG, this, this.onCloseMissionLotteryPreviewDialog);
        this.addEventListener(ModuleCommand.RED_CHILD_CHANGE, this, this.refreshRedPoint);

        this.addEventListener(ModuleCommand.UPDATE_LORD_INFO, this, this.onUpdateLordInfo);
        this.addEventListener(ModuleCommand.ON_TD_FIGHT_GET_INFO, this, this.onFightGetInfo);
        this.addEventListener(ModuleCommand.ON_CLOSE_LORD_LINE_UP_DIALOG, this, this.onCloseLordDialog);
        this.addEventListener(ModuleCommand.ON_CLOSE_ROLE_INFO_DIALOG, this, this.onCloseLordDialog);

        // this.addEventListener(ModuleCommand.GUIDE_END_ID, this, this.onGuideEnd);
        // this.addEventListener(ModuleCommand.ON_CLOSE_TD_GUIDE_NEW_HERO_DIALOG, this, this.onCloseNewHeroDialog);

        this.addEventListener(ModuleCommand.ON_OPEN_OR_CLOSE_DIALOG, this, this.onOpenOrCloseDialog);
        this.addEventListener(ModuleCommand.UPDATE_MAIN_BATTLE_MISSION_STATUS, this, this.refBtnPassReward);
    }

    private onUpdateLordInfo(): void {
        if (this.isGameFighting == false) {
            this.doForceLineUp();
        }
    }

    private onCloseLordDialog(data) {
        if (!data || data.matchType == this.matchType) {
            //领主技能更新
            TdMainDataCenter.ins.m_td_main_fight_tos(TdMainFightOpType.GET_INFO);
        }
    }

    private onCloseMissionLotteryPreviewDialog(pos: Point): void {
        //自动开始战斗
        this.onStartGameClick();
        this.missionLotteryScript.createFlyItem(pos);
    }

    private onLevelUp(): void {
        this.checkOpenState();
    }

    private onCloseHeroInfoDialog(): void {
        TdMainDataCenter.ins.m_td_main_fight_tos(TdMainFightOpType.GET_INFO);

        // this.refreshRoleShowInfo();
    }


    private updateAllPoint(): void {
        let dataCenter = GuaJiDataCenter.instance;
        this.SetRedPoint(this.lbBoxNum, false, 70, -20);
        if (dataCenter.show_box_red_point) {
            dataCenter.show_box_red_point = false;
            this.SetRedPoint(this.lbBoxNum, dataCenter.getGuajiBoxCount() > 0, 70, -20);
        }
    }

    private checkRateBtn(pass: number = 0): void {
        let maxSpeedRate = TdMainDataCenter.ins.maxSpeedRate;
        if (maxSpeedRate > 1) {
            let currPass = pass || TdMainDataCenter.ins.currPass;
            this.ratebtn.visible = currPass >= MiscConstAuto.td_main_fight_speed_open || GlobalConfig.is_LOCAL_DEBUG;
            this.ratebtn.label = "x" + TdMainDataCenter.ins.speedRate;
        } else {
            this.ratebtn.visible = false;
        }
    }

    private checkOpenState(): void {
        this.leftIconLayer?.updateView();
        this.rightBottomIconLayer?.updateView();
        this.rightTopIconLayer?.updateView();
        this.refBtnPassReward();
        this.checkRateBtn();
        this.UpdateShowRechargeState();
    }

    private checkFirstGuideStep(): void {
        // let isGuide = this.isNewRoleGuide;
        let isNewHeroFinish = this.isNewHeroAppearFinish;
        let isAllFinish = this.isFirstGuideAllFinish;

        this.showBoxMission();
        // this.btnQuickFight.visible = isAllFinish;
        this.bottombox.visible = isAllFinish;
        this.lotterbtn.visible = isAllFinish;
        this.boxLeftTop.visible = this.missionLotteryScript.isShowLeftIcon ? true : isAllFinish;
        if (this.btnPassReward) {
            this.btnPassReward.visible = isAllFinish;
        }

        if (this.rightBottomIconLayer) {
            this.rightBottomIconLayer.visible = isAllFinish;
        }

        // this.ratebtn.visible = isAllFinish || GlobalConfig.IsDebug;
        if (this.onlineRewardView) {
            this.onlineRewardView.visible = isAllFinish;
        }
        if (this.leftIconLayer) {
            this.leftIconLayer.visible = this.missionLotteryScript.isShowLeftIcon ? true : isAllFinish;
        }

        if (this.rightTopIconLayer) {
            this.rightTopIconLayer.visible = isAllFinish;
        }

        if (this.newHeroScript) {
            this.newHeroScript.checkFirstGuideStep();
        }
    }

    private onGuideAutoLineUp() {

        if (this.isNewHeroAppearFinish) {
            return;
        }

        if (this.newHeroScript) {
            this.newHeroScript.onGuideAutoLineUp();
        }

        this.checkFirstGuideStep();
    }

    UpdateShowRechargeState(): void {
        if (this.rightTopIconLayer) {
            this.rightTopIconLayer.visible = GlobalConfig.showRecharge;
        }
    }

    public onClose(): void {
        // this.onFront();
        super.onClose();

        let isBackMode = this.isBackMode;
        this.isBackMode = false;
        if (isBackMode == false) {
        }
    }

    onDestroy() {
        super.onDestroy();

        if (this.elementMgr) {
            this.elementMgr.destroy();
        }

        let curHide = GuideMgr.ins.getIsHideGuideUI(this.name);
        if (curHide == true) {
            GuideMgr.ins.setHideGuideUI(false, this.name);
        }
        TdMainDataCenter.ins.clearData(true);
    }

    //-----------------比赛流程--------------------/
    /** 准备阶段 主要是创建角色和界面显示更新 */
    private readyGame() {
        if (TdMainDataCenter.ins.tdMainInfo && TdMainDataCenter.ins.tdMainInfo.lineup) {
            if (this.nowPass != TdMainDataCenter.ins.nextPass) {
                this.nowPass = TdMainDataCenter.ins.nextPass;
                this.nowPassCfg = CfgCacheMapMgr.cfg_td_mainCache.get(this.nowPass);
                this.initScene();
                this.initGameShow();
                this.createOrRefreshRole();
            }

            this.guajiScript.showFreeWave();
        }

        this.setSpeedRate(TdMainDataCenter.ins.speedRate);
    }

    private startGame() {
        this.guajiScript.unShowFreeWave();
        this.timer.clear(this, this.delayAutoFight);

        // this.roleInitSkill();

        this.showGameInfo(true);
        // this.onekeyBtn.visible = false;
        this.startgamebtn.visible = false;
        this.stopgamebtn.visible = this.nowPass >= MiscConstAuto.td_main_exit_limit;
        this.isGameFighting = true;
        this.nowshowwave = 0;

        this.updateCountdown = 0;

        this.passShow();
        this.nextWave();

        this.timer.frameLoop(1, this, this.onTimer100);

        this.timer.clear(this, this.delayClear);
        this.elementMgr.startTdFight();
        this.elementMgr.setPlaySpeed(TdMainDataCenter.ins.speedRate, true);

        this.startGameShow();

        //测试用
        // if (GlobalConfig.is_LOCAL_DEBUG == false && this.nowPass != 1) {
        // // if (this.nowPass != 1) {
        //     TdMainDataCenter.ins.newGuideStep = ETdMainGuideStep.finish;
        // }
        this.checkFirstGuideStep();

        if (!TdMainDataCenter.ins.isFightWin) {
            this.callLater(this.showLordFailTalk);
        }

    }

    private nextWave() {
        this.nowshowwave++;
        this.nowWaveCfg = CfgCacheMapMgr.cfg_td_main_monsterCache.m_get(this.nowPass, this.nowshowwave);
        if (this.nowWaveCfg) {
            this.oneWave();
        }

        this.monsterMoveTime = 0;

        if (this.nowPass == 2 && this.nowshowwave <= 3) {
            this.fingerMoveScript = this.getOrAddComponent(TdMainFingerMoveScript);
            this.fingerMoveScript.onFingerGuideStart();
        } else {
            // this.onFingerGuideEnd();
        }
    }

    private oneWave() {
        let monsterList = this.createMonster();
        let hasBoss = false;
        monsterList.some(monster => {
            if (monster.actorType == ActorType.TDBoss) {
                hasBoss = true;
                return true;
            }
        })

        this.monsterPathPoint(monsterList);
        this.waveShow();
    }

    /**
     *
     * @param endType 结束类型 1正常结束 2 失败结束
     */
    private endGame(endType: number) {
        TdMainDataCenter.ins.m_td_main_op_tos(ETdMainOpType.END_FIGHT, [endType, this._isAutoFight ? 1 : 0]);
        this.timer.clear(this, this.onTimer100);
        // this.timer.clear(this, this.onTimer1000);
        this.monsterMoveList.length = 0;

        this.endGameShow(endType);

        let isWin = endType == TdEndType.WIN;
        let delayClearSec = 2;
        let delayNext = Math.max(MiscConst.battle_success_close_tick, delayClearSec) * 1000;
        if (isWin && this.isAutoFight()) {
            //第一关结束后不应该自动挑战下一关，需要玩家手动点击挑战
            // if(this.nowPass != 1){
            this.isGameFighting = true;
            this.timer.once(delayNext, this, this.delayAutoFight);
            // }
        } else {

            this.timer.clear(this, this.delayAutoFight);

            this.refreshStartGameBtn();
            this.startgamebtn.visible = true;
            this.stopgamebtn.visible = false;
            this.isGameFighting = false;
            this._isAutoFight = false;
        }

        // this.onekeyBtn.visible = this.isGameFighting == false;

        delayClearSec = isWin ? 2 : 0;
        this.timer.once(delayClearSec * 1000, this, this.delayClear);

        if (this.isBackMode) {
            this.timer.once(10000, this, this.checkIsBackEndGame);
        }

        // this.reqLineUp();

        this.guajiScript.showFreeWave();
    }

    private delayAutoFight() {
        if (this.isAutoFight()) {
            this.autoFightNext();
        }
    }

    private isAutoFight() {
        return this._isAutoFight && this.nowPass >= MiscConstAuto.td_main_auto_fight_limit;
    }

    private delayClear() {
        //等待死亡动画结束
        this.elementMgr.endTdFight();
        TDFightHandler.ins.clearData();
        TdMainDataCenter.ins.clearData();
    }

    private checkIsBackEndGame() {
        if (this.isBackMode && this.isGameFighting == false) {
            this.realClose();
        }
    }

    private onClickBtnExit() {
        this.realClose();
    }


    //----------------战斗逻辑---------------------/
    private onTimer100() {
        this.checkMonsterMove();
        if (this.updateCountdown > 0) {
            this.updateCountdown -= this.timer.delta;
            return;
        } else {
            this.updateCountdown = 100;
        }

        this.onForeverPlay();
        this.checkTimer();
        this.checkMonsterNum();
        this.refreshRemainTime();
    }


    /** 每秒一次的循环遍历 */
    // private onTimer1000() {
    //     this.refreshRemainTime();
    // }

    /** 攻击播放 */
    private onForeverPlay() {
        var allHeros = this.elementMgr.findRoleByCamp(RoleCamp.OUR);
        for (let i = 0; i < allHeros.length; i++) {
            if (allHeros[i].checkCanAttack()) {
                this.onFight(allHeros[i] as TDRoleHero);
            }
        }
    }

    /** 单个攻击动作 */
    private onFight(atk: TDRoleHero): void {
        let skillId = atk.getCanUseSkillId();
        if (skillId == 0) {
            return;
        }
        let fight_data: p_td_hero_fight = TdMainDataCenter.ins.get_p_td_hero_fight(atk.heroId, skillId);
        if (fight_data == null) {
            return;
        }

        let skillInfo = atk.getSkillInfo(skillId);
        let hurtList: number[] = this.elementMgr.getHurtList(atk.actor_id, fight_data.num, skillInfo.td_skill_range);
        if (hurtList.length == 0) {
            return;
        }

        let p_step_skill_buff_hurt_map = TDFightHandler.ins.getStepSkill2(skillId, atk, hurtList, this.elementMgr, TdMainDataCenter.ins.p_td_skill_map);
        if (p_step_skill_buff_hurt_map) {
            let stepSkill: p_step_skill = p_step_skill_buff_hurt_map.p_step_skill;
            let buffHurtMap: Map<number, Map<number, p_fight_hurt>> = p_step_skill_buff_hurt_map.buff_hurt_map;
            if (stepSkill) {
                this.elementMgr.battlePlayer.setStepSkill(stepSkill, buffHurtMap);
            }
        }
    }


    private checkTimer() {
        let nextcfg = CfgCacheMapMgr.cfg_td_main_monsterCache.m_get(this.nowPass, this.nowshowwave + 1);
        if (nextcfg && this.elapsedTime >= nextcfg.born_time) {
            this.nextWave();
        }
    }

    private checkMonsterNum() {
        // if (DataCenter.serverTimeSeconds > this.endTime) {
        //     if (this.monsterNum > 0) {
        //         this.endGame(TdEndType.FAIL);
        //     }
        // } 

        if (this.countdown <= 0) {
            if (this.monsterNum > 0) {
                this.endGame(TdEndType.FAIL);
            }
        } else {
            let num = this.monsterNum;
            this.refreshMonsterNumberShow(num);
            if (num == 0) {
                if (this.haveNext) {
                    this.nextWave();
                } else {
                    this.endGame(TdEndType.WIN);
                }
            }
        }
    }

    private monsterEndPath(monster: TDRoleBase) {
        if (monster.isFreeMonster) {
            monster.ToDead();
            // this.elementMgr.delRole(monster.actor_sn);
            return;
        }
        if (this.isGameFighting) {
            this.endGame(TdEndType.FAIL);
        }
    }


    //----------------面板显示相关-------------------/
    private initGameShow() {
        this.passtext.text = window.iLang.L2_DI_P0_GUAN.il().Format(this.nowPass);
        this.stopgamebtn.visible = false;
        this.refreshStartGameBtn();
        this.checkRateBtn();
    }

    /**  */
    private passShow() {

        // this.endTime = DataCenter.serverTimeSeconds + this.nowPassCfg.limit_time;
        // this.startTime = DataCenter.serverTime;
        this.refreshRemainTime();
    }

    private waveShow() {
        this.refreshMonsterNumberShow();
        this.levelTxt.innerHTML = StringUtil.Format(window.iLang.L2_DI_P0_GUAN_DI_P1_BO.il(), [ColorUtil.GetColorHtml(this.nowPass, ColorUtil.GOLD_COLOR), ColorUtil.GetColorHtml(this.nowshowwave, ColorUtil.GOLD_COLOR)]);
        this.passtext.text = window.iLang.L2_DI_P0_GUAN.il().Format(this.nowPass);
    }

    private startGameShow() {
        this.remainbg.visible = true;
        this.elementMgr.hideAllHeroMenu();
        FightStartAnimation.instance.Show();
        this.addChildAt(FightStartAnimation.instance, 0);
        this.nowplayUIMusic();

        this.checkRateBtn(this.nowPass);

        this.showDoorEffect();
    }

    private refreshStartGameBtn(): void {
        let cfg: cfg_main_battle = GuaJiDataCenter.instance.main_battle;
        if (cfg && cfg.need_level > DataCenter.myLevel) {
            this.startgamebtn.label = cfg.need_level + window.iLang.L2_JI_KE_TIAO_ZHAN.il();
            this.startgamepic.skin = ""
        } else {
            this.startgamebtn.label = "";
            if (this.nowPass >= MiscConstAuto.td_main_auto_fight_limit) {
                this.startgamepic.skin = "tdMain/zztf_22.png";
            } else {
                this.startgamepic.skin = "tdMain/zztf_2.png";
            }
        }

        this.imgTips.visible = this.nowPass >= MiscConstAuto.td_main_auto_fight_limit;
    }

    private refreshRemainTime() {

        if (this.isBackMode) {
            return;
        }

        if (this.isGameFighting) {

            let countdownSecond = Math.ceil(this.countdown / 1000);
            countdownSecond = Math.max(0, countdownSecond);
            this.txtremain.value = countdownSecond + "";

            // if (this.endTime > DataCenter.serverTimeSeconds) {
            //     this.txtremain.value = (this.endTime - DataCenter.serverTimeSeconds) + "";
            // } else {
            //     this.txtremain.value = 0 + "";
            // }
        }
    }

    private refreshMonsterNumberShow(num: number = -1) {
        if (this.isBackMode) {
            return;
        }
        if (this.elementMgr) {
            if (num == -1) {
                num = this.monsterNum;
            }
            let otherMonster = this.otherWaveMonsters();
            this.remainnum.innerHTML = StringUtil.Format(window.iLang.L2_SHENG_YU_GUAI_WU_ch10_P0.il(), [ColorUtil.GetColorHtml(num + otherMonster, ColorUtil.GOLD_COLOR)]);
        }
    }

    private otherWaveMonsters(): number {
        let result = 0;
        for (let i = this.nowshowwave + 1; i <= this.nowPassCfg.max_wave; i++) {
            let cfgMonster: cfg_td_main_monster = CfgCacheMapMgr.cfg_td_main_monsterCache.m_get(this.nowPass, i);
            let monster = cfgMonster.monster.split("|");
            if (monster && monster.length > 1) {
                result += Number(monster[1]);
            }
        }
        return result;
    }

    private endGameShow(endType: number) {
        if (endType == TdEndType.WIN) {
        } else if (endType == TdEndType.FAIL) {
        }
        this.remainbg.visible = false;
        this.showGameInfo(false);

        this.clearDoorEffect();

        GameSoundManager.instance.playMusic(MusicUtil.GROW_MUSIC);
    }

    /**播放界面音效 */
    playUIMusic(): void {

    }

    nowplayUIMusic(): void {
        super.playUIMusic();
    }

    private showGameInfo(isshow: boolean) {
        this.showtips.visible = isshow;
        this.autopic.visible = this.nowPass >= MiscConstAuto.td_main_auto_fight_limit;
        this.remainbg.visible = isshow;
    }

    showRedPoint(): void {
        this.SetRedPoint(this.lotterbtn, RedPointMgr.ins.IsCheckRedPoint(PanelEventConstants.LOTTERY_NORMAL), 55, 0);
    }

    refreshRedPoint(vo: RedPointVo): void {
        if (vo == null) {
            return;
        }
        if (vo && vo.eventId == PanelEventConstants.LOTTERY_NORMAL) {
            this.SetRedPoint(this.lotterbtn, vo.isRedState);
        }
    }

    private showDoorEffect() {
        let cfgTdMap = CfgCacheMapMgr.cfg_td_mapCache.get(this.nowPassCfg.map);
        let posList = this.getDoorEffectPos();
        posList.forEach(pos => {
            this.showDoorEffectList.push(this.showGSkeleton(this.elementMgr.battleLayer, cfgTdMap.effect, null, { x: pos.targetX, y: pos.targetY - 70,scale:1.5}));
        })
    }

    private clearDoorEffect() {
        this.showDoorEffectList.forEach(effect => {
            effect && effect.destroy();
        })
        this.showDoorEffectList.length = 0;
    }

    // ----------------场景相关--------------------/

    private initScene(): void {
        //panel用于地图滚动
        // this.mapPanel = new Panel();
        // this.mapPanel.name = "mapPanel";
        // this.mapPanel.width = this.relativeWidth;
        // this.mapPanel.height = this.relativeHeight;
        // this.mapPanel.zOrder = -100;
        // this.mapPanel.vScrollBarSkin = "common/vscroll.png";
        // this.mapPanel.vScrollBar.visible = false;
        // this.addChild(this.mapPanel);

        if (!this.elementMgr) {
            this.elementMgr = new TDSceneElementMgr(SceneType.TOWER_DEFENSE_MAIN, TdMainDataCenter.ins);
            this.elementMgr.addSceneLayer(this);
            this.elementMgr.setCamera2d(this.width, 1760, this.width, this.height, this);
            this.elementMgr.battleLayer.on(Event.RESIZE, this, this.onTdMainBgShow);
        }

        this.elementMgr.setTdMapBg(this.nowPassCfg.map);


        this.onTdMainBgShow();

        this._pathPointMap = this.getMonsterPathMap();
    }

    private onTdMainBgShow(nowscale: number = NaN, offsetY: number = NaN, textScale: number = NaN, angletxt: number = NaN) {
        // this.elementMgr.battleLayer.x = GlobalConfig.isYeGame ? 720 : 0;
        // this.elementMgr.battleLayer.y = -((1700 - this.relativeHeight) / 2);

        if (Number.isNaN(nowscale) || nowscale <= 0) {
            nowscale = MiscConstAuto.td_main_map_rate;
        }
        if (Number.isNaN(offsetY) || offsetY <= 0) {
            offsetY = MiscConstAuto.td_main_map_pos_y;
        }
        if (Number.isNaN(textScale) || textScale <= 0) {
            textScale = MiscConstAuto.td_main_map_word_rate;
        }
        if (Number.isNaN(angletxt) || angletxt <= 0) {
            angletxt = 0;
        }

        let mapWidth = this.elementMgr.battleLayer.width * nowscale;
        let mapHeight = this.elementMgr.battleLayer.height * nowscale;
        //居中
        let x = (this.relativeWidth - mapWidth) / 2;
        let y = (this.relativeHeight - mapHeight) / 2 + offsetY;
        this.elementMgr.battleLayer.pos(x, y);
        this.elementMgr.battleLayer.scale(nowscale, nowscale);

        let rolelist = this.elementMgr.findRoleByCamp(RoleCamp.OUR);
        rolelist.forEach(role => {
            (role as TDRoleHero).setTextScale(textScale);
            (role as TDRoleHero).rotateAngle(angletxt);
        });

        // this.bottombox.y = this.elementMgr.battleLayer.y + 1230;
    }


    //-----------------场景元素相关---------------------/

    private addNullItem(index: number, isLord: boolean, pointVo: TDPathPointVo) {
        let nullImte = new TdNulltem(index, this.matchType);
        nullImte.name = "Nulltem" + index;
        this.nullShowMap.set(index, nullImte);
        this.elementMgr.battleLayer.addChild(nullImte)
        nullImte.isLord = isLord;
        let heroPointVo = pointVo;
        nullImte.pos(heroPointVo.targetX - 60, heroPointVo.targetY - 60);
    }

    private refreshNullItemMap() {

        let heroPointVoList: TDPathPointVo[] = ArrayUtil.getArrByMapSortKey(this.getRolePos());
        this._roleDataList = this.getRoleDataList();

        this.nullShowMap.forEach(item => {
            item.destroy();
        });
        this.nullShowMap.clear();

        for (let i = 0; i < this._roleDataList.length; i++) {
            let nowshowIndex = i + 1;
            if (this._roleDataList[i]) {

            } else {
                this.addNullItem(nowshowIndex, false, heroPointVoList[i] || heroPointVoList[0]);
            }
        }

        let lordPosVo = heroPointVoList[heroPointVoList.length - 1];
        let nowshowIndex = lordPosVo.index + 1;
        let lordData = this.getLordData(nowshowIndex);
        if (lordPosVo && lordData) {

        } else {
            this.addNullItem(nowshowIndex, true, lordPosVo);
        }
    }

    private createOrRefreshRole() {
        let heroPointVoList: TDPathPointVo[] = ArrayUtil.getArrByMapSortKey(this.getRolePos());
        this._roleDataList = this.getRoleDataList();

        let rolelist = this.currLineUpList;

        //如果不存在的就删了
        let allRoleList = this.elementMgr.findRoleByCamp(RoleCamp.OUR);
        let showRoleMap: Map<number, TDRoleBase> = new Map();
        let showLord: TDRoleBase;
        allRoleList.forEach(role => {
            if (role.actorType == ActorType.Hero) {
                if (rolelist.indexOf(role.actor_id) == -1) {
                    role.clear();
                } else {
                    showRoleMap.set(role.actor_id, role);
                }
            } else {
                if (TdMainDataCenter.ins.p_td_lineup && role.actor_id != TdMainDataCenter.ins.p_td_lineup.lord_id) {
                    role.clear();
                } else {
                    showLord = role;
                }

            }
        });

        //删除Role的Sn绑定数据
        this.elementMgr.clearRoleDataMap();


        //重新添加武将
        for (let i = 0; i < this._roleDataList.length; i++) {
            if (this._roleDataList[i]) {
                let hero;
                if (showRoleMap.has(this._roleDataList[i].actor_id) == false) {
                    hero = this.elementMgr.addHero(this._roleDataList[i], RoleCamp.OUR, {classRoleBase: TDRoleHero});
                    hero.canDrag = this.isFirstGuideAllFinish;
                } else {
                    hero = showRoleMap.get(this._roleDataList[i].actor_id);
                    this.elementMgr.setRoleIndex(hero, this._roleDataList[i].actor_sn);
                }

                this.elementMgr.setHeroPointVo(hero, heroPointVoList);
                hero.pos(hero.heroPointVo.targetX, hero.heroPointVo.targetY);
            }
        }

        /** 领主上阵 */
        let lordPosVo = heroPointVoList[heroPointVoList.length - 1];
        let lordData = this.getLordData(lordPosVo.index + 1);
        if (lordPosVo && lordData) {
            if (!showLord) {
                let lord = this.elementMgr.addLord(lordData);
                this.elementMgr.setHeroPointVo(lord, heroPointVoList);
                lord.heroPointVo = lordPosVo;
                lord.pos(lordPosVo.targetX, lordPosVo.targetY);
            } else {
                this.elementMgr.setRoleIndex(showLord, lordData.actor_sn);
            }
        } else {
            // this.addNullItem(nowshowIndex, true, lordPosVo);
        }

        this.doForceLineUp();
        this.refreshNullItemMap();
        this.onGuideAutoLineUp();
    }

    private onFightGetInfo() {
        let roleList = this.elementMgr.findRoleByCamp(RoleCamp.OUR);
        roleList.forEach(role => {
            if (role instanceof TDRoleHero) {
                if (role && role.normalSkillInfo && role.normalSkillInfo.td_cd_time == 0) {
                    role.onFightStart();
                }
            }
        });

        this.refreshRoleShowInfo();
    }

    private refreshRoleShowInfo() {

        // if(this.isGameFighting == false){
        //     return;
        // }

        this._roleDataList = this.getRoleDataList();
        this._roleDataList.forEach(heroData => {
            if (heroData) {
                //寒冰1  风行2
                //寒冰2  风行1
                let hero = this.elementMgr.findRoleById(heroData.actor_id) as TDRoleHero;
                if (hero) {
                    hero.refreshShowInfo(heroData);
                }
            }
        })

        //领主技能更新
        let lord = this.lord;
        if (lord) {
            lord.refreshShowInfo(lord.data);
        }
    }

    private onRoleDeadHide(roleBase: TDRoleBase) {
        if (roleBase instanceof TDRoleMonster) {
        }
    }


    private createMonster(): TDRoleMonster[] {
        this._monsterDataList = this.getMonsterDataList();
        let monsterList = [];
        for (let i = 0; i < this._monsterDataList.length; i++) {
            let monster = this.elementMgr.addMonster(this._monsterDataList[i], -100, -100, 0);
            // monster.setMonsterData(this.nowWaveCfg);
            let moveSpeed = monster.actorType == ActorType.TDMonster ? MiscConstAuto.td_trial_monster_speed : MiscConstAuto.td_trial_boss_speed;
            let showSize = monster.actorType == ActorType.TDMonster ? MiscConstAuto.td_trial_monster_show_rate : MiscConstAuto.td_trial_boss_show_rate;
            monster.baseAttr.moveSpeed = moveSpeed;
            monster.scale(showSize, showSize);
            monster.name = this.nowPassCfg.id + "_" + this.nowWaveCfg.wave + "_" + this._monsterDataList[i].actor_sn;
            monsterList.push(monster);
        }
        return monsterList;
    }


    public monsterPathPoint(monsterList: TDRoleMonster[]) {
        for (let i = 0; i < monsterList.length; i++) {
            this.monsterMoveList.push(monsterList[i])
        }
    }

    public checkMonsterMove() {

        let monsterInterval = MiscConstAuto.td_trial_monster_interval;
        if (this.nowWaveCfg) {
            monsterInterval = this.nowWaveCfg.interval;
        } else {
            let nowMonster = CfgCacheMapMgr.cfg_td_main_monsterCache.m_get(this.nowPass, 1);
            monsterInterval = nowMonster.interval;
        }

        if (this.monsterMoveTime == 0) {
            this.setMonsterMove(0);
            this.monsterMoveTime = this.now + monsterInterval;
        } else if (this.now > this.monsterMoveTime) {
            var delta = this.now - this.monsterMoveTime;
            var monsterNum = Math.floor(delta / monsterInterval);
            if (monsterNum == 0) {
                monsterNum = 1;
                var remainDelta = delta;
            } else {
                var remainDelta = delta - monsterNum * monsterInterval;
            }

            this.monsterMoveTime = this.now + monsterInterval - remainDelta;

            for (let i = 0; i < monsterNum; i++) {
                this.setMonsterMove(delta);
            }
        }
    }

    private setMonsterMove(delta: number) {
        // this.monsterMoveTime = this.now + MiscConstAuto.td_trial_monster_interval / this.elementMgr.playRate;
        let monster = this.monsterMoveList.shift();
        if (!monster) {
            return;
        }

        //TODO 第几组路点, 随机?
        let groupIndex = Math.floor(Math.random() * this._pathPointMap.size);
        let script = monster.startPathPoint(this._pathPointMap.get(groupIndex), false, monster.baseAttr.moveSpeed, Handler.create(this, this.monsterEndPath));
        //偏移修正
        // console.log("------delta = " + delta);

        //if(this.nowWaveCfg.wave == 7){
        //    console.log("------delta = " + delta);
        //}
        script.setStartDeltaPos(delta);

        //BOSS来袭
        let isBoss = monster.actorType == ActorType.TDBoss;
        if (isBoss) {
            this.skBossTips = this.showGSkeleton(this, "td_main_boss", this.skBossTips, {isLoop: false});
            this.skBossTips.pos(this.relativeWidth / 2, this.relativeHeight / 2);
            this.skBossTips.on(Laya.Event.STOPPED, this, () => {
                this.skBossTips.destroy();
                this.skBossTips = null;
            })
        } else {
            // if (this.skBossTips) {
            //     this.skBossTips.destroy();
            //     this.skBossTips = null;
            // }
        }
    }

    private updateGuaJiInfo(): void {
        //添加倒计时
        this.timer.loop(1000, this, this.updateTime).runImmediately();

        this.checkRedQuickFight();
    }

    public onClickMission(): void {
        this.checkGuideMission(true);
        let mission = TdMainDataCenter.ins.get_curr_mission();
        if (!mission) return;

        let cfg = CfgCacheMapMgr.cfg_td_main_missionCache.get(mission?.id);
        if (!cfg) {
            return;
        }
        if (mission.status == MissionStatus.ACCEPT) {

        } else if (mission.status == MissionStatus.ACC_REWARD) {
            //完成任务
            //领取奖励
            TdMainDataCenter.ins.m_simp_mission_finish_tos(mission.id);
        } else if (mission.status == MissionStatus.FINISH_REWARD) {

        }
    }

    public checkGuideMission(isAutoGuide: boolean = false): void {
        this.checkLastFinishGuideMission();
        this.checkCurrGuideMission(isAutoGuide);
        this.missionGuideScript.updateMissionEff();
    }

    private checkCurrGuideMission(isAutoGuide: boolean): void {


        let mission = TdMainDataCenter.ins.get_curr_mission();
        let cfg = CfgCacheMapMgr.cfg_td_main_missionCache.get(mission?.id);

        isAutoGuide = isAutoGuide || !!cfg?.is_auto_guide;

        //---------当前任务
        mission = TdMainDataCenter.ins.get_curr_mission();
        cfg = CfgCacheMapMgr.cfg_td_main_missionCache.get(mission?.id);
        if (!mission || !cfg || !cfg.guide_id || !isAutoGuide) {
            return;
        }

        if (mission.status == MissionStatus.ACCEPT) {

            // if (!isAutoGuide) {
            //     return;
            // }

            if (!cfg.guide_id) {
                return;
            }

            let finishList = TdMainDataCenter.ins.tdMissionIdFinishList;
            if (finishList.indexOf(cfg.id) != -1) {
                return;
            }

            // if (cfg.guide_id != 0) {
            //     GuideMgr.ins.setGranchGuideId(cfg.guide_id);
            //     return;
            // }

            //当前引导

            let progress = mission.cur_progress;
            let maxProgress = cfg.max_progress;
            let isGuide = false;
            if (progress < maxProgress) {
                isGuide = true;
            }

            if (isGuide) {

                if (!GuideMgr.ins.curGuideId) {
                    // TdMainDataCenter.ins.currMissionId = cfg.id;
                    TdMainDataCenter.ins.tdMissionIdFinishList.push(cfg.id);
                    GuideMgr.ins.setGranchGuideId(cfg.guide_id, false, {missionId: cfg.id});
                }

            } else {
            }

        } else if (mission.status == MissionStatus.ACC_REWARD) {

        } else if (mission.status == MissionStatus.FINISH_REWARD) {

        }
    }

    private checkLastFinishGuideMission(): void {
        let mission = TdMainDataCenter.ins.get_last_acc_reward_mission();
        let cfg = CfgCacheMapMgr.cfg_td_main_missionCache.get(mission?.id);
        if (!mission || !cfg || !cfg.guide_id || !cfg.is_auto_guide) {
            return;
        }

        //完成任务
        let finishList = TdMainDataCenter.ins.tdMissionIdFinishList;
        if (finishList.indexOf(cfg.id) == -1) {
            finishList.push(cfg.id);
        }

        //任务刚开始,那就强制完成
        let cfgGuide = GuideMgr.ins.curCfg;
        if (cfg.guide_id && cfgGuide && cfgGuide.guide_id == cfg.guide_id && cfgGuide.step == 0) {
            let tempData = GuideMgr.ins.granchTempData;
            if (tempData?.missionId == cfg.id) {
                ConsoleUtils.log("------强制完成任务引导, cfg = ", cfg);
                GuideMgr.ins.clearGranchGuide(true);
            }
        }
    }

    public updateMissionProgress(updateMission: p_simp_mission = null): void {

        // if (this.uiProgress == null) {
        //     this.uiProgress = UIProgressBarEx.SetUIProgressBar(this, this.proMission);
        // }

        this.showBoxMission();

        let mission = TdMainDataCenter.ins.get_curr_mission();
        let cfg = CfgCacheMapMgr.cfg_td_main_missionCache.get(mission?.id);
        if (cfg) {
            let progress = mission.cur_progress;
            let maxProgress = cfg.max_progress;
            let progressRate = progress / maxProgress;

            // let tempParam = this.uiProgress.getTempParam();
            // let tempId = tempParam?.id;
            // let isChange = tempId && tempId != mission.id;
            // this.uiProgress.SetValueLerp(progress, maxProgress, isChange, 500);
            // this.uiProgress.setTempParam({id:mission.id});

            this.htmlMissionDesc.innerHTML = StringUtil.FormatCfgField(cfg, "event_name", ColorUtil.TOTAL_GREEN);

            let colorProgress = progress >= maxProgress ? ColorUtil.TOTAL_GREEN : ColorUtil.FONT_RED_ITEM;
            let strProgress = "{0}/{1}".Format(progress.toString(), maxProgress.toString()).toColor(colorProgress);
            this.htmlMissionProgress.innerHTML = strProgress;

            // if (isChange) {
            //     this.uiProgress.ShowEffectWhenLvUp("lottery_box0", 88, 8, 0.5, 0.8);
            //     // let eff = this.ShowEffect("lottery_box0", this.proMission, true);
            //     // eff.pos(0, 0);
            //     // eff.scale(1.4, 1);
            // }


            if (this.itemMission == null) {
                this.itemMission = GoodsItem.create();
                this.itemMission.name = "itemMission";
                this.itemMission.scale(0.7, 0.7);
                this.itemMission.pos(2, 2);
                this.boxMission.addChild(this.itemMission);
            }

            let p_item = mission.rewards[0];
            if (p_item) {
                let vo = GoodsVO.GetPItemToVos(mission.rewards)[0];
                this.itemMission.SetGoodsVoData(vo);
            }

            this.missionGuideScript.updateMissionEff();
            this.checkGuideMission();
        }
    }

    private showBoxMission() {
        this.boxMission.visible = this.isFirstGuideAllFinish && TdMainDataCenter.ins.hasMainMissions();
    }


    private updateTime(): void {

        if (this.isBackMode) {
            return;
        }

        //更新挂机时长
        let time: number = GuaJiDataCenter.instance.guaJiTime > GuaJiDataCenter.instance.max_duration ? GuaJiDataCenter.instance.max_duration : GuaJiDataCenter.instance.guaJiTime;
        this.lbTime.text = DateUtil.GetHMS(time);
        //宝箱图片
        // let effectName: string = GuaJiDataCenter.getGuajiBoxEffName(time);
        // this.showbox.skin = "guaJi/" + effectName + ".png"

        //cfg_guaji_box_time中配置不同的显示状态
        let cfg = GuaJiDataCenter.getCfgGuajiBoxTime(time);
        let effName = GuaJiDataCenter.getGuajiBoxEffName(time);
        if (effName == "td_main_gold1") {
            var offsetX = 30;
            var offsetY = 50;
        } else {
            var offsetX = 50;
            var offsetY = 50;
        }

        if (!this.skGuajiReward || this.skGuajiReward.name != effName) {

            this.skGuajiReward = this.showGSkeleton(this.btnQuickFight, effName, this.skGuajiReward, {type: ESkeletonType.UI_EFFECT});
            this.skGuajiReward.name = effName;
            this.skGuajiReward.pos(offsetX, offsetY);
            this.btnQuickFight.addChildAt(this.skGuajiReward, 0);
        }

        // if (!this.skGuajiReward) {
        //     this.skGuajiReward = this.showGSkeleton(this.btnQuickFight, "ji_ji", null, { type: ESkeletonType.MODEL_ACTION });
        // }

        if (!this.skBox) {
            this.skBox = this.showGSkeleton(this.btnBox, "007jinxiangzi", null);
            this.skBox.scale(0.3, 0.3);
            this.skBox.pos(45, 80);
            this.skBox.playAction("idle_1");
        }

        this.lbBoxNum.text = "X" + GuaJiDataCenter.instance.getGuajiBoxCount();
        this.updateAllPoint();
    }

    private onClickMainBattleBox() {
        if (this.checkGuideLevel()) {
            return;
        }
        this.dispatchEvent(ModuleCommand.OPEN_GUAJI_BOX_DIALOG)
    }

    private onClickLotteryBtn() {
        this.dispatchEvent(ModuleCommand.OPEN_LOTTERY_DIALOG);
    }

    private onClickSetScale() {
        this.onTdMainBgShow(Number(this.scaleinpu.text), Number(this.yoff.text), Number(this.fontsize.text), Number(this.angletxt.text));

        TdMainDataCenter.ins.testMonsterSkin = this.inputMonsterSkin.text || "";
        //测试怪物皮肤
        let allMonster: TDRoleMonster[] = this.elementMgr.findRoleByCamp(RoleCamp.ENEMY).filter(role => {
            return role instanceof TDRoleMonster;
        }) as TDRoleMonster[];
        allMonster.forEach(monster => {
            monster.testMonsterSkin();
        })
    }

    //----------------------引导--------------------------/
    public checkGuide(): void {
        let curHide = GuideMgr.ins.getIsHideGuideUI(this.name);
        let newHide = false;
        if (this.isNewHeroAppearFinish == false) {
            newHide = true;
        }

        if (curHide != newHide) {
            GuideMgr.ins.setHideGuideUI(newHide, this.name);
        }

        super.checkGuide();

        // this.missionGuideScript.updateMissionEff();
        this.checkGuideMission();
    }

    private getLineupLordId() {
        let lord = this.lord;
        if (!lord || !lord.data) {
            return 0;
        }

        let lordId = lord.data.actor_id;

        //领主变了,重新布阵
        // for (const lordsGroup of LordDataCenter.instance.lordLineUpList) {
        //     if (this.matchType == lordsGroup.match_type) {
        //         for (const lord of lordsGroup.list) {
        //             if (lord.lord_id != this.lord.data.actor_id) {
        //                 ret = lord.lord_id;
        //                 break;
        //             }
        //         }
        //     }
        // }

        //if (!LordDataCenter.instance.lordLineUpList.get(this.matchType)?.has(lord.data.actor_id)) {
        //        ret = LordDataCenter.instance.lordLineUpList.get(this.matchType)?.get(lord.data.actor_id).lord_id;
        //   }
        //const lineUpForMatchType = LordDataCenter.instance.lordLineUpList.get(this.matchType);
        // if (lineUpForMatchType &&!lineUpForMatchType.has(lord.data.actor_id)) {
        //     const lordEntry = lineUpForMatchType.get(lord.data.actor_id);
        //     if (lordEntry) {
        //         ret = lordEntry.lord_id;
        //     }
        // }

        const lineUpForMatchType = LordDataCenter.instance.lordLineUpList.get(this.matchType);
        let lordEntry = lineUpForMatchType?.get(lordId);
        if (lordEntry) {
            lordId = lordEntry.lord_id;
        }

        return lordId;
    }

    private doForceLineUp() {
        this.callLater(this.doForceLineUp2);
    }

    private doForceLineUp2() {

        let currLordId = 0;
        let lord = this.lord;
        if (lord && lord.data) {
            currLordId = lord.data.actor_id;
        }

        let lineupLordId = this.getLineupLordId() || currLordId;
        let isLordChange = lineupLordId != currLordId;

        // let isLineupChange = this.tempLineUpList?.length;
        // if (isLineupChange || isLordChange) {
        if (isLordChange) {
            TdMainDataCenter.ins.m_td_main_lineup_tos(this.currLineUpList, lineupLordId);
        }
        // this.tempLineUpList = null;
    }

    private onHeroPosChange(newLineUpList, matchType: number) {
        if (matchType && matchType == this.matchType) {
            TdMainDataCenter.ins.m_td_main_lineup_tos(newLineUpList);
            // this.tempLineUpList = newLineUpList;

            // // if(this.isGameFighting == false){
            // //     this.doForceLineUp();
            // // }
            // this.refreshNullItemMap();
            this.onFingerGuideEnd();

        }
    }

    private onFingerGuideEnd() {

        let script = this.elementMgr.battleLayer.getComponent(FingerMoveScript);
        if (script) {
            script.onFinish();
        }

        if (this.fingerMoveScript) {
            this.fingerMoveScript.onFingerGuideEnd();
        }
    }

    //-----------------------数据处理-----------------------/
    private reqStartFight() {
        // this.reqLineUp();
        /** 获取当前关的数据 */
        TdMainDataCenter.ins.m_td_main_fight_tos(TdMainFightOpType.START_FIGHT);
    }

    private getRoleDataList(): p_fight_actor[] {
        let roledataList = [];
        let rolelist = this.currLineUpList;
        for (let i = 0; i < rolelist.length; i++) {
            let roleId = rolelist[i];
            let hero: PHero = HeroDataCenter.instance.getHero(roleId);
            if (hero) {
                roledataList.push(this.getRoleData(i + 1, hero));
            } else {
                roledataList.push(null)
            }
        }
        return roledataList;
    }

    private getMonsterDataList(): p_fight_actor[] {
        let monsterDataList = [];
        if (this.nowWaveCfg) {
            let monsterData = this.nowWaveCfg.monster.split("|");
            let monsterId = Number(monsterData[0]);
            let monsterNum = Number(monsterData[1]);
            let maxSn = this.nowWaveCfg.wave * 100 + 1;
            for (let i = 0; i < monsterNum; ++i) {
                monsterDataList.push(this.getMonsterData(maxSn + i, monsterId));
            }
        }

        return monsterDataList;
    }

    //actor_type = 1[default = 0];  //作战单元的类型，1=英雄，2=怪物
    private getMonsterData(actor_sn: number, type_id: number): p_fight_actor {
        let data: p_fight_actor = new p_fight_actor();
        let cfgMonster = CfgCacheMapMgr.cfg_monsterCache.get(type_id);
        if (cfgMonster) {
            data.level = cfgMonster.level;
        }
        data.actor_sn = actor_sn;
        data.type_id = type_id;
        data.actor_id = type_id;
        data.actor_type = this.nowWaveCfg.type == TdMonsterType.MONSTER ? ActorType.TDMonster : ActorType.TDBoss;
        data.hp = TdMainDataCenter.ins.p_td_monster_hp_map.get(type_id);
        data.max_hp = data.hp;
        return data;
    }


    private getRoleData(actor_sn: number, phero: PHero, actor_type: number = 1): p_fight_actor {
        let data: p_fight_actor = new p_fight_actor();
        data.actor_sn = actor_sn;
        data.actor_type = actor_type;
        data.type_id = phero.type_id;
        data.hero_type_id = phero.type_id;
        data.actor_id = phero.hero_id;
        data.level = phero.level;
        data.stage = phero.stage;
        data.star = phero.star;
        data.skin_id = phero.skin_id;
        data.power = phero.power;
        data.skin_id = phero.skin_id;
        data.hp = phero.fight.hp;
        data.max_hp = phero.fight.hp;
        data.nation = phero.nation;
        return data;
    }

    public getRolePos(): Map<number, TDPathPointVo> {
        let posMap = TDPathPointVo.parseCfgTdPathPoint(this.nowPassCfg.map, ETDPathPointType.HERO);

        //英雄取第一组
        let posList = posMap.get(0);
        let map = new Map<number, TDPathPointVo>();
        for (let i = 0; i < posList.length; i++) {
            map.set(posList[i].index + 1, posList[i]);
        }
        return map;
    }

    public getDoorEffectPos(): TDPathPointVo[] {
        let posMap = TDPathPointVo.parseCfgTdPathPoint(this.nowPassCfg.map, ETDPathPointType.PATH);

        let posList: TDPathPointVo[] = [];
        posMap.forEach((value, key) => {
            posList.push(value[0]);
        });
        return posList;
    }

    private getMonsterPathMap(): Map<number, TDPathPointVo[]> {
        //TODO 怪物先取第一组
        return TDPathPointVo.parseCfgTdPathPoint(this.nowPassCfg.map, ETDPathPointType.PATH);
    }

    private getLordData(actor_sn: number) {
        if (!TdMainDataCenter.ins.p_td_lineup) {
            return;
        }
        let lord_id: number = TdMainDataCenter.ins.p_td_lineup.lord_id;
        let cfg = CfgCacheMapMgr.cfg_lord_baseCache.get(lord_id);
        if (cfg == null) {
            return;
        }

        let actorData: p_fight_actor = new p_fight_actor();
        actorData.type_id = lord_id;
        actorData.actor_sn = actor_sn;
        actorData.actor_id = lord_id;
        actorData.hp = 99999999;
        actorData.actor_type = ActorType.Lord;
        return actorData;
    }

    private get haveNext(): boolean {
        return !!CfgCacheMapMgr.cfg_td_main_monsterCache.m_get(this.nowPass, this.nowshowwave + 1);
    }

    public get monsterNum(): number {
        if (this.elementMgr) {
            // let monsterNum = this.elementMgr.findRoleByCamp(RoleCamp.ENEMY).length;
            let monsterNum = this.elementMgr.findRoleByCamp(RoleCamp.ENEMY).filter(monster => {
                return monster.isDead() == false;
            }).length;
            return monsterNum;
        }
        return 0;
    }


    //-------------------------点击事件-------------------------/
    public btnClick(name?: string): void {
        super.btnClick(name);
        if (name != "imgClick" && this.elementMgr) {
            this.elementMgr.hideAllHeroMenu();
        }
    }

    private onStartGameClick() {
        // if (TdMainDataCenter.ins.remainTimes>0 && this.isGameFighting == false) {
        if (this.isCanFightNextPass()) {
            this.reqStartFight();

            if (this.nowPass >= MiscConstAuto.td_main_auto_fight_limit) {
                this._isAutoFight = true;
            }
        }

        // } else {
        //     TipsUtil.showTips(window.iLang.L2_TIAO_ZHAN_CI_SHU_BU_ZU.il());
        //     this.close();
        // }
    }

    public isCanFightNextPass(): boolean {
        let cfg: cfg_main_battle = GuaJiDataCenter.instance.main_battle;
        let isMaxFight = GuaJiDataCenter.instance.checkIsMaxFightPass();
        if (cfg.need_level > DataCenter.myLevel) {
            if (GuaJiDataCenter.instance.quick_times > 0) {
                DispatchManager.dispatchEvent(ModuleCommand.OPEN_GUA_JI_QUICK_FIGHT_DIALOG);
            } else if (!YueKaDataCenter.instance.isOpenAllQuickFightYueKa()) {
                TipsUtil.showDialog(this, window.iLang.L2_JI_HUO_YUE_KA_HUO_QU_GENG_DUO_KUAI_SU_GUA_JI_CI_SHU.il(), window.iLang.L2_TI_ch11_SHI.il(), () => {
                    GameUtil.gotoSystemPanelById(PanelEventConstants.PAYMENT, PaymentVO.KEY_WELFARE_CARD);
                }, {okName: window.iLang.L2_QIAN_WANG_JI_HUO.il()});
            } else {
                TipsUtil.showTips(window.iLang.L2_DENG_JI_BU_ZU_ch31_QING_TI_SHENG_DENG_JI.il());
            }

            return false;
        } else if (isMaxFight) {
            TipsUtil.showTips(window.iLang.L2_YI_DA_DANG_QIAN_ZUI_DA_GUAN_KA_ch31_JING_QING_QI_DAI_XIN_GUAN_QIA_TUI_CHU.il());
            return false;
        }
        return true;
    }

    private onStopGameClick() {
        if (this.isGameFighting) {
            TipsUtil.showDialog(this, window.iLang.L2_TUI_CHU_HOU_BEN_CHANG_ZHAN_DOU_JIANG_PAN_DING_WEI_SHI_BAI_ch31_SHI_FOU_QUE_REN.il(), "", () => {
                this.endGame(TdEndType.FAIL);
                // this.isExitBattle = true;
            });
        } else {
        }
    }


    private autoFightNext() {
        this.onStartGameClick();
    }

    onClickQuickFight(): void {
        if (this.checkGuideLevel()) {
            return;
        }
        GuaJiDataCenter.instance.loginRedPoint = false;
        this.dispatchEvent(ModuleCommand.OPEN_GUA_JI_QUICK_FIGHT_DIALOG);
        this.checkRedQuickFight();
    }

    private checkGuideLevel(): boolean {
        if (MiscConstAuto.mainUIBottomLayer_fight_change == DataCenter.myLevel) {
            TipsUtil.showTips(MiscConstAuto.mainUIBottomLayer_fight_change_tip);
            return true;
        }
        return false;
    }

    //设置"快速挂机"的按钮红点
    private checkRedQuickFight(): void {
        let redPointX = (this.btnQuickFight.width * this.btnQuickFight.scaleX) + 20;
        let redPointY = 10;
        this.SetRedPoint(this.btnQuickFight, GuaJiDataCenter.instance.checkRedQuickFight(), redPointX, redPointY);
    }


    private setSpeedRate(rate: number) {
        if (rate > TdMainDataCenter.ins.maxSpeedRate) {
            rate = 1;
        }
        this.ratebtn.label = "x" + rate;
        TdMainDataCenter.ins.speedRate = rate;
        this.elementMgr.setPlaySpeed(rate, true);
    }

    private onClickRate() {
        let rate = TdMainDataCenter.ins.speedRate + 1;

        if (!VipTeQuanUtil.checkFightRate(rate)) {
            rate = 1;
        }

        this.setSpeedRate(rate);
    }

    private onClickBack() {

        if (this.rightTopIconLayer) {
            TdMainDialog.menuExpandState.set(this.rightTopIconLayer.name, this.rightTopIconLayer.expandFlag);
        }
        if (this.rightBottomIconLayer) {
            TdMainDialog.menuExpandState.set(this.rightBottomIconLayer.name, this.rightBottomIconLayer.expandFlag);
        }

        // if (this.elementMgr.isFighting || this.isAutoFight()) {

        //     if (this.isBackMode == false) {
        //         this.isBackMode = true;
        //         this.dispatchEvent(ModuleCommand.CLOSE_TD_MAIN_DIALOG);
        //         UIUtil.onDialogOpenOrClose(this);
        //         Dialog.manager._checkMask();
        //         TipsUtil.showTips(window.iLang.L2_YI_QI_DONG_HOU_TAI_YUN_XING.il());
        //     }

        // } else {
        //     this.realClose();
        // }


        if (this.isBackMode == false) {
            this.isBackMode = true;
            this.dispatchEvent(ModuleCommand.CLOSE_TD_MAIN_DIALOG);
            UIUtil.onDialogOpenOrClose(this);
            Dialog.manager._checkMask();

            if (this.elementMgr.isFighting || this.isAutoFight()) {
                // TipsUtil.showTips(window.iLang.L2_YI_QI_DONG_HOU_TAI_YUN_XING.il());
            }

            this.timer.once(5000, this, this.checkIsBackEndGame);
        }
    }

    close(type: string = null): void {
        this.onClickBack();
    }

    realClose(type: string = null) {
        this.isBackMode = false;
        this.doForceLineUp();
        super.close(type);
    }

    private onClickFightMode() {
        let index = this.cbFightMode.selectedIndex;
        this.isBackMode = index == 1;
        if (index == 0) {
            this.onFront();
        } else {
            this.onBack();
        }
    }

    private onOpenOrCloseDialog(dialog: BaseDialog) {
        if (!dialog || !dialog.isFullScreen) {
            return;
        }

        if (dialog.name == this.name) {
            this._onOpenOrCloseDialog(this);
        } else {
            this.timer.once(200, this, this._onOpenOrCloseDialog, [dialog]);
        }

        // this.callLater(this._onOpenOrCloseDialog, [dialog]);
    }

    private _onOpenOrCloseDialog(dialog: BaseDialog) {

        if (!dialog || !this.elementMgr || this.isBackMode) {
            return;
        }

        let topDialog = UIUtil.getTopDialogWithFullScreen();
        if (topDialog) {
            this.elementMgr.isMute = topDialog.name != this.name;
        } else {
            // this.elementMgr.isMute = false;
        }
    }

    public onFront(param?: any): void {
        super.onFront();
        this.isBackMode = false;
        this.cbFightMode.selectedIndex = 0;
        this.elementMgr.onFront();
        this.checkGuide();

        this.onBackModeChange();
        this._onOpenOrCloseDialog(this);
    }

    public onBack(): void {

        super.onBack();
        this.isBackMode = true;
        this.elementMgr.hideAllHeroMenu();
        this.elementMgr.onBack();
        this.checkGuide();

        this.onBackModeChange();
    }

    private onBackModeChange() {
        if (this.isBackMode) {

            if (!this.rightBottomIconLayer) {
                return;
            }

            this.onlineRewardView?.destroy();
            this.leftIconLayer?.destroy();
            this.rightBottomIconLayer?.destroy();
            this.rightTopIconLayer?.destroy();
            this.btnPassReward?.destroy();
            if (this.skGuajiReward) {
                this.skGuajiReward.destroy();
                this.skGuajiReward = null;
            }

            this.onlineRewardView = null;
            this.leftIconLayer = null;
            this.rightBottomIconLayer = null;
            this.rightTopIconLayer = null;
            this.btnPassReward = null;
        } else {
            if (this.rightBottomIconLayer) {
                return;
            }

            let isExpand = true;

            //在线奖励
            this.onlineRewardView = OnlineRewardDataCenter.addOnlineRewardView(this.boxLeftTop, 0, DataCenter.excursion_y + 110);
            // this.TdMainLeftIconLayer2 = new TdMainLeftIconLayer2();

            this.leftIconLayer = new TdMainLeftIconLayer2();
            this.leftIconLayer.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
            this.leftIconLayer.pos(0, 250 + DataCenter.excursion_y);
            //iconLayer.top = 120;
            this.boxLeftTop.addChild(this.leftIconLayer);

            /**招募 */
            this.lotterbtn.pos(90, this.leftIconLayer.y + 260);

            /**右下 */
            this.rightBottomIconLayer = new TdMainRightBottomIconLayer();
            this.rightBottomIconLayer.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
            this.rightBottomIconLayer.updateView();
            this.rightBottomIconLayer.bottom = 30;
            this.addChild(this.rightBottomIconLayer);

            isExpand = !!TdMainDialog.menuExpandState.get(this.rightBottomIconLayer.name);
            this.rightBottomIconLayer.setIconVisible(true, false);

            /**通关奖励按钮 */
            this.createBtnPassReward();

            //右侧
            this.rightTopIconLayer = new MainTopIconLayer2(1, 4);
            this.rightTopIconLayer.name = "rightTopIconLayer";
            this.rightTopIconLayer.anchorX = 0;
            this.rightTopIconLayer.anchorY = 0;
            this.rightTopIconLayer.zOrder = LayerManager.STAGE_ZORDE_TOPEST;
            this.rightTopIconLayer.updateView();

            this.rightTopIconLayer.pos(630, (GlobalConfig.isYeGame ? 35 : 280) + DataCenter.excursion_y + 10);
            this.addChild(this.rightTopIconLayer);

            // let rateBtnY = Math.min(this.rightTopIconLayer.y + 480, )
            this.ratebtn.pos(660, this.rightTopIconLayer.y + 480);

            // isExpand = !!TdMainDialog.menuExpandState.get(this.rightTopIconLayer.name);
            // this.rightTopIconLayer.setIconVisible(isExpand);

            this.updateGuaJiInfo();
            this.updateTime();
            this.refreshMonsterNumberShow();
        }
    }

    private createBtnPassReward() {
        let vo: cfg_activity_icon = CfgCacheMapMgr.cfg_activity_iconCache.get(PanelEventConstants.PASS_REWARD);
        this.btnPassReward = new MenuActivityIconView(Number(vo.show_tag));
        this.btnPassReward.InitInfo(vo);
        let funObj = MainIconConfig.getIconCfg(vo.id);
        if (funObj && funObj["custom"]) {
            this.btnPassReward.updateCustomView();
        }
        this.addChild(this.btnPassReward);
        this.btnPassReward.pos(5, this.leftIconLayer.y + 340);
    }

    private refBtnPassReward() {
        this.btnPassReward?.reflash(PanelEventConstants.PASS_REWARD);
    }

    private nullItemClick(index: number) {

    }

    private onOutLineUp(heroid: number, matchType: number) {
        if ((matchType && matchType != this.matchType) || GlobalConfig.is_majia_shenhe) {
            return;
        }

        let nowShowHeroList = this.currLineUpList;
        let index: number = nowShowHeroList.indexOf(heroid);
        let hero: PHero = HeroDataCenter.instance.getHero(heroid);
        if (index != -1) {
            /**继承下阵 */
            if (TdUtil.checkCanLegends(hero)) {
                TdUtil.checkHeroLegendsReset(hero, index, matchType);
            } else {
                nowShowHeroList[index] = 0;
                TdMainDataCenter.ins.m_td_main_lineup_tos(nowShowHeroList);
            }
        }
    }


    private onOneKeyClick() {
        // if (this.isGameFighting) {
        //     TipsUtil.showTips("战斗中无法布阵");
        //     return;
        // }
        this.autoLineUp();
    }

    /**一键上阵 */
    public autoLineUp() {
        let allHeroList: p_hero[] = HeroDataCenter.instance.hero_list;
        allHeroList = allHeroList.sort(HeroDataCenter.instance.sortHero2);
        let autolineUpList = LineUpDataCenter.instance.autoLineUp(this.matchType, 1, allHeroList, 7);
        let lineUpNum = 0;
        for (let i = 0; i < autolineUpList.length; ++i) {
            if (autolineUpList[i] != 0) {
                lineUpNum++;
            }
        }

        if (lineUpNum == 0) {
            TipsUtil.showTips(window.iLang.L2_MEI_YOU_KE_SHANG_ZHEN_DE_HERO.il());
            return;
        }

        // let setLineUpList = this.currLineUpList;
        // setLineUpList = ArrayUtil.mergeAndFill(setLineUpList, autolineUpList);
        TdMainDataCenter.ins.m_td_main_lineup_tos(autolineUpList, -1, TdMainLineUpOpType.ONE_KEY);
    }

    private onFightResult(toc: m_fight_simp_result_toc) {
        if (toc.match_type != this.matchType) {
            return;
        }

        let isNewHeroGuide = this.newHeroScript?.isNewHeroGuide;
        if (toc.is_success) {
            TipsUtil.showTips(StringUtil.Format(window.iLang.L2_GONG_XI_TONG_GUAN_ZHENG_ZHAN_DI_P0_GUAN.il(), [toc.target_id]));
            //BOSS掉落
            this.elementMgr.onFightResult(toc);
            this.timer.once(1000, this, () => {
                this.elementMgr.flyItemDrop();
            })
            if (isNewHeroGuide) {
                this.missionLotteryScript.clearIconInfo();
                this.timer.once(2000, this, () => {
                    //第一关战斗结束后自动弹出千抽界面，手指指引领取奖励、关闭界面;
                    this.dispatchEvent(ModuleCommand.OPEN_MISSION_LOTTRY_DIALOG);
                });
            }
        } else {
            this.timer.once(2000, this, () => {
                this.elementMgr.alphaOutItemDrop();
            })
        }

        if (this.newHeroScript) {
            this.newHeroScript.onFightResult(toc);
        }

        this.doForceLineUp();

        this.checkFirstGuideStep();
        this.checkGuide();

    }

    private updateMainBattleInfo() {
        this.onLevelUp();
        this.checkFirstRecharge();
    }

    private checkFirstRecharge(openFlag: boolean = false): void {
        if (GuaJiDataCenter.instance.curGuajiPass - 1 == MiscConstAuto.first_recharge_show) {
            ConfigManager.cfg_first_payCache.forEach(cfg => {
                let info: p_first_pay_info = RechargeDataCenter.instance.getFirstPayTaskInfoByGiftId(cfg.gift_id);
                if (info && info.status == 0) {
                    openFlag = true;
                }
            });
            let key = "OPEN_FIRST_RECHARGE_DIALOG" + (GuaJiDataCenter.instance.curGuajiPass - 1);
            let num: number = SettingDataCenter.instance.getVal(key);
            if (num < 1 && openFlag && GlobalConfig.showRecharge && GameUtil.isSysOpen(PanelEventConstants.FIRST_RECHARG, 0, false)) {
                this.dispatchEvent(ModuleCommand.OPEN_FIRST_RECHARGE_DIALOG);
                SettingDataCenter.instance.m_role_setting_tos(key, 1);
            }
        }


    }

    //显示领主对战斗失败的提示
    private showLordFailTalk() {
        this.updateLordTalk();

        let interval = TdMainLordTalk.getTalkArg(0);
        interval > 0 && this.timer.loop(interval * 1000, this, this.updateLordTalk);
    }

    private updateLordTalk() {
        if (this.tdLordTalk) {
            let duration = TdMainLordTalk.getTalkArg(1);
            duration > 0 && this.tdLordTalk.showLordFailTalk(duration * 1000);
        }
    }

}