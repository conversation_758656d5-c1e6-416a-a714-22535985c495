import { GameObject, Component, Vector3 } from "../types/GameTypes";
import { ChaProperty, ChaControlState, ChaResource } from "../character/ChaProperty";
import { BuffManager } from "../managers/BuffManager";
import { DamageManager } from "../managers/DamageManager";

/**
 * 角色状态组件
 * 管理角色的所有状态信息
 */
export class ChaState extends Component {
    /** 角色基础属性 */
    public baseProperty: ChaProperty;

    /** 角色基础控制状态 */
    public baseControlState: ChaControlState;

    /** 角色当前资源 */
    public resource: ChaResource;

    /** 是否已死亡 */
    public dead: boolean = false;

    /** 无敌时间 (秒) */
    public immuneTime: number = 0.0;

    /** 角色朝向角度 */
    public facingDegree: number = 0.0;

    /** 移动速度向量 */
    public moveVelocity: Vector3 = Vector3.zero;

    /** 是否正在移动 */
    public isMoving: boolean = false;

    /** 当前动作速度倍率 */
    public currentActionSpeedRate: number = 1.0;

    constructor(
        baseProperty: ChaProperty,
        baseControlState: ChaControlState = new ChaControlState(),
        initialResource?: ChaResource
    ) {
        super();
        this.baseProperty = baseProperty;
        this.baseControlState = baseControlState;
        this.resource = initialResource || new ChaResource(baseProperty.hp, baseProperty.ammo, 0);
    }

    /** 获取最终属性 (包含Buff修正) */
    public getFinalProperty(): ChaProperty {
        return BuffManager.instance.calculateFinalProperty(this.gameObject, this.baseProperty);
    }

    /** 获取最终控制状态 (包含Buff修正) */
    public getFinalControlState(): ChaControlState {
        return BuffManager.instance.calculateFinalControlState(this.gameObject, this.baseControlState);
    }

    /** 检查是否可以移动 */
    public canMove(): boolean {
        return !this.dead && this.getFinalControlState().canMove;
    }

    /** 检查是否可以旋转 */
    public canRotate(): boolean {
        return !this.dead && this.getFinalControlState().canRotate;
    }

    /** 检查是否可以使用技能 */
    public canUseSkill(): boolean {
        return !this.dead && this.getFinalControlState().canUseSkill;
    }

    /** 检查是否处于无敌状态 */
    public isImmune(): boolean {
        return this.immuneTime > 0;
    }

    /** 设置移动速度 */
    public setMoveVelocity(velocity: Vector3): void {
        if (!this.canMove()) {
            this.moveVelocity = Vector3.zero;
            this.isMoving = false;
            return;
        }

        const finalProperty = this.getFinalProperty();
        const maxSpeed = finalProperty.moveSpeed;

        // 限制最大速度
        if (velocity.magnitude() > maxSpeed) {
            velocity = velocity.normalized().multiply(maxSpeed);
        }

        this.moveVelocity = velocity;
        this.isMoving = velocity.magnitude() > 0.01;
    }

    /** 设置朝向 */
    public setFacing(degree: number): void {
        if (!this.canRotate()) return;

        this.facingDegree = degree;

        // 更新Transform的旋转
        if (this.gameObject && this.gameObject.transform) {
            this.gameObject.transform.rotation = degree;
        }
    }

    /** 治疗 */
    public heal(amount: number): void {
        if (this.dead) return;

        const finalProperty = this.getFinalProperty();
        this.resource.hp = Math.min(finalProperty.hp, this.resource.hp + amount);
    }

    /** 消耗资源 */
    public consumeResource(cost: ChaResource): boolean {
        if (!this.resource.canAfford(cost)) {
            return false;
        }

        this.resource.consume(cost);
        return true;
    }

    /** 恢复资源 */
    public restoreResource(amount: ChaResource): void {
        const finalProperty = this.getFinalProperty();

        this.resource.restore(amount);

        // 限制最大值
        this.resource.hp = Math.min(finalProperty.hp, this.resource.hp);
        this.resource.ammo = Math.min(finalProperty.ammo, this.resource.ammo);
        // MP没有上限限制
    }

    /** 检查是否死亡 */
    public checkDeath(): void {
        if (this.resource.hp <= 0 && !this.dead) {
            this.dead = true;
            this.onDeath();
        }
    }

    /** 死亡处理 */
    private onDeath(): void {
        // 停止移动
        this.moveVelocity = Vector3.zero;
        this.isMoving = false;

        // 清除所有Buff
        BuffManager.instance.clearAllBuffs(this.gameObject);

        // 触发死亡事件
        // TODO: 发送死亡事件给其他系统
    }

    /** 复活 */
    public revive(hp: number = -1): void {
        if (!this.dead) return;

        this.dead = false;

        if (hp < 0) {
            const finalProperty = this.getFinalProperty();
            this.resource.hp = finalProperty.hp;
        } else {
            this.resource.hp = hp;
        }

        // 触发复活事件
        // TODO: 发送复活事件给其他系统
    }

    public update(deltaTime: number): void {
        // 更新无敌时间
        if (this.immuneTime > 0) {
            this.immuneTime -= deltaTime;
        }

        // 更新位置
        if (this.isMoving && this.gameObject.transform) {
            const movement = this.moveVelocity.multiply(deltaTime);
            this.gameObject.transform.translate(movement);
        }

        // 检查死亡
        this.checkDeath();
    }
}