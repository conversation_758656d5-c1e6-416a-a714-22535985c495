import { RankSimpleItemScript } from "./RankSimpleItemScript";
import { RankConst } from "../../../../auto/ConstAuto";
/**
 * 通用排行榜脚本
 */
export class RankSimpleDialogScript extends RankSimpleItemScript {
    /**动态设置列名 */
    static setColNames(rank_key, { col1 = window.iLang.L2_PAI_MING.il(), col2 = window.iLang.L2_WAN_JIA_MING_CHENG.il(), col3 = window.iLang.L2_ZHAN_LI.il(), } = {}) {
        RankSimpleDialogScript.colNameMap[rank_key] = [col1, col2, col3];
    }
    get tempView() {
        return super.tempView;
    }
    get headBox() {
        if (!this.tempView)
            return null;
        return this.tempView.headBox;
    }
    get txtName() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColVal_1;
    }
    /**排行数值 */
    get txtSortVal() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColVal_2;
    }
    get txtColTitle_0() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColTitle_0;
    }
    get txtColTitle_1() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColTitle_1;
    }
    get txtColTitle_2() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColTitle_2;
    }
    onAwake() {
        super.onAwake();
        this.initColNames();
        this.isShowFcRank = false;
    }
    initColNames() {
        let colNames = RankSimpleDialogScript.colNameMap[this.rank_key];
        if (colNames) {
            this.txtColTitle_0 && (this.txtColTitle_0.text = colNames[0]);
            this.txtColTitle_1 && (this.txtColTitle_1.text = colNames[1]);
            this.txtColTitle_2 && (this.txtColTitle_2.text = colNames[2]);
            return;
        }
        switch (this.rank_key) {
            case RankConst.RANK_KEY_ARENA: //竞技场
                this.txtColTitle_1.text = window.iLang.L2_WAN_JIA_MING_CHENG.il();
                this.txtColTitle_2.text = window.iLang.L2_JI_FEN.il();
                break;
            case RankConst.RANK_KEY_FAST_POWER: //实时战力排行榜
                this.txtColTitle_1.text = window.iLang.L2_WAN_JIA_MING_CHENG.il();
                this.txtColTitle_2.text = window.iLang.L2_ZHAN_LI.il();
                break;
            case RankConst.RANK_KEY_MAIN_BATTLE: //主线征战关卡
                this.txtColTitle_1.text = window.iLang.L2_WAN_JIA_MING_CHENG.il();
                this.txtColTitle_2.text = window.iLang.L2_ZHENG_ZHAN_GUAN_QIA.il();
                break;
            case RankConst.RANK_KEY_FAMILY_BOSS: //公会劲敌
                this.txtColTitle_1.text = window.iLang.L2_WAN_JIA_MING_CHENG.il();
                this.txtColTitle_2.text = window.iLang.L2_LEI_JI_SHANG_HAI.il();
                break;
            case RankConst.RANK_KEY_GUANDU_PASS_FAST: //暗黑地牢（官渡之战）
                this.txtColTitle_1.text = window.iLang.L2_WAN_JIA_MING_CHENG.il();
                this.txtColTitle_2.text = window.iLang.L2_CENG_SHU.il();
                break;
            case RankConst.RANK_KEY_LCQS: //神装纷争
                this.txtColTitle_1.text = window.iLang.L2_WAN_JIA_MING_CHENG.il();
                this.txtColTitle_2.text = window.iLang.L2_TONG_GUAN_XING_SHU.il();
                break;
            case RankConst.RANK_KEY_ACTIVITY_TAX: //活动税收排行榜
            case RankConst.RANK_KEY_ACTIVITY_QUICK_PASS: //活动快速挂机排行榜
            case RankConst.RANK_KEY_ACTIVITY_TRAVEL: //限时活动探索排行榜
            case RankConst.RANK_KEY_ACTIVITY_BEHEAD: //限时活动勇闯异境排行榜
            case RankConst.RANK_KEY_ACTIVITY_HUANG_JIN: //限时活动暗黑地牢排行榜
            case RankConst.RANK_KEY_ACTIVITY_DRUM: //限时活动击鼓夺宝排行榜
                this.txtColTitle_1.text = window.iLang.L2_WAN_JIA_MING_CHENG.il();
                this.txtColTitle_2.text = window.iLang.L2_HUO_DONG_JI_FEN.il();
                break;
            case RankConst.RANK_KEY_WORLD_BOSS_ROLE_HURT: //南蛮入侵排行榜
                this.txtColTitle_2.text = window.iLang.L2_SHANG_HAI.il();
                break;
            case RankConst.RANK_KEY_STAGE_COPY:
                this.txtColTitle_2.text = window.iLang.L2_TAN_SUO_JIN_DU.il();
                break;
            // case RankConst.RANK_KEY_ACT_RANK://合服活动战队排行榜
            // 	this.txtColTitle_1.text = "公会名称";
            // 	this.txtColTitle_2.text = window.iLang.L2_HUO_DONG_JI_FEN.il();
        }
    }
}
/**列名 */
RankSimpleDialogScript.colNameMap = {};
