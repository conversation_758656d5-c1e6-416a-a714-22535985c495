{"type": "BaseDialog", "props": {"width": 720, "height": 1280}, "compId": 2, "child": [{"type": "Box", "props": {"y": 389, "x": 22, "width": 676, "var": "topIns", "name": "topIns", "height": 432}, "compId": 22, "child": [{"type": "Image", "props": {"y": 15, "var": "bg", "top": 8, "skin": "common2/bg_top_panel1.png", "sizeGrid": "60,60,60,60", "right": 4, "left": 4, "bottom": 15}, "compId": 23}, {"type": "<PERSON><PERSON>", "props": {"var": "closeBtn", "top": -14, "skin": "common2/btn_close.png", "right": 0, "name": "close"}, "compId": 24}, {"type": "Box", "props": {"width": 400, "top": 4, "height": 48, "centerX": 1}, "compId": 25, "child": [{"type": "Image", "props": {"y": 14, "width": 328, "var": "titleBg", "skin": "common2/bg_top_title1.png", "height": 52, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "compId": 26}, {"type": "Image", "props": {"y": 15, "x": 200, "var": "titleIcon", "anchorY": 0.5, "anchorX": 0.5}, "compId": 27}, {"type": "Label", "props": {"y": 15, "x": 200, "width": 250, "var": "titleNameTxt", "text": "提示", "strokeColor": "#80472d", "stroke": 5, "height": 24, "fontSize": 28, "color": "#fffdf3", "bold": false, "anchorY": 0.5, "anchorX": 0.5, "align": "center"}, "compId": 28}]}, {"type": "Box", "props": {"y": 7, "x": -4, "var": "p_tab", "selectedIndex": 0, "right": 0, "name": "tab", "left": 47, "height": 32, "bottom": 0}, "compId": 29}]}, {"type": "Image", "props": {"y": 470, "x": 64, "width": 593, "visible": false, "var": "txtBg", "skin": "common/content_bg6.png", "sizeGrid": "10,10,10,10", "height": 197}, "compId": 4}, {"type": "Panel", "props": {"y": 477, "x": 65, "width": 587, "var": "contentBox", "height": 184}, "compId": 15, "child": [{"type": "HTMLDivElement", "props": {"y": 4, "x": 18, "width": 560, "var": "content", "height": 171, "runtime": "Laya.HTMLDivElement"}, "compId": 19}]}, {"type": "Image", "props": {"y": 679, "x": 45, "width": 630, "skin": "common/line2.png", "height": 1}, "compId": 34}, {"type": "Box", "props": {"zOrder": 10, "y": 699, "x": 147, "width": 433, "var": "btnBox", "name": "btnBox", "height": 48}, "compId": 5, "child": [{"type": "<PERSON><PERSON>", "props": {"x": 216.5, "width": 160, "var": "okBtn", "skin": "common/btnYellow.png", "name": "ok", "label": "确 定", "centerY": -6, "anchorY": 0.5, "anchorX": 0.5, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "height": 60}, "compId": 7}, {"type": "Label", "props": {"y": 61, "x": 216.5, "width": 720, "var": "txtCountDown", "mouseEnabled": false, "height": 25, "fontSize": 22, "color": "#551511", "bold": false, "anchorX": 0.5, "align": "center"}, "compId": 8}]}, {"type": "TextInput", "props": {"y": 569, "x": 85, "width": 545, "var": "inputLocalConfigDir", "skin": "common/progress5.png", "sizeGrid": "5,5,5,5", "promptColor": "#676767", "prompt": "本地配置表目录,例如:F:\\trunk\\w6d\\config\\trunk\\iex", "height": 29, "fontSize": 20, "color": "#2d2d2c", "align": "center"}, "compId": 38}, {"type": "Label", "props": {"text": "填写本地iex配置表目录,\\n例如: F:/trunk/w6d/config/trunki/ex", "x": 171.939453125, "y": 501}, "compId": 39}], "loadList": ["common2/bg_top_panel1.png", "common2/btn_close.png", "common2/bg_top_title1.png", "common/content_bg6.png", "common/line2.png", "common/btnYellow.png", "common/progress5.png"], "loadList3D": []}