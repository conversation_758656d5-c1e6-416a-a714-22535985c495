import { BaseRankScript } from "../BaseRankScript";
/**合服活动排行榜item */
export class actRankRewardItemScript extends BaseRankScript {
    get tempView() {
        return super.tempView;
    }
    /**排行数值 关卡数*/
    get txtSortVal() {
        if (!this.tempView)
            return null;
        return this.tempView.txtPassNum;
    }
    /**用时*/
    get txtUseTime() {
        if (!this.tempView)
            return null;
        return this.tempView.txtUseTime;
    }
    get headBox() {
        if (!this.tempView)
            return null;
        return this.tempView.headBox;
    }
    get txtName() {
        if (!this.tempView)
            return null;
        return this.tempView.txtName;
    }
    onAwake() {
        super.onAwake();
        this.isShowFcRank = true;
    }
    updateRankItemView(itemVo) {
        super.updateRankItemView(itemVo);
        if (!itemVo)
            return;
        // 用时消耗
        // this.setUseTimeStr(DateUtil.GetHMS(itemVo.getOtherParamByKey(RankOtherParamsConst.USE_TIME)));
        // this.setUseTimeStr(DateUtil.GetHMS(itemVo.getEntityExt(0)));
        this.txtSortVal.text = itemVo.family_level + "";
        this.txtUseTime.text = itemVo.sort_val + "";
        this.txtName.text = itemVo.entity_name + "";
    }
    /**设置用时消耗 */
    setUseTimeStr(text) {
        if (this.txtUseTime) {
            this.txtUseTime.text = text;
        }
    }
}
