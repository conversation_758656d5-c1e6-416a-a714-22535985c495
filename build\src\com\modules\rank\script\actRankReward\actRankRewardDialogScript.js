import { DateUtil } from "../../../../util/DateUtil";
import { RankDataCenter } from "../../data/RankDataCenter";
import { RankOtherParamsConst } from "../../RankOtherParamsConst";
import { actRankRewardItemScript } from "./actRankRewardItemScript";
/**
 * 活动公会排行 我的排行
 */
export class actRankRewardDialogScript extends actRankRewardItemScript {
    onAwake() {
        super.onAwake();
        this.isShowFcRank = false;
    }
    updateMyRankView(rank_key, server_type = 1) {
        super.updateMyRankView(rank_key, server_type);
        //TODO 用时消耗
        let rankVo = RankDataCenter.instance.getRankVoByRankKey(rank_key, server_type);
        let time = 0;
        if (rankVo) {
            time = rankVo.getOtherParamByKey(RankOtherParamsConst.USE_TIME);
        }
        if (time > 0) {
            this.setUseTimeStr(DateUtil.GetHMS(time));
        }
        else {
            this.setUseTimeStr(""); //未上榜 不显示通关时间
        }
    }
}
