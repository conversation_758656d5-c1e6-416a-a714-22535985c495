import { UrlConfig } from "../../../game/UrlConfig";
import { cfg_item } from "../../cfg/vo/cfg_item";
import { ConfigManager } from "../../managers/ConfigManager";
import { p_goods } from "../../proto/common/p_goods";
import { p_item } from "../../proto/common/p_item";
import { GameUtil } from "../../util/GameUtil";
import { DudufuDataCenter } from "../dudufu/data/DudufuDataCenter";
import { ForgeDataCenter } from "../forge/data/ForgeDataCenter";
import { MiscConst } from "../misc_config/MiscConst";
import { GoodsManager } from "../test_bag/GoodsManager";
import { ItemConst } from "./ItemConst";
import { ItemMacro } from "../../auto/ItemMacro";
export class GoodsVO {
    constructor() {
        /**物品数量 */
        this._num = 1;
        /**通过配置生成的p_goods */
        this.isCfgVo = false;
        //物品颜色
        this._color = 0;
        //绑定的养成位置
        this._hero_id = 0;
        //合成目标数量
        this._need_num = -1;
        //合成需要武器数量
        this._need_equip_num = -1;
        //角色的等级
        this.roleLevel = 0;
        this.font_size = 0;
        /**显示物品名称长度  */
        this.showNameLen = 0;
        /** 是否显示已领取 */
        this.isShowReward = false;
        /** 是否显示已过期 */
        this.isShowGuoQi = false;
        /**是否过期才可出售 */
        this.isCheckGuoQiMaiChu = false;
        /**超过该数量显示数字 */
        this.showNum = 1;
        this.isShowPossessNum = false;
        this.ischeckItem = false;
        /**显示玩家拥有的以及剩余的数量 */
        this.isShowCostNum = false;
        this.isAdd = false;
        this._oid = 0;
    }
    /**
         *  通过一个TypeId获取一个GoodsVo
         * @param typeid
         * @param count
         * @return
         */
    static GetVoByTypeId(typeid, count = 1) {
        if (!typeid || !ConfigManager.cfg_itemCache.has(typeid))
            return null;
        return GoodsVO.GetVoByCfgItem(ConfigManager.cfg_itemCache.get(typeid), count);
    }
    /**
         *  通过一个TypeId获取一个GoodsVo 支持倍数
         * @param typeid
         * @param count
         * @return
         */
    static GetVoByTypeIdHaveRate(typeid, count = 1, rate = 1) {
        return GoodsVO.GetVoByCfgItemHaveRate(ConfigManager.cfg_itemCache.get(typeid), count, rate);
    }
    /***一个p_goods 转为 p_item */
    static GetOnePGoodToPItem(gains) {
        let _p_item = new p_item();
        _p_item.type_id = gains.type_id;
        _p_item.num = gains.num;
        return _p_item;
    }
    /***p_goods TO p_item */
    static GetPGoodToPItem(gains) {
        let rewardList = [];
        for (let item of gains) {
            let _p_item = new p_item();
            _p_item.num = item.num;
            _p_item.type_id = item.type_id;
            rewardList.push(_p_item);
        }
        return rewardList;
    }
    /***GoodsVO TO p_item */
    static GetGoodsVOToPItem(gains) {
        let rewardList = [];
        for (let item of gains) {
            let _p_item = new p_item();
            _p_item.num = item.num;
            _p_item.type_id = item.typeId;
            rewardList.push(_p_item);
        }
        return rewardList;
    }
    static GetPItemToVo(item) {
        let cfg = ConfigManager.cfg_itemCache.get(item.type_id);
        if (cfg) {
            return GoodsVO.GetVoByTypeId(item.type_id, item.num);
        }
        return null;
    }
    /***p_item TO GoodsVO */
    static GetPItemToVos(gains) {
        let rewardList = [];
        for (let item of gains) {
            let cfg = ConfigManager.cfg_itemCache.get(item.type_id);
            if (cfg) {
                rewardList.push(GoodsVO.GetVoByTypeId(item.type_id, item.num));
            }
        }
        return rewardList;
    }
    /***p_item2 TO GoodsVO 支持倍数*/
    static GetPItemToVosHaveRate(gains) {
        let rewardList = [];
        for (let item of gains) {
            let cfg = ConfigManager.cfg_itemCache.get(item.type_id);
            if (cfg) {
                rewardList.push(GoodsVO.GetVoByTypeIdHaveRate(item.type_id, item.num, item.rate));
            }
        }
        return rewardList;
    }
    /**
     *  通过一个cfg_item获取一个GoodsVo
     * @param item
     * @param count
     * @return
     */
    static GetVoByCfgItem(item, count = 1) {
        var rebackVO = new GoodsVO();
        rebackVO.item = item;
        rebackVO.isCreateGoodsByItemVo = true;
        rebackVO.num = count;
        return rebackVO;
    }
    /**
     *  通过一个cfg_item获取一个GoodsVo 支持倍数
     * @param item
     * @param count
     * @return
     */
    static GetVoByCfgItemHaveRate(item, count = 1, rate = 1) {
        var rebackVO = new GoodsVO();
        rebackVO.item = item;
        rebackVO.isCreateGoodsByItemVo = true;
        rebackVO.num = count;
        return rebackVO;
    }
    /**
 *  通过一个p_goods获取一个GoodsVo
 * @param typeid
 * @param count
 * @return
 */
    static GetVoByPGoods(p) {
        var rebackVO = new GoodsVO();
        rebackVO.goods = p;
        rebackVO.item = ConfigManager.cfg_itemCache.get(p.type_id);
        return rebackVO;
    }
    /**
 *  通过一个p_goods获取一个GoodsVo
 * @param p_goods
 * @param hero_id
 * @return
 */
    static getVoByEquip(p, hero_id = 1) {
        var rebackVO = new GoodsVO();
        rebackVO.goods = p;
        rebackVO.item = ConfigManager.cfg_itemCache.get(p.type_id);
        rebackVO.hero_id = hero_id;
        return rebackVO;
    }
    /**
    *  传一个p_goods数组获取一个GoodsVo列表
    * @return
    */
    static GetEquipGoodsList(p) {
        let arr = [];
        for (let i = 0; i < p.length; i++) {
            let kind = p[i].kind;
            if (ItemConst.isEquip(kind) || ItemConst.isHeroGodEquip(kind) || ItemConst.isRide(kind) || ItemConst.isQiMou(kind)) {
                let rebackVO = new GoodsVO();
                rebackVO.goods = p[i];
                rebackVO.item = ConfigManager.cfg_itemCache.get(p[i].type_id);
                arr.push(rebackVO);
            }
        }
        return arr;
    }
    /**
   *  传一个p_goods数组获取一个GoodsVo列表
   * @return
   */
    static GetGoodsList(p) {
        let arr = [];
        for (let i = 0; i < p.length; i++) {
            let rebackVO = new GoodsVO();
            rebackVO.goods = p[i];
            rebackVO.item = ConfigManager.cfg_itemCache.get(p[i].type_id);
            arr.push(rebackVO);
        }
        return arr;
    }
    /**
*  传一个字符串获取一个GoodsVo 主要用于配置表的解析
* @param typeid
* @param count
* @return
*/
    static GetGoodsByString(p) {
        let arr = p.split("|");
        let tempId = parseInt(arr[0]);
        let tempCount = parseInt(arr[1]);
        let rebackVO = GoodsVO.GetVoByTypeId(tempId, tempCount);
        return rebackVO;
    }
    /**解析配置指定奖励字段 */
    static parseCfgRewardsByField(cfg, field1, field2, font_size = 0, nameLen = 0) {
        let goodsVoList = [];
        let rewardDatas = GameUtil.parseCfgByField(cfg, field1, field2);
        for (let i = 0; i < rewardDatas.length; ++i) {
            let data = rewardDatas[i].map(Number);
            if (data) {
                let itemId = data[0];
                let itemNum = data[1];
                if (itemId != 0) {
                    let vo = GoodsVO.GetVoByTypeId(itemId, itemNum);
                    vo.showNameLen = nameLen;
                    vo.font_size = font_size;
                    goodsVoList.push(vo);
                }
            }
        }
        return goodsVoList;
    }
    /**根据p_stone获取GoodsVO goods_id单独设置 */
    static GetVoByPStone(p) {
        var rebackVO = new GoodsVO();
        rebackVO.item = ConfigManager.cfg_itemCache.get(p.type_id);
        rebackVO._goods_id = p.id;
        return rebackVO;
    }
    set item(value) {
        this._item = value;
    }
    /**获取物品配置表属性 */
    get item() {
        if (this._item == null) {
            this._item = new cfg_item();
        }
        return this._item;
    }
    /**
     * 装备的套装ID
     */
    get suit_id() {
        if (this._item) {
            let equipCfg = ConfigManager.cfg_equipCache.get(this._item.type_id);
            if (equipCfg) {
                return equipCfg.suit_id;
            }
        }
        return 0;
    }
    set goods(value) {
        this._goods = value;
    }
    /**获取物品p_goods */
    get goods() {
        if (this._goods == null) {
            this._goods = new p_goods();
            this._goods.type_id = this.item.type_id;
            this._goods.kind = this.item.kind;
            this._goods.num = this._num;
        }
        return this._goods;
    }
    /**装备星级 */
    get equipStar() {
        /* if (this._goods == null) {
             return 0;
         }
  
         for (const pkv of this._goods.other_vals) {
             if (pkv.key == GoodsVO.ATTR_TYPE_STAR) {
                 return pkv.val;
             }
         }*/
        return this.star;
    }
    /**装备星级经验 */
    get equipStarExp() {
        if (this._goods == null) {
            return 0;
        }
        for (const pkv of this._goods.other_vals) {
            if (pkv.key == GoodsVO.ARR_TYPE_STAREXP) {
                return pkv.val;
            }
        }
        return 0;
    }
    /**失败率（成功率） */
    get failed_rate() {
        for (const pkv of this._goods.other_vals) {
            if (pkv.key == GoodsVO.RIDE_FAIL_RATE) {
                return pkv.val;
            }
        }
        return 0;
    }
    /**获取物品id */
    get typeId() {
        if (this._goods) {
            return this._goods.type_id;
        }
        if (this._item) {
            return this.item.type_id;
        }
        return 0;
    }
    /**获取物品经验 */
    get exp() {
        /*W7屏蔽
        if (this._goods) {
            return this._goods.exp;
        }
        */
        return 0;
    }
    /**获取物品种类 */
    get kind() {
        if (this._goods) {
            return this._goods.kind || this.item.kind;
        }
        return this.item.kind;
    }
    /**获取星级 */
    get star() {
        /*W7屏蔽
        //if (this.isHeroSoul) {//战魂星级读后端数据
        if (this._goods && this._goods.star != 0) {
            return this._goods.star;
        }
        //}
        */
        return this.item.star;
    }
    /**强化等级 */
    get strength() {
        /*W7屏蔽
        if (this._goods) {
            return this._goods.strength;
        }
       */
        return 0;
    }
    /**附魔等级 */
    get enchant() {
        /*W7屏蔽
        if (this._goods) {
            return this._goods.enchant;
        }
       */
        return 0;
    }
    /**精炼等级 */
    get refine() {
        /*W7屏蔽
        if (this._goods) {
            return this._goods.refine;
        }
       */
        return 0;
    }
    /**刻印等级 */
    get carve() {
        /*W7屏蔽
        if (this._goods) {
            return this._goods.carve;
        }
       */
        return 0;
    }
    // public get cfgCarveSuit(): cfg_equip_carve_suit {
    //     let arr: cfg_equip_carve_suit[] = ConfigManager.cfg_equip_carve_suitCache.get(this.carve);
    //     if (arr != null && arr.length > 1) {
    //         return arr[0];
    //     }
    //     return null;
    // }
    /**物品名称 */
    set name(value) {
        this._name = value;
    }
    /**物品名称 */
    get name() {
        if (this._name != null) {
            return this._name;
        }
        return this.item.name;
    }
    /**获取国家 */
    get nation() {
        return this.item.nation;
    }
    /**获取物品大类型 */
    get bigType() {
        if (this.item) {
            return this.item.big_type;
        }
        return this.item.kind;
    }
    /**获取物品数量 */
    get num() {
        if (this.kind == ItemMacro.ITEM_KIND_LEVEL_REWARDS) {
            let tmepNum = this._num;
            if (this._goods) {
                tmepNum = this.goods.num;
            }
            // let cfg: cfg_item_level = ConfigManager.getCfgItemLevel(this.typeId, DataCenter.myLevel);
            // if (cfg != null) {
            //     let xml: XmlFormatVo = XmlFormatVo.GetVo(cfg.reward_1);
            //     if (xml != null) {
            //         return Number(xml.vlaue1) * tmepNum;
            //     }
            // }
            return 0;
        }
        if (this._goods) {
            this._num = this._goods.num;
        }
        return this._num;
    }
    /**显示的物品数量 (钻石充值比例问题)*/
    get showUINum() {
        if (this.typeId == ItemConst.COST_GOLD) {
            return GameUtil.gold(this.num);
        }
        return MiscConst.getGoodsRateMum(this.typeId) * this.num;
        //return this.num;
    }
    /**是否红装提携*/
    get isFiveHeroResonateEquip() {
        if (this.hero_id == 0) {
            return false;
        }
        return DudufuDataCenter.instance.is_unlock_equip && DudufuDataCenter.instance.checkHeroIsUseFiveHeroResonateEquip(this.hero_id);
    }
    set num(value) {
        if (this._goods) {
            this._goods.num = value;
        }
        else {
            this._num = value;
        }
    }
    /**获取物品数据库唯一标识ID */
    get oid() {
        if (this._goods) {
            return this._goods.id;
        }
        if (this._goods_id) {
            return this._goods_id;
        }
        return this._oid;
    }
    set oid(value) {
        if (this._goods) {
            this._goods.id = value;
        }
        this._oid = value;
    }
    /**获取物品在背包存放位置 */
    get ride_slots() {
        /*W7屏蔽
        if (this._goods) {
            return this._goods.ride_slots;
        }
       */
        return [];
    }
    /**（兵器：阶位）(领主装备：品质) */
    get stage() {
        if (this.item)
            return this.item.stage;
        return 0;
    }
    /**获取颜色 */
    get color() {
        if (this.item) {
            return this.item.color;
        }
        return this._color;
    }
    /**获取特殊值 */
    get prop_value() {
        if (this.item && this.item.prop_value != undefined) {
            return this.item.prop_value;
        }
        return 0;
    }
    set color(value) {
        this._color = value;
    }
    /**获取装备英雄id */
    get hero_id() {
        return this._hero_id;
    }
    /**获取装备英雄id */
    set hero_id(value) {
        this._hero_id = value;
    }
    /**获取图标资源 */
    get skin() {
        if (this.isHeadFrame) {
            return this.headFrameSkin;
        }
        return UrlConfig.getGoodsIcon(this.item); // UrlConfig.GOODS_RES_URL + this.item.icon + ".png";
    }
    /**获取头像框资源 */
    get headFrameSkin() {
        if (!this.item.icon) {
            return "";
        }
        return UrlConfig.getGoodsIcon(this.item);
    }
    /**获取坐骑图标资源 */
    get rideSkin() {
        return UrlConfig.getGoodsRideIcon(this.item.type_id, this.refine); // UrlConfig.GOODS_RES_URL + this.item.icon + ".png";
    }
    /**是否是装备类型 */
    get IsEquip() {
        // if (this.item.kind == 2001 || this.item.kind == 2002 || this.item.kind == 2003 || this.item.kind == 2004) {
        //     return true;
        // }
        // return false;
        return ItemConst.isEquip(this.item.kind);
    }
    //跨服战统帅装备
    get IsCrossrealmWarEquip() {
        // if (this.item.kind == 2001 || this.item.kind == 2002 || this.item.kind == 2003 || this.item.kind == 2004) {
        //     return true;
        // }
        // return false;
        return ItemConst.isCrossrealmWarEquip(this.item.kind);
    }
    /**是否是纹章类型 */
    get IsRide() {
        if (this.item.kind == ItemMacro.ITEM_KIND_BING_FU) {
            return true;
        }
        return false;
    }
    /**是否是神临装备 */
    get IsDivine() {
        if (this.IsEquip && ItemConst.isDivineEquipByColor(this.item.color, this.item.kind)) {
            return true;
        }
        return false;
    }
    /**是否是工坊秘籍 */
    get IsSecretBook() {
        if (this.item.kind == ItemMacro.ITEM_KIND_SECRET_BOOK) {
            return true;
        }
        return false;
    }
    /**是否是英雄类型 */
    get IsHero() {
        if (this.item.kind == ItemMacro.ITEM_KIND_HERO_CARD) {
            return true;
        }
        return false;
    }
    /**是否是领主类型 */
    get IsLord() {
        return this.isRoleEquipNormal;
    }
    /**是否是英雄皮肤类型 */
    get IsHeroSkin() {
        if (this.item.kind == ItemMacro.ITEM_KIND_HERO_FASHION) {
            return true;
        }
        return false;
    }
    /**是否是英雄碎片类型 */
    get isHeroDebris() {
        if (this.item.kind == ItemMacro.ITEM_KIND_HERO_CHIP) {
            return true;
        }
        return false;
    }
    /**是否是通用英雄碎片类型 */
    get isCommonHeroDebris() {
        if (this.item.kind == ItemConst.COST_DEBRIS_STAR) {
            return true;
        }
        return false;
    }
    /**是否是头像框类型 */
    get isHeadFrame() {
        return this.item.kind == ItemMacro.ITEM_KIND_ROLE_PROFILE;
    }
    /**是否是装备碎片 */
    get isEquipDebris() {
        return this.item.kind == ItemMacro.ITEM_KIND_EQUIP_CHIP;
    }
    /**是否普通道具碎片 */
    get isComposeDebris() {
        return ConfigManager.cfg_item_composeCache.has(this.item.type_id) && (this.item.kind != ItemConst.COST_DEBRIS_STAR || this.item.kind != ItemMacro.ITEM_KIND_HERO_CHIP);
    }
    /**是否随机英雄碎片 */
    get isHeroRamdomDebris() {
        return !ConfigManager.cfg_hero_base_chipIdCache.has(this.item.type_id) && (this.item.kind == ItemMacro.ITEM_KIND_HERO_CHIP);
    }
    /**是否是战魂 */
    get isHeroSoul() {
        return ItemConst.isHeroSoul(this.item.kind);
    }
    /**是否是兵器 */
    get isSymbol() {
        return this.item.kind == ItemConst.KIND_SYMBOL && this.item.sub_type == 1;
    }
    /**是否未鉴定兵器 */
    get isUnlook() {
        return this.item.kind == ItemConst.KIND_SYMBOL && this.item.sub_type == 0;
    }
    /**是否兵器灵石 */
    get isStone() {
        return ItemConst.isSymbolGem(this.item.kind);
    }
    /**是否领主装备 */
    get isRoleEquip() {
        return ItemConst.isRoleEquip(this.item.kind);
    }
    /**是否领主普通装备 */
    get isRoleEquipNormal() {
        return ItemConst.isLordEquip(this.item.kind);
    }
    /**是否领主特殊装备（可养成） */
    get isRoleSpecial() {
        return false;
    }
    get isBox() {
        switch (this.item.kind) {
            case ItemConst.COST_FIXED_BOX:
            case ItemConst.COST_RANDOM_BOX:
            case ItemConst.COST_SELECT_BOX:
            case ItemConst.COST_SWEAPON_BOX:
                return true;
            default:
                return false;
        }
    }
    get isGodEquip() {
        switch (this.item.kind) {
            case ItemMacro.ITEM_KIND_GOD_EQUIP_1:
            case ItemMacro.ITEM_KIND_GOD_EQUIP_2:
            case ItemMacro.ITEM_KIND_GOD_EQUIP_3:
            case ItemMacro.ITEM_KIND_GOD_EQUIP_4:
                return true;
            default:
                return false;
        }
    }
    /**获取兵器附加属性 */
    get plus_attrs() {
        /*W7屏蔽
        if (this.isSymbol) {
            return this.goods.plus_attrs;
        }
       */
        return [];
    }
    /**物品评分 */
    get score() {
        if (this.item)
            return this.item.ping_fen;
    }
    /**
     * 客户端通过itemVo配置表来生成p_goods（只有itemVo提供的部分基础属性值），而不用后端传送，用于装备提示
     * @param value
     *
     */
    set isCreateGoodsByItemVo(value) {
        /*W7屏蔽

       if (value && this.item) {
           if (this._goods == null) {
               this._goods = new p_goods();
           }
           this._goods.name = this.item.name;
           this._goods.kind = this.item.kind;
           this._goods.type_id = this.item.type_id;
           this.isCfgVo = true;
       }
       */
    }
    /**获取的物品基本属性*/
    getEquipBaseAttr(hideType = 0) {
        var resultInfo = [];
        var tmpe = [];
        if (this.IsEquip == true || this.IsRide == true || this.isHeroSoul || this.isStone || this.isRoleEquip) {
            tmpe = GameUtil.parseAttrVo(this.item, "ft_attr_");
        }
        for (const vo of tmpe) {
            if (vo.show_type != hideType) {
                resultInfo.push(vo);
            }
        }
        resultInfo.sort(ItemConst.SortAttr);
        return resultInfo;
    }
    /**获取已装备的物品强化属性*/
    getEquipStengthAttr(type) {
        if (this.IsEquip == false) {
            return [];
        }
        let currentAttr = [];
        if (type == ForgeDataCenter.TYPE_FORGE) {
            // let baseConfig: Map<number, cfg_equip_reinforce> = ConfigManager.getEquipReinforce(this.typeId);
            // if (baseConfig != null) {
            //     let equip_cfg: cfg_equip_reinforce = baseConfig.get(this.strength);
            //     if (equip_cfg) {
            //         currentAttr = GameUtil.parseAttrVo(equip_cfg);
            //     }
            // }
        }
        else if (type == ForgeDataCenter.TYPE_REFINE) {
            // let baseConfig: Map<number, cfg_equip_refine> = ConfigManager.getEquiprefine(this.typeId);
            // if (baseConfig) {
            //     let equip_cfg: cfg_equip_refine = baseConfig.get(this.refine);
            //     if (equip_cfg) {
            //         currentAttr = GameUtil.parseAttrVo(equip_cfg);
            //     }
            // }
        }
        else if (type == ForgeDataCenter.TYPE_CARVE) {
            // let baseConfig: Map<number, cfg_equip_carve> = ConfigManager.cfg_equip_carveCache.get(this.typeId);
            // if (baseConfig != null) {
            //     let equip_cfg: cfg_equip_carve = baseConfig.get(this.carve);
            //     if (equip_cfg) {
            //         currentAttr = GameUtil.parseAttrVo(equip_cfg);
            //     }
            // }
        }
        else if (type == ForgeDataCenter.TYPE_ENCHANT) {
            // let baseConfig: Map<number, cfg_equip_enchant> = ConfigManager.cfg_equip_enchant_Cache.get(this.typeId);
            // if (baseConfig != null) {
            //     let equip_cfg: cfg_equip_enchant = baseConfig.get(this.enchant);
            //     if (equip_cfg) {
            //         currentAttr = GameUtil.parseAttrVo(equip_cfg);
            //     }
            // }
        }
        else if (type == ForgeDataCenter.TYPE_STAR) {
            // let equip_cfg: cfg_equip_star = ForgeDataCenter.instance.getCfgStar(this.typeId, this.equipStar);
            // if (equip_cfg) {
            //     currentAttr = GameUtil.parseAttrVo(equip_cfg);
            //     let extAttr: FightAttrVO[] = GameUtil.parseAttrVo(equip_cfg, "exp_attr_");
            //     let progressVal: number = 0;
            //     if (equip_cfg.cost_exp > 0) {
            //         progressVal = Math.floor(this.equipStarExp / equip_cfg.cost_exp * 100) / 100;
            //     }
            //     progressVal = progressVal > 1 ? 1 : progressVal;
            //     for (const curVo of currentAttr) {
            //         for (const nextVo of extAttr) {
            //             if (curVo.attrKey == nextVo.attrKey) {
            //                 curVo.val += Math.floor(nextVo.val * progressVal);
            //             }
            //         }
            //     }
            // }
        }
        return currentAttr;
    }
    //获取物品的总属性
    GetSumAttr() {
        var baseInfo = this.getEquipBaseAttr();
        var refineInfo = [];
        var strengthInfo = [];
        if (this.refine > 0) {
            refineInfo = this.getEquipStengthAttr(ForgeDataCenter.TYPE_REFINE);
        }
        if (this.strength > 0) {
            strengthInfo = this.getEquipStengthAttr(ForgeDataCenter.TYPE_FORGE);
        }
        let obj = {};
        let codeArr = [];
        //base
        for (let i = 0; i < baseInfo.length; i++) {
            let vo = baseInfo[i];
            obj[vo.code] = vo;
            codeArr.push(vo.code);
        }
        //refine
        for (let i = 0; i < refineInfo.length; i++) {
            let vo = refineInfo[i];
            if (obj[vo.code] != null) {
                let vo2 = obj[vo.code];
                vo2.val += vo.val;
            }
            else {
                obj[vo.code] = vo;
                codeArr.push(vo.code);
            }
        }
        //forge
        for (let i = 0; i < strengthInfo.length; i++) {
            let vo = strengthInfo[i];
            if (obj[vo.code] != null) {
                let vo2 = obj[vo.code];
                vo2.val += vo.val;
            }
            else {
                obj[vo.code] = vo;
                codeArr.push(vo.code);
            }
        }
        //汇总
        let arr = [];
        for (let i = 0; i < codeArr.length; i++) {
            let vo = obj[codeArr[i]];
            arr.push(vo);
        }
        return arr;
    }
    /**合成目标数量 */
    get need_num() {
        if (this._need_num == -1) {
            let vo = ConfigManager.cfg_item_composeCache.get(this.item.type_id);
            if (vo) {
                this._need_num = vo.need_num;
            }
            else {
                this._need_num = 0;
            }
        }
        return this._need_num;
    }
    /**工坊合成武器的武器所需数量 */
    get need_equip_num() {
        if (this._need_equip_num == -1) {
            let vo = ConfigManager.cfg_equip_composeCache.get(this.item.type_id);
            let tmep = GoodsManager.instance.GetGoodsNumByTypeId(this.item.type_id);
            if (vo) {
                this._need_equip_num = tmep;
            }
            else {
                this._need_equip_num = 0;
            }
        }
        return this._need_equip_num;
    }
    /**当前拥有该物品的数量 */
    get has_count() {
        let count = GoodsManager.instance.GetGoodsNumByTypeId(this.item.type_id);
        return count;
    }
}
/**装备星级 */
//public static ATTR_TYPE_STAR: number = 1001;
/**装备星级经验 */
GoodsVO.ARR_TYPE_STAREXP = 1002;
/**坐骑升星的失败率 */
GoodsVO.RIDE_FAIL_RATE = 1003;
