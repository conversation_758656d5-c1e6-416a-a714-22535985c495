{"type": "BaseDialog", "props": {"width": 720, "height": 1280}, "compId": 2, "child": [{"type": "TopInsAuto", "props": {"y": 30, "var": "topIns", "right": 0, "left": 0, "height": 1200, "runtime": "com/modules/common/TopInsAuto.ts"}, "compId": 14}, {"type": "Box", "props": {"y": 82, "x": 41, "width": 639, "var": "listBox", "name": "listBox", "height": 995}, "compId": 32, "child": [{"type": "VScrollBar", "props": {"skin": "common/vscroll.png", "name": "bar"}, "compId": 33}]}, {"type": "TextInput", "props": {"y": 1092, "x": 134, "width": 158, "var": "inputOffsetX", "stroke": 2, "promptColor": "#e5e5e5", "prompt": "偏移x", "height": 34, "color": "#ffffff", "visible": false}, "compId": 34}, {"type": "TextInput", "props": {"y": 1092, "x": 353, "width": 158, "var": "inputOffsetY", "stroke": 2, "promptColor": "#e5e5e5", "prompt": "偏移y", "height": 34, "color": "#ffffff", "visible": false}, "compId": 35}, {"type": "Label", "props": {"y": 1098, "text": "偏移X:", "strokeColor": "#030303", "stroke": 2, "langTag": "MING_CHENG_SHI_SHEN_ME", "fontSize": 22, "color": "#00FF7F", "centerX": -279, "visible": false}, "compId": 36}, {"type": "Label", "props": {"y": 1098, "text": "偏移Y", "strokeColor": "#030303", "stroke": 2, "langTag": "MING_CHENG_SHI_SHEN_ME", "fontSize": 22, "color": "#00FF7F", "centerX": -63, "visible": false}, "compId": 37}, {"type": "TextInput", "props": {"y": 1137, "x": 55, "width": 416, "var": "inputLocalIexPath", "stroke": 2, "promptColor": "#e5e5e5", "prompt": "本电脑cfg_fish_resources.iex地址", "height": 34, "color": "#ffffff"}, "compId": 38}, {"type": "<PERSON><PERSON>", "props": {"y": 1157, "x": 577, "width": 199, "var": "btnSaveIex", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 99.5, "label": "保存到本地配置文件", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 39}], "loadList": ["res/base/TopInsAuto.scene", "common/vscroll.png", "common/btnYellow.png"], "loadList3D": []}