
import { Event } from "laya/events/Event";
import { UrlConfig } from "../../../../game/UrlConfig";
import { PanelIconMacro } from "../../../auto/PanelConstAuto";
import { com } from "../../../ui/layaMaxUI";
import { DialogNavShow } from "../../BaseDialog";
import { IUIListItem } from "../../baseModules/IUListItem";
import { UIList } from "../../baseModules/UIList";
import { UIListItemData } from "../../baseModules/UIListItemData";
import { UITabData } from "../../baseModules/UITabData";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { ModuleCommand } from "../../ModuleCommand";
import { AuctionHouseDataCenter, EAuctionHouseOpType } from "../data/AuctionHouseDataCenter";
import { TipsUtil } from "../../../util/TipsUtil";
import { p_lord_treasure } from "../../../proto/common/p_lord_treasure";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { Handler } from "laya/utils/Handler";
import { ConfigManager } from "../../../managers/ConfigManager";
import { ColorUtil } from "../../../util/ColorUtil";
import { MatchConst } from "../../../auto/ConstAuto";
import { LordDataCenter } from "../../Lord/data/LordDataCenter";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { StringUtil } from "../../../util/StringUtil";
import { GoodsManager } from "../../test_bag/GoodsManager";
import { AuctionHouseListItem } from "../view/AuctionHouseListItem";
import { LordTreasureDataCenter } from "../../LordTreasure/data/LordTreasureDataCenter";
import { cfg_auction_house } from "../../../cfg/vo/cfg_auction_house";
import { m_auction_house_op_tos } from "../../../proto/line/m_auction_house_op_tos";



export default class AuctionHouseListDialog extends com.ui.res.auctionHouse.AuctionHouseListDialogUI {
    constructor() {
        super();
    }

    private itemUIList: UIList;
    private typeId: number = 0;

    private param: any;

    private _goodsItem: GoodsItem;

    private buyNum: number = 1;
    private maxCanBuyNum: number = 0;
    private minCanBuyNum: number = 0;

    /**一口价 */
    private fiexedPrice: number = 0;
    /**竞拍价 */
    private bidPrice: number = 0;

    private _itemVO: any;//物品goodsVO或者宝物p_lord_treasure

    /**紫币 */
    private costId: number = MiscConstAuto.auction_house_use_item_id;


    initUI(): void {
        this.navShow = DialogNavShow.NONE;

        this.topIns.titleNameTxt.text = "上架拍卖品";

        this._goodsItem = this.goodsItem;
        this._goodsItem.mouseEnabled = false;

        this.itemUIList = UIList.SetUIList(this, this.listBox, AuctionHouseListItem, this.onSelectList, false);
        this.itemUIList.SetRepeat(5, 4);
        this.itemUIList.SetSpace(25, 40);

        this.itemUIList.checkSelectHandler = Handler.create(this, this.checkListCanSelect, null, false);

        this.lbDeposit.text = StringUtil.Format("押金{0}紫币", MiscConstAuto.auction_house_deposit[1]);
        this.lbFee.text = StringUtil.Format("手续费{0}%", 10000 / MiscConstAuto.auction_house_commission);

        this.costId = MiscConstAuto.auction_house_use_item_id;

        this.icon1.skin = UrlConfig.getGoodsIconByTypeId(this.costId);
        this.icon2.skin = UrlConfig.getGoodsIconByTypeId(this.costId);

        this._goodsItem.isAdd = true;
        this._goodsItem.showAdd(true);
        this._goodsItem.pos(287, 189);

    }

    private onSelectList(itemData: UITabData): void {
        if (!itemData) { return }

        this._itemVO = itemData.data;
        if (this._itemVO instanceof GoodsVO) {
            this.typeId = this._itemVO.typeId;
        } else {
            this.typeId = this._itemVO.type_id;

        }

        this.refreshCanBuyNum();
        this.refreshItemView();

        this.buyNum = this.minCanBuyNum;
        this.inputNum.text = this.buyNum.toString();

        this.initPriceInput();
    }

    addClick(): void {
        this.addOnClick(this, this.btnList, this.onClickBtnList);

        this.addOnClick(this, this.btnDel, this.onChangeBuyNum, Event.CLICK, [1], 0);
        this.addOnClick(this, this.btnAdd, this.onChangeBuyNum, Event.CLICK, [2], 0);
    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.GOODS_UPDATE, this, this.refrehUpdateGoods);
        this.addEventListener(ModuleCommand.UPDATE_LORD_INFO, this, this.refreshUIList);
        this.addEventListener(ModuleCommand.UPDATE_LORD_TREASURE_OP, this, this.refreshUIList);
        this.addEventListener(ModuleCommand.UPDATE_AUCTION_HOUSE_OP, this, this.refreshView);


        this.inputNum.on(Event.INPUT, this, this.onInputChange);
        this.inputFixedPrice.on(Event.INPUT, this, this.onInputFixedPrice);
        this.inputBidPrice.on(Event.INPUT, this, this.onInputBidPrice);
    }


    onOpen(param: any): void {
        this.param = param;

        this.inputNum.text = this.buyNum.toString();

        this.refreshUIList();

    }

    onInputChange() {
        if (this.maxCanBuyNum <= 0) {
            return;
        }


        let text = this.inputNum.text;
        this.buyNum = parseInt(text) || 1;

        if (this.buyNum < this.minCanBuyNum) {
            this.buyNum = this.minCanBuyNum;
        }

        if (this.buyNum > this.maxCanBuyNum) {
            this.buyNum = this.maxCanBuyNum;
        }

        this.inputNum.text = this.buyNum.toString();

        this.initPriceInput();
    }

    onInputFixedPrice() {
        if (this.maxCanBuyNum <= 0) {
            return;
        }

        let text = this.inputFixedPrice.text;
        this.fiexedPrice = parseInt(text) || 0;

        let cfg: cfg_auction_house = CfgCacheMapMgr.cfg_auction_houseCache.get(this.typeId);
        let max_fixed_price = cfg.max_fixed_price;
        let min_fixed_price = cfg.min_fixed_price;

        if (this.fiexedPrice < min_fixed_price * this.buyNum) {
            this.fiexedPrice = min_fixed_price * this.buyNum;
        }

        if (this.fiexedPrice > max_fixed_price * this.buyNum) {
            this.fiexedPrice = max_fixed_price * this.buyNum;
        }

        this.inputFixedPrice.text = this.fiexedPrice.toString();


        let _bid_price = cfg.initial_bidding;//改了一口价后，把竞拍价初始化
        this.bidPrice = _bid_price * this.buyNum;

        this.inputBidPrice.text = this.bidPrice.toString();

    }


    onInputBidPrice() {
        if (this.maxCanBuyNum <= 0) {
            return;
        }

        let text = this.inputBidPrice.text;
        this.bidPrice = parseInt(text) || 0;

        let cfg: cfg_auction_house = CfgCacheMapMgr.cfg_auction_houseCache.get(this.typeId);
        let max_bid_price = cfg.max_fixed_price;
        let min_bid_price = cfg.initial_bidding;

        if (this.bidPrice < min_bid_price * this.buyNum) {
            this.bidPrice = min_bid_price * this.buyNum;
        }


        if (this.bidPrice >= this.fiexedPrice * this.buyNum) {
            this.bidPrice = this.fiexedPrice * this.buyNum - 1;
        }

        this.inputBidPrice.text = this.bidPrice.toString();

    }


    initPriceInput() {
        let cfg: cfg_auction_house = CfgCacheMapMgr.cfg_auction_houseCache.get(this.typeId);
        let _fixed_price = cfg.max_fixed_price;
        let _bid_price = cfg.initial_bidding;


        this.fiexedPrice = _fixed_price * this.buyNum;

        this.bidPrice = _bid_price * this.buyNum;


        this.inputBidPrice.text = this.bidPrice.toString();
        this.inputFixedPrice.text = this.fiexedPrice.toString();
    }

    /**改变购买数量 */
    onChangeBuyNum(type: number): void {
        if (this.maxCanBuyNum <= 0) {
            return;
        }

        switch (type) {
            case 1: //减1
                this.buyNum -= 1;
                break;
            case 2: //加1
                this.buyNum += 1;
                break;
            case 3: //
                this.buyNum = this.maxCanBuyNum;
                break;
            case 4: //加1
                this.buyNum = 1;
                break;

        }
        if (this.buyNum < this.minCanBuyNum) {
            this.buyNum = this.minCanBuyNum;
        }
        if (this.buyNum > this.maxCanBuyNum) {
            this.buyNum = this.maxCanBuyNum;
        }

        if (this.inputNum) {
            this.inputNum.text = this.buyNum?.toString();
        }

        this.initPriceInput();
    }

    refrehUpdateGoods() {
        this.refreshUIList();
        this.refreshCanBuyNum();

    }

    refreshView(param: m_auction_house_op_tos) {
        if (param.op_type == EAuctionHouseOpType.LIST) {
            this.initView();
            this.refreshUIList();
        }

    }

    refreshCanBuyNum() {
        if (this._itemVO) {
            if (this._itemVO instanceof GoodsVO) {
                let cfg = CfgCacheMapMgr.cfg_auction_houseCache.get(this._itemVO.typeId);

                this.maxCanBuyNum = Math.min(this._itemVO.num, cfg.listing_max_num);

                this.minCanBuyNum = Math.min(this._itemVO.num, cfg.listing_min_num);
            } else {
                this.maxCanBuyNum = 1;
                this.minCanBuyNum = 1;
            }
        }
    }


    refreshUIList() {
        let list = AuctionHouseDataCenter.instance.getAllCanAuctionItemList();

        this.itemUIList.array = [];
        this.itemUIList.array = list;

    }

    initUIListIndex(): void {
        if (!this.itemUIList.array) { return }

        if (this.param && this.param.child_id) {
            let idx = this.itemUIList.getIdToIndex(this.param.child_id);
            this.itemUIList.selectedIndex = idx;
        } else {
            this.itemUIList.selectedIndex = 0;
        }
    }

    refreshItemView() {
        if (this.typeId > 0) {
            this._goodsItem.isAdd = false;
            let cfgItem = CfgCacheMapMgr.cfg_itemCache.get(this.typeId);
            this._goodsItem.SetData(GoodsVO.GetVoByTypeId(this.typeId));

            this.lbName.text = cfgItem.name;
            this.boxInfo.visible = true;

            this._goodsItem.pos(56, 100);

        } else {
            this._goodsItem.isAdd = true;
            this._goodsItem.showAdd(true);
            this._goodsItem.pos(287, 189);
            this.lbName.text = "";
            this.boxInfo.visible = false;
            this._goodsItem.Clean();
        }
    }

    refreshBidAndFiexedPriceView() {

    }


    onClickBtnList() {
        AuctionHouseDataCenter.instance.auctionHouseOpTOS(
            EAuctionHouseOpType.LIST,
            0,
            this._itemVO?.oid || 0,
            this.bidPrice,
            this.fiexedPrice,
            this.buyNum,
            this._itemVO?.id || 0,

        )
    }

    checkListCanSelect(listItemData: UIListItemData): boolean {
        if (listItemData.data && listItemData.data instanceof GoodsVO) {
            let itemVO = listItemData.data;

            let cfg = CfgCacheMapMgr.cfg_auction_houseCache.get(itemVO.typeId);
            let minBuyNum = cfg.listing_min_num;

            if (itemVO.num < minBuyNum) {
                TipsUtil.showTips(StringUtil.Format("该道具小于最小上架数量{0}，不可上架", minBuyNum));
                return false;
            } else {
                return true;
            }

        }


        if (listItemData.data && listItemData.data instanceof p_lord_treasure) {
            let data = listItemData.data as p_lord_treasure;

            let isLineUp: boolean = false;//是否上阵
            for (const [k1, v1] of LordDataCenter.instance.lordLineUpMapList) {
                for (const [k2, v2] of v1) {
                    if (v2.lord_treasure == data.id) {//选出已穿戴的
                        isLineUp = true;
                        break;
                    }
                }
            }

            if (isLineUp) {
                let name = CfgCacheMapMgr.cfg_itemCache.get(data.type_id).name;

                let context = ColorUtil.GetColorHtml(name, ColorUtil.FONT_RED)
                    + "已在部分玩法中上阵，选择此领主宝物并操作成功后，会自动将该宝物从阵型中下阵，仍要继续选择吗？";

                TipsUtil.showDialog(this, context, window.iLang.L2_TI_SHI.il(), () => {
                    this.itemUIList.selectedIndex = listItemData.index;

                }, {
                    okName: window.iLang.L2_RENG_RAN_XUAN_ZE.il(),
                    cancelName: window.iLang.L_CANCEL.il(),
                });

            } else {
                return true;
            }

        } else {
            return true;
        }
    }

    initView(): void {
        this.typeId = 0;
        this.buyNum = 0;
        this.maxCanBuyNum = 0;
        this.minCanBuyNum = 0;

        this.fiexedPrice = 0;
        this.bidPrice = 0;

        this.refreshItemView();
    }

}


