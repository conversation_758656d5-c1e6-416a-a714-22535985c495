# Quick start guide

The Version Control package will allow you to use either Collaborate or Plastic SCM for Unity for your projects in the Unity Editor. The Editor does not allow both services at the same time. For information on how to switch between both services, see [Switch between Collaborate and Plastic SCM](SwitchCollabAndPlastic.md).

**Note** : For information on migrating projects from Collaborate to Plastic SCM, see [Migration from Collaborate to Plastic SCM](MigrateCollab.md).

* Collaborate will give you the ability to share projects amongst small teams who want an easy way to sync their projects without navigating the complexity of version control.
* Plastic SCM for Unity (beta) is an integration of Plastic SCM in Unity (actively developed) that will abstract version control complexity, like Collaborate. It will also enable you to work collaboratively on more complex projects by providing additional VCS features such as branching, locking, merging, and a standalone GUI.

The Version Control package follows the Unity support schedule. Currently, supported versions are 2019.4LTS, 2020.3LTS, 2021.1, and 2021.2.

* [Getting started with Collaborate](StartWithCollab.md)
* [Getting started with Plastic SCM for Unity (beta)](StartPlasticForUnity.md)
