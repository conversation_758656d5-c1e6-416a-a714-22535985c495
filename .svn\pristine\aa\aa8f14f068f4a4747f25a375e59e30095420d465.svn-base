
import { Handler } from "laya/utils/Handler";
import { UrlConfig } from "../../../../game/UrlConfig";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { PanelIconMacro } from "../../../auto/PanelConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { p_auction_house_goods } from "../../../proto/common/p_auction_house_goods";
import { com } from "../../../ui/layaMaxUI";
import { StringUtil } from "../../../util/StringUtil";
import { DialogNavShow } from "../../BaseDialog";
import UITab from "../../baseModules/UITab";
import { UITabData } from "../../baseModules/UITabData";
import { CommonBottomTabItem } from "../../common/CommonBottomTabItem";
import CommonCostItem1 from "../../common/CommonCostItem1";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { ModuleCommand } from "../../ModuleCommand";
import { GoodsManager } from "../../test_bag/GoodsManager";
import { AuctionHouseDataCenter, EAuctionHouseOpType } from "../data/AuctionHouseDataCenter";
import { Event } from "laya/events/Event";
import { m_auction_house_info_toc } from "../../../proto/line/m_auction_house_info_toc";
import { m_auction_house_op_tos } from "../../../proto/line/m_auction_house_op_tos";
import { m_auction_house_op_toc } from "../../../proto/line/m_auction_house_op_toc";

export default class AuctionHouseGetDialog extends com.ui.res.auctionHouse.AuctionHouseGetDialogUI {
    constructor() {
        super();
    }

    private info: p_auction_house_goods;

    private _goodsItem: GoodsItem;

    private _goodsVO: GoodsVO;

    private _id: number = 0;

    /**区分卖出物品还是流拍物品 */
    private type: number = 0;

    /**紫币 */
    private costId: number = MiscConstAuto.auction_house_use_item_id;

    initUI(): void {
        this.navShow = DialogNavShow.NONE;
     

        this._goodsItem = this.goodsItem;
        this._goodsItem.mouseEnabled = false;

        this.costId = MiscConstAuto.auction_house_use_item_id;

        this.icon1.skin = UrlConfig.getGoodsIconByTypeId(this.costId);
        this.icon2.skin = UrlConfig.getGoodsIconByTypeId(this.costId);
        this.icon3.skin = UrlConfig.getGoodsIconByTypeId(this.costId);
        this.icon4.skin = UrlConfig.getGoodsIconByTypeId(this.costId);


    }


    addClick(): void {
        this.addOnClick(this, this.btnGet, this.onClickBtnGet);

    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.UPDATE_AUCTION_HOUSE_OP, this, this.refreshView);
    }

    refreshView(param: m_auction_house_op_toc) {
        // if (param && param.op_type == EAuctionHouseOpType.UOPDATE_NEW &&
        //     param.auction_goods && this._id > 0 && param.auction_goods.id == this._id) {


        // }

    }

    onOpen(param: any): void {
        if (!param) {
            this.close();
            return;
        }

        this.info = param.info;
        this.type = param.type;
        this._goodsVO = GoodsVO.GetVoByPGoods(this.info.goods);

        this._id = this.info.id;


        this.refreshItemView();

    }


    refreshItemView() {
        if (this._goodsVO) {
            this._goodsItem.SetData(this._goodsVO);
            this.lbName.text = this._goodsVO.name;


            this.inputBidPrice.text = this.info.bidding_price + "";

            // if(this.type==e)

            // this.inputFixedPrice.text = this.info.buyout_price + "";

            // let fee= MiscConstAuto.auction_house_commission

            // this.lbTipsPriceNum.text = + "";

        } else {
            this.lbName.text = "";
            this._goodsItem.Clean();

            this.inputBidPrice.text = "";

            this.inputFixedPrice.text = "";

            this.lbTipsPriceNum.text =""
        }
    }


    onClickBtnGet(): void {
        AuctionHouseDataCenter.instance.auctionHouseOpTOS(
            // EAuctionHouseOpType.BUY_FIXED,
            // 0,
            // this._id,
            // 0,

        )


    }


}
