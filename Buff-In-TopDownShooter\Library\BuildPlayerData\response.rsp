-a="E:\unity learn\Buff In ARPG\Library\PlayerScriptAssemblies\Assembly-CSharp.dll"
-a="E:\unity learn\Buff In ARPG\Library\PlayerScriptAssemblies\UnityEngine.TestRunner.dll"
-a="E:\unity learn\Buff In ARPG\Library\PlayerScriptAssemblies\Unity.Timeline.dll"
-a="E:\unity learn\Buff In ARPG\Library\PlayerScriptAssemblies\Unity.TextMeshPro.dll"
-a="E:\unity learn\Buff In ARPG\Library\PlayerScriptAssemblies\UnityEngine.UI.dll"
-a="E:\unity learn\Buff In ARPG\Library\PackageCache\com.unity.nuget.newtonsoft-json@2.0.0\Runtime\AOT\Newtonsoft.Json.dll"
-a="E:\unity learn\Buff In ARPG\Library\PackageCache\com.unity.nuget.newtonsoft-json@2.0.0\Runtime\Portable\Newtonsoft.Json.dll"
-a="E:\unity learn\Buff In ARPG\Library\PackageCache\com.unity.nuget.newtonsoft-json@2.0.0\Runtime\Newtonsoft.Json.dll"
-a="E:\unity learn\Buff In ARPG\Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom\nunit.framework.dll"
-s="E:\unity editor\2020.3.15f2c1\Editor\Data\Managed\UnityEngine"
-s="E:\unity learn\Buff In ARPG\Library\PlayerScriptAssemblies"
-s="E:\unity learn\Buff In ARPG\Library\PackageCache\com.unity.nuget.newtonsoft-json@2.0.0\Runtime\AOT"
-s="E:\unity learn\Buff In ARPG\Library\PackageCache\com.unity.nuget.newtonsoft-json@2.0.0\Runtime\Portable"
-s="E:\unity learn\Buff In ARPG\Library\PackageCache\com.unity.nuget.newtonsoft-json@2.0.0\Runtime"
-s="E:\unity learn\Buff In ARPG\Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom"
-s="E:\unity editor\2020.3.15f2c1\Editor\Data\Managed\UnityEngine"
-s="E:\unity editor\2020.3.15f2c1\Editor\Data\NetStandard\ref\2.0.0"
-s="E:\unity editor\2020.3.15f2c1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard"
-s="E:\unity editor\2020.3.15f2c1\Editor\Data\NetStandard\Extensions\2.0.0"
-s="E:\unity editor\2020.3.15f2c1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx"
-o="E:\unity learn\Buff In ARPG\Library\BuildPlayerData\Player"
-rn="RuntimeInitializeOnLoads.json"
-tn="TypeDb-All.json"
