# Getting started with Plastic SCM for Unity (beta)

**Note**: Plastic SCM for Unity (beta) is available via the Version Control package in the Unity Package Manager.

Plastic SCM for Unity (beta) will allow you to use Plastic SCM directly in Unity.

**Important**: Enabling the Plastic SCM for Unity package will disable the standard VCS integration option in the Project Settings (this option will be removed from future Editor releases), which was an older integration previously offered in the Editor. Features available in the standard integration have been ported to the Plastic SCM for Unity package. This is the version that will be actively developed and maintained going forward.

Plastic SCM Cloud Edition is **free for up to 3 users and up to 5 GB**. To learn more about Plastic SCM, see [Version control for game development](https://unity.com/products/plastic-scm?_ga=2.258676808.391394560.1617648415-1935282152.1592839733).

* To start with a new Plastic SCM repository for your project, see [Getting started with a New Plastic SCM repository](NewPlasticRepo.md)
* To start from an existing Plastic SCM repository, see [Getting started with an existing Plastic SCM repository](ExistingPlasticRepo.md)