import { PanelIconMacro } from "../../../auto/PanelConstAuto";
import { com } from "../../../ui/layaMaxUI";
import { ColorUtil } from "../../../util/ColorUtil";
import { DateUtil } from "../../../util/DateUtil";
import { GameUtil } from "../../../util/GameUtil";
import { StringUtil } from "../../../util/StringUtil";
import { UIList } from "../../baseModules/UIList";
import { GoodsVO } from "../../goods/GoodsVO";
import { EAuctionHouseOpType } from "../data/AuctionHouseDataCenter";
export default class AuctionHouseActiveDialog extends com.ui.res.auctionHouse.AuctionHouseActiveDialogUI {
    constructor() {
        super();
    }
    initUI() {
        this.navShow = 0 /* NONE */;
        this.topIns.titleNameTxt.text = "提示";
        this.itemUIList = UIList.SetUIList(this, this.list, AuctionHouseActiveItem);
        this.itemUIList.SetSpace(1, 20);
        this.itemUIList.SetRepeat(1, 5);
        this.itemUIList.isBoxCenter = true;
    }
    addClick() {
        this.addOnClick(this, this.btnClose, this.close);
        this.addOnClick(this, this.btnOk, this.onClickBtnOk);
    }
    addEvent() {
    }
    onOpen(param) {
        if (!param) {
            return;
        }
        this.itemUIList.array = param.list;
    }
    onClickBtnOk() {
        GameUtil.gotoSystemPanelById(PanelIconMacro.AUCTION_HOUSE, EAuctionHouseOpType.VIEW_AUCTION_INFO);
        this.close();
    }
}
export class AuctionHouseActiveItem extends com.ui.res.auctionHouse.AuctionHouseActiveItemUI {
    constructor() {
        super();
    }
    UpdateItem(itemData, select) {
        if (!itemData.data) {
            return;
        }
        this.data = itemData.data;
        let type_id = this.data.key;
        let time = this.data.val;
        this.lbTime.text = DateUtil.GetMDHM(time * 1000);
        let goods = GoodsVO.GetVoByTypeId(type_id);
        this.htmlDesc.innerHTML = StringUtil.Format("当前您的【{0}】竞拍价格已被超过，是否前往查看？", ColorUtil.GetColorHtml(goods.name, "#e25b18"));
    }
    Clean() {
    }
    initUI() {
        this.SetUIHTMLDiv(this.htmlDesc, 24, "#855033");
    }
}
