import { Point } from "laya/maths/Point";
import { MatchConst } from "../../../auto/ConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { cfg_world_secret_treasure_boss } from "../../../cfg/vo/cfg_world_secret_treasure_boss";
import { cfg_world_secret_treasure_boss_group } from "../../../cfg/vo/cfg_world_secret_treasure_boss_group";
import { com } from "../../../ui/layaMaxUI";
import { GameUtil } from "../../../util/GameUtil";
import { DialogNavShow } from "../../BaseDialog";
import { UIList } from "../../baseModules/UIList";
import { FightDataCenter } from "../../fight/data/FightDataCenter";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import WorldTreasureBossTeamItem from "../view/WorldTreasureBossTeamItem";
import { p_world_secret_treasure_gdn } from "../../../proto/common/p_world_secret_treasure_gdn";
import { WorldTreasureDataCenter, WorldTreasureFightType } from "../data/WorldTreasureDataCenter";
import { StringUtil } from "../../../util/StringUtil";
import { ModuleCommand } from "../../ModuleCommand";
import TopInsAuto from "../../common/TopInsAuto";

export default class WorldTreasureBossBattleDialog extends com.ui.res.worldTreasure.WorldTreasureBossBattleDialogUI {
    
    private teamList:UIList;
    private goodsList:UIList;
    private goodsExtraList:UIList;
    private arg_param:number [] = [];
    private bossInfo:p_world_secret_treasure_gdn;
    private topPanel:TopInsAuto;
    constructor(){
        super();
        this.navShow = DialogNavShow.NONE;
    }
    public addEvent(): void {
        this.addEventListener(ModuleCommand.UPDATE_TREASURE_BOSS_EXTRA_TIMES,this,this.setTimes)
    }
    public addClick(): void {
        this.addOnClick(this,this.startFightBtn,this.startFight);
    }
    public initUI(): void {
        this.teamList = UIList.SetUIList(this,this.monsterTeamBox,WorldTreasureBossTeamItem);
        this.teamList.SetRepeat(1,3);
        this.teamList.SetSpace(0,10);
        this.teamList.isBoxCenter = true;

        this.goodsList = UIList.SetUIList(this,this.goodBox1,GoodsItem);
        this.goodsList.SetRepeat(5,1);
        this.goodsList.SetSpace(15);
        this.goodsList.SetItemOffset(10,10);
        this.goodsList.SetPadding(10);
        this.goodsList.isBoxCenter = true;

        this.goodsExtraList = UIList.SetUIList(this,this.goodBox2,GoodsItem);
        this.goodsExtraList.SetRepeat(5,1);
        this.goodsExtraList.SetSpace(15);
        this.goodsExtraList.SetItemOffset(10,10);
        this.goodsExtraList.SetPadding(10);
        this.goodsExtraList.isBoxCenter = true;

        this.topPanel = this.topIns;
        this.topPanel.setTitle("宝藏守护者")
        

    }
    setTimes(){
        const times = WorldTreasureDataCenter.instance.remainAddRewardTimes;
        this.lbExtraTimes.text = StringUtil.FormatArr("今日剩余额外产出次数：{0}",[times])
    }
    public onOpen(param: {bossId:number ,playerPos:Point}): void {
        if (!param) {
            return;
        }
        this.bossInfo = WorldTreasureDataCenter.instance.treasureBossMap.get(param.bossId);
        if (!this.bossInfo ) {
            return;
        }
        const pos = param.playerPos;
        //保存玩家战斗时的位置
        this.arg_param = [WorldTreasureFightType.BOSS,this.bossInfo.type_id,pos.x,pos.y];
        const cfgBossGroup:cfg_world_secret_treasure_boss_group = CfgCacheMapMgr.cfg_world_secret_treasure_boss_groupCache.get(this.bossInfo.type_id);
        if (!cfgBossGroup) {
            return;
        }
        this.teamList.array = GameUtil.parseCfgByField(cfgBossGroup, "group_id_", { ingoreEmpty: true }).map(Number) || [];
        const cfgboss:cfg_world_secret_treasure_boss = CfgCacheMapMgr.cfg_world_secret_treasure_bossCache.get(cfgBossGroup.element_id);
        if (!cfgboss) {
            return;
        }
        this.goodsList.array = GameUtil.parseRewards(cfgboss, "drop_item_");
        this.goodsExtraList.array = this.parseExtraReward(cfgboss);
        this.setTimes();

    }
    private parseExtraReward(cfgboss:cfg_world_secret_treasure_boss):GoodsVO[]{
        const itemStrlist:string[] = cfgboss.show_drop_group.split(",");
        let itemList:GoodsVO[] = [];
        for (const item of itemStrlist) {
            itemList.push(GoodsVO.GetGoodsByString(item));
            
        }
        return itemList;
    }
    startFight(){
        let lineUpVo = WorldTreasureDataCenter.instance.createLineUpVo();
        FightDataCenter.instance.m_fight_start_tos(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE,this.bossInfo.id,{
            target_arg:this.arg_param,
            lineUpVo:lineUpVo
        });
    }
}