{"root": [{"name": "Assembly-CSharp", "path": "E:\\unity learn\\Buff In ARPG\\Library\\ScriptAssemblies\\Assembly-CSharp.dll", "types": [{"className": "AoeState", "fieldInfos": []}, {"className": "BulletState", "fieldInfos": [{"name": "caster", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fireDegree", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "speed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "duration", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "useFireDegreeForever", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "canHitAfterCreated", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "hp", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "BulletCollisionManager", "fieldInfos": []}, {"className": "CamFollow", "fieldInfos": []}, {"className": "<PERSON><PERSON><PERSON>", "fieldInfos": []}, {"className": "ChaState", "fieldInfos": [{"name": "charging", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "dead", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "side", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "PlayerController", "fieldInfos": [{"name": "mainCamera", "type": "UnityEngine.Camera", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "SimpleAI", "fieldInfos": []}, {"className": "DamageManager", "fieldInfos": []}, {"className": "GameManager", "fieldInfos": []}, {"className": "SightEffect", "fieldInfos": [{"name": "duration", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TimelineManager", "fieldInfos": []}, {"className": "PieChartController", "fieldInfos": [{"name": "radius", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "startAngleDegree", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "angleDegree", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "angleDegreePrecision", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "radiusPrecision", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnitAnim", "fieldInfos": [{"name": "timeScale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnitBindManager", "fieldInfos": []}, {"className": "UnitBindPoint", "fieldInfos": [{"name": "key", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "offset", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnitMove", "fieldInfos": [{"name": "moveType", "type": "MoveType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bodyRadius", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "smoothMove", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnitRemover", "fieldInfos": [{"name": "duration", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnitRotate", "fieldInfos": [{"name": "rotateSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}]}, {"name": "UnityEngine.TestRunner", "path": "E:\\unity learn\\Buff In ARPG\\Library\\ScriptAssemblies\\UnityEngine.TestRunner.dll", "types": [{"className": "UnityEngine.TestRunner.Utils.TestRunCallbackListener", "fieldInfos": []}, {"className": "UnityEngine.TestRunner.TestLaunchers.RemoteTestData", "fieldInfos": [{"name": "id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fullName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "testCaseCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ChildIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isSuite", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "childrenIds", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "testCaseTimeout", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Categories", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "IsTestAssembly", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "RunState", "type": "NUnit.Framework.Interfaces.RunState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Description", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SkipReason", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ParentId", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "UniqueName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ParentUniqueName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ParentFullName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestRunner.TestLaunchers.RemoteTestResultData", "fieldInfos": [{"name": "testId", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fullName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "resultState", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "testStatus", "type": "NUnit.Framework.Interfaces.TestStatus", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "duration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "message", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "stackTrace", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "assertCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "failCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "passCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "skip<PERSON><PERSON>nt", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "inconclusiveCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "output", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xml", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "childrenIds", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestRunner.TestLaunchers.RemoteTestResultDataWithTestData", "fieldInfos": [{"name": "results", "type": "UnityEngine.TestRunner.TestLaunchers.RemoteTestResultData[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tests", "type": "UnityEngine.TestRunner.TestLaunchers.RemoteTestData[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestTools.BeforeAfterTestCommandState", "fieldInfos": [{"name": "NextBeforeStepIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "NextBeforeStepPc", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "NextAfterStepIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "NextAfterStepPc", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "TestHasRun", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CurrentTestResultStatus", "type": "NUnit.Framework.Interfaces.TestStatus", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CurrentTestResultLabel", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CurrentTestResultSite", "type": "NUnit.Framework.Interfaces.FailureSite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CurrentTestMessage", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CurrentTestStrackTrace", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "TestAfterStarted", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Timestamp", "type": "System.Int64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestTools.EnumerableTestState", "fieldInfos": [{"name": "Repeat", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Retry", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestTools.Logging.LogMatch", "fieldInfos": [{"name": "m_UseRegex", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Message", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MessageRegex", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LogType", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestTools.TestRunner.TestFinishedEvent", "fieldInfos": []}, {"className": "UnityEngine.TestTools.TestRunner.TestStartedEvent", "fieldInfos": []}, {"className": "UnityEngine.TestTools.TestRunner.RunFinishedEvent", "fieldInfos": []}, {"className": "UnityEngine.TestTools.TestRunner.RunStartedEvent", "fieldInfos": []}, {"className": "UnityEngine.TestTools.TestRunner.PlaymodeTestsController", "fieldInfos": [{"name": "m_AssembliesWithTests", "type": "System.Collections.Generic.List`1[System.String]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "testStartedEvent", "type": "UnityEngine.TestTools.TestRunner.TestStartedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "testFinishedEvent", "type": "UnityEngine.TestTools.TestRunner.TestFinishedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "runStartedEvent", "type": "UnityEngine.TestTools.TestRunner.RunStartedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "runFinishedEvent", "type": "UnityEngine.TestTools.TestRunner.RunFinishedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "settings", "type": "UnityEngine.TestTools.TestRunner.PlaymodeTestsControllerSettings", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestTools.TestRunner.PlaymodeTestsControllerSettings", "fieldInfos": [{"name": "filters", "type": "UnityEngine.TestTools.TestRunner.GUI.RuntimeTestRunnerFilter[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sceneBased", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "originalScene", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bootstrapScene", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestTools.TestRunner.GUI.RuntimeTestRunnerFilter", "fieldInfos": [{"name": "assemblyNames", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "groupNames", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "categoryNames", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "testNames", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "synchronousOnly", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.TestTools.TestRunner.Callbacks.PlayModeRunnerCallback", "fieldInfos": []}, {"className": "UnityEngine.TestTools.TestRunner.Callbacks.PlayerQuitHandler", "fieldInfos": []}, {"className": "UnityEngine.TestTools.TestRunner.Callbacks.RemoteTestResultSender", "fieldInfos": []}, {"className": "UnityEngine.TestTools.TestRunner.Callbacks.TestResultRendererCallback", "fieldInfos": []}]}, {"name": "Unity.Timeline", "path": "E:\\unity learn\\Buff In ARPG\\Library\\ScriptAssemblies\\Unity.Timeline.dll", "types": [{"className": "UnityEngine.Timeline.ActivationPlayableAsset", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ActivationTrack", "fieldInfos": [{"name": "m_PostPlaybackState", "type": "UnityEngine.Timeline.ActivationTrack+PostPlaybackState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TrackAsset", "fieldInfos": [{"name": "m_Version", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON>im<PERSON><PERSON>", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Locked", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Muted", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CustomPlayableFullTypename", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Curves", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Parent", "type": "UnityEngine.Playables.PlayableAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Children", "type": "System.Collections.Generic.List`1[UnityEngine.ScriptableObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Clips", "type": "System.Collections.Generic.List`1[UnityEngine.Timeline.TimelineClip]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON>", "type": "UnityEngine.Timeline.MarkerList", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AnimationPlayableAsset", "fieldInfos": [{"name": "m_Clip", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Position", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EulerAngles", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseTrackMatchFields", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON><PERSON><PERSON>ields", "type": "UnityEngine.Timeline.MatchTargetFields", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RemoveStartOffset", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ApplyFootIK", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Loop", "type": "UnityEngine.Timeline.AnimationPlayableAsset+LoopMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Version", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Rotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AnimationTrack", "fieldInfos": [{"name": "m_InfiniteClipPreExtrapolation", "type": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipPostExtrapolation", "type": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipOffsetPosition", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipOffsetEulerAngles", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipTimeOffset", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipRemoveOffset", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipApplyFootIK", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "mInfiniteClipLoop", "type": "UnityEngine.Timeline.AnimationPlayableAsset+LoopMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON><PERSON><PERSON>ields", "type": "UnityEngine.Timeline.MatchTargetFields", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Position", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EulerAngles", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AvatarMask", "type": "UnityEngine.AvatarMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ApplyAvatarMask", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TrackOffset", "type": "UnityEngine.Timeline.TrackOffset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClip", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OpenClipOffsetRotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Rotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ApplyOffsets", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineClip", "fieldInfos": [{"name": "m_Version", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Start", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClipIn", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Asset", "type": "UnityEngine.Object", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Duration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TimeScale", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ParentTrack", "type": "UnityEngine.Timeline.TrackAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EaseInDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EaseOutDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlendInDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlendOutDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MixInCurve", "type": "UnityEngine.AnimationCurve", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MixOutCurve", "type": "UnityEngine.AnimationCurve", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlendInCurveMode", "type": "UnityEngine.Timeline.TimelineClip+BlendCurveMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlendOutCurveMode", "type": "UnityEngine.Timeline.TimelineClip+BlendCurveMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ExposedParameterNames", "type": "System.Collections.Generic.List`1[System.String]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AnimationCurves", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Recordable", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PostExtrapolationMode", "type": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreExtrapolationMode", "type": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PostExtrapolationTime", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreExtrapolationTime", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisplayName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineAsset", "fieldInfos": [{"name": "m_Version", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Tracks", "type": "System.Collections.Generic.List`1[UnityEngine.ScriptableObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FixedDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EditorSettings", "type": "UnityEngine.Timeline.TimelineAsset+EditorSettings", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DurationMode", "type": "UnityEngine.Timeline.TimelineAsset+DurationMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MarkerTrack", "type": "UnityEngine.Timeline.MarkerTrack", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineAsset+EditorSettings", "fieldInfos": [{"name": "m_Framerate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScenePreview", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AudioClipProperties", "fieldInfos": [{"name": "volume", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AudioMixerProperties", "fieldInfos": [{"name": "volume", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "stereoPan", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spatialBlend", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AudioPlayableAsset", "fieldInfos": [{"name": "m_Clip", "type": "UnityEngine.AudioClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Loop", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_bufferingTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClipProperties", "type": "UnityEngine.Timeline.AudioClipProperties", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AudioTrack", "fieldInfos": [{"name": "m_TrackProperties", "type": "UnityEngine.Timeline.AudioMixerProperties", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.ControlPlayableAsset", "fieldInfos": [{"name": "sourceGameObject", "type": "UnityEngine.ExposedReference`1[UnityEngine.GameObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "prefabGameObject", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "updateParticle", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "particleRandomSeed", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "updateDirector", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "updateITimeControl", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "searchHierarchy", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "active", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "postPlayback", "type": "UnityEngine.Timeline.ActivationControlPlayable+PostPlaybackState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.ControlTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.Marker", "fieldInfos": [{"name": "m_Time", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.MarkerList", "fieldInfos": [{"name": "m_Objects", "type": "System.Collections.Generic.List`1[UnityEngine.ScriptableObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.MarkerTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.SignalTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.SignalAsset", "fieldInfos": []}, {"className": "UnityEngine.Timeline.SignalEmitter", "fieldInfos": [{"name": "m_Retroactive", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EmitOnce", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Asset", "type": "UnityEngine.Timeline.SignalAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.SignalReceiver", "fieldInfos": [{"name": "m_Events", "type": "UnityEngine.Timeline.SignalReceiver+EventKeyValue", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.SignalReceiver+EventKeyValue", "fieldInfos": [{"name": "m_Signals", "type": "System.Collections.Generic.List`1[UnityEngine.Timeline.SignalAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Events", "type": "System.Collections.Generic.List`1[UnityEngine.Events.UnityEvent]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.GroupTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.BasicPlayableBehaviour", "fieldInfos": []}, {"className": "UnityEngine.Timeline.PlayableTrack", "fieldInfos": []}]}, {"name": "Unity.TextMeshPro", "path": "E:\\unity learn\\Buff In ARPG\\Library\\ScriptAssemblies\\Unity.TextMeshPro.dll", "types": [{"className": "TMPro.TMP_Asset", "fieldInfos": [{"name": "hashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "materialHashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Character", "fieldInfos": []}, {"className": "TMPro.TMP_TextElement", "fieldInfos": [{"name": "m_ElementType", "type": "TMPro.TextElementType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Unicode", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlyphIndex", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Scale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_ColorGradient", "fieldInfos": [{"name": "colorMode", "type": "TMPro.ColorMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "topLeft", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "topRight", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomLeft", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomRight", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown", "fieldInfos": [{"name": "m_Template", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaptionText", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaptionImage", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Placeholder", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ItemText", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ItemImage", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Value", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Options", "type": "TMPro.TMP_Dropdown+OptionDataList", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "TMPro.TMP_Dropdown+DropdownEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AlphaFadeSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown+DropdownItem", "fieldInfos": [{"name": "m_Text", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Image", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RectTransform", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Toggle", "type": "UnityEngine.UI.Toggle", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown+OptionData", "fieldInfos": [{"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Image", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown+OptionDataList", "fieldInfos": [{"name": "m_Options", "type": "System.Collections.Generic.List`1[TMPro.TMP_Dropdown+OptionData]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown+DropdownEvent", "fieldInfos": []}, {"className": "TMPro.TMP_FontAsset", "fieldInfos": [{"name": "m_Version", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SourceFontFileGUID", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SourceFontFile_EditorRef", "type": "UnityEngine.Font", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SourceFontFile", "type": "UnityEngine.Font", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasPopulationMode", "type": "TMPro.AtlasPopulationMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FaceInfo", "type": "UnityEngine.TextCore.FaceInfo", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlyphTable", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.Glyph]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterTable", "type": "System.Collections.Generic.List`1[TMPro.TMP_Character]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasTextures", "type": "UnityEngine.Texture2D[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasTextureIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsMultiAtlasTexturesEnabled", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClearDynamicDataOnBuild", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UsedGlyphRects", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.GlyphRect]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FreeGlyphRects", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.GlyphRect]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontInfo", "type": "TMPro.FaceInfo_Legacy", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "atlas", "type": "UnityEngine.Texture2D", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasWidth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasHeight", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasPadding", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasRenderMode", "type": "UnityEngine.TextCore.LowLevel.GlyphRenderMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_glyphInfoList", "type": "System.Collections.Generic.List`1[TMPro.TMP_Glyph]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_KerningTable", "type": "TMPro.KerningTable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FontFeatureTable", "type": "TMPro.TMP_FontFeatureTable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fallbackFontAssets", "type": "System.Collections.Generic.List`1[TMPro.TMP_FontAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FallbackFontAssetTable", "type": "System.Collections.Generic.List`1[TMPro.TMP_FontAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CreationSettings", "type": "TMPro.FontAssetCreationSettings", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FontWeightTable", "type": "TMPro.TMP_FontWeightPair[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontWeights", "type": "TMPro.TMP_FontWeightPair[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalStyle", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalSpacingOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "boldStyle", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "boldSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "italicStyle", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tabSize", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.FaceInfo_Legacy", "fieldInfos": [{"name": "Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "PointSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Scale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CharacterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "LineHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Baseline", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Ascender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CapHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Descender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CenterLine", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SuperscriptOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SubscriptOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SubSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Underline", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "UnderlineThickness", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strikethrough", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strikethroughThickness", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Padding", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "AtlasWidth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "AtlasHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Glyph", "fieldInfos": []}, {"className": "TMPro.TMP_TextElement_Legacy", "fieldInfos": [{"name": "id", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "x", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "y", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "width", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "height", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "scale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.FontAssetCreationSettings", "fieldInfos": [{"name": "sourceFontFileName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sourceFontFileGUID", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pointSizeSamplingMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pointSize", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "padding", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "packingMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "atlasWidth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "atlasHeight", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characterSetSelectionMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characterSequence", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "referencedFontAssetGUID", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "referencedTextAssetGUID", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontStyle", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontStyleModifier", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "renderMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "includeFontFeatures", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_FontWeightPair", "fieldInfos": [{"name": "regularTypeface", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "italicTypeface", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.GlyphValueRecord_Legacy", "fieldInfos": [{"name": "xPlacement", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yPlacement", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.KerningPair", "fieldInfos": [{"name": "m_FirstGlyph", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FirstGlyphAdjustments", "type": "TMPro.GlyphValueRecord_Legacy", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SecondGlyph", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SecondGlyphAdjustments", "type": "TMPro.GlyphValueRecord_Legacy", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IgnoreSpacingAdjustments", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.KerningTable", "fieldInfos": [{"name": "kerningPairs", "type": "System.Collections.Generic.List`1[TMPro.KerningPair]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_FontFeatureTable", "fieldInfos": [{"name": "m_GlyphPairAdjustmentRecords", "type": "System.Collections.Generic.List`1[TMPro.TMP_GlyphPairAdjustmentRecord]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_GlyphValueRecord", "fieldInfos": [{"name": "m_XPlacement", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_YPlacement", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_XAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_YAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_GlyphAdjustmentRecord", "fieldInfos": [{"name": "m_GlyphIndex", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlyphValueRecord", "type": "TMPro.TMP_GlyphValueRecord", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_GlyphPairAdjustmentRecord", "fieldInfos": [{"name": "m_FirstAdjustmentRecord", "type": "TMPro.TMP_GlyphAdjustmentRecord", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SecondAdjustmentRecord", "type": "TMPro.TMP_GlyphAdjustmentRecord", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FeatureLookupFlags", "type": "TMPro.FontFeatureLookupFlags", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_InputField", "fieldInfos": [{"name": "m_TextViewport", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Placeholder", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbar", "type": "UnityEngine.UI.Scrollbar", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbarEventHandler", "type": "TMPro.TMP_ScrollbarEventHandler", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LayoutGroup", "type": "UnityEngine.UI.LayoutGroup", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ContentType", "type": "TMPro.TMP_InputField+ContentType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputType", "type": "TMPro.TMP_InputField+InputType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AsteriskChar", "type": "System.Char", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_KeyboardType", "type": "UnityEngine.TouchScreenKeyboardType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LineType", "type": "TMPro.TMP_InputField+LineType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HideMobileInput", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HideSoftKeyboard", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterValidation", "type": "TMPro.TMP_InputField+CharacterValidation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RegexValue", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlobalPointSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterLimit", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnEndEdit", "type": "TMPro.TMP_InputField+SubmitEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnSubmit", "type": "TMPro.TMP_InputField+SubmitEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnSelect", "type": "TMPro.TMP_InputField+SelectionEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnDeselect", "type": "TMPro.TMP_InputField+SelectionEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnTextSelection", "type": "TMPro.TMP_InputField+TextSelectionEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnEndTextSelection", "type": "TMPro.TMP_InputField+TextSelectionEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "TMPro.TMP_InputField+OnChangeEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnTouchScreenKeyboardStatusChanged", "type": "TMPro.TMP_InputField+TouchScreenKeyboardEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaretColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CustomCaretColor", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectionColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaretBlinkRate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON>tWidth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReadOnly", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RichText", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlobalFontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnFocusSelectAll", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ResetOnDeActivation", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RestoreOriginalTextOnEscape", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isRichTextEditingAllowed", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LineLimit", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputValidator", "type": "TMPro.TMP_InputValidator", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_InputField+SubmitEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+OnChangeEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+SelectionEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+TextSelectionEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+TouchScreenKeyboardEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputValidator", "fieldInfos": []}, {"className": "TMPro.TMP_PackageResourceImporter", "fieldInfos": []}, {"className": "TMPro.TMP_PackageResourceImporterWindow", "fieldInfos": [{"name": "m_ResourceImporter", "type": "TMPro.TMP_PackageResourceImporter", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_ScrollbarEventHandler", "fieldInfos": [{"name": "isSelected", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SelectionCaret", "fieldInfos": []}, {"className": "TMPro.TMP_Settings", "fieldInfos": [{"name": "m_enableWordWrapping", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableKerning", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableExtraPadding", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableTintAllSprites", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableParseEscapeCharacters", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EnableRaycastTarget", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GetFontFeaturesAtRuntime", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_missingGlyphCharacter", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_warningsDisabled", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultFontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultFontAssetPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultFontSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultAutoSizeMinRatio", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultAutoSizeMaxRatio", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultTextMeshProTextContainerSize", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultTextMeshProUITextContainerSize", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_autoSizeTextContainer", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsTextObjectScaleStatic", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fallbackFontAssets", "type": "System.Collections.Generic.List`1[TMPro.TMP_FontAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_matchMaterialPreset", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultSpriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultSpriteAssetPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableEmojiSupport", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MissingCharacterSpriteUnicode", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultColorGradientPresetsPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultStyleSheet", "type": "TMPro.TMP_StyleSheet", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_StyleSheetsResourcePath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_leadingCharacters", "type": "UnityEngine.TextAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_following<PERSON><PERSON><PERSON><PERSON>", "type": "UnityEngine.TextAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseModernHangulLineBreakingRules", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Sprite", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "hashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "unicode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pivot", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SpriteAnimator", "fieldInfos": []}, {"className": "TMPro.TMP_SpriteAsset", "fieldInfos": [{"name": "m_Version", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FaceInfo", "type": "UnityEngine.TextCore.FaceInfo", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spriteSheet", "type": "UnityEngine.Texture", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SpriteCharacterTable", "type": "System.Collections.Generic.List`1[TMPro.TMP_SpriteCharacter]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SpriteGlyphTable", "type": "System.Collections.Generic.List`1[TMPro.TMP_SpriteGlyph]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spriteInfoList", "type": "System.Collections.Generic.List`1[TMPro.TMP_Sprite]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fallbackSpriteAssets", "type": "System.Collections.Generic.List`1[TMPro.TMP_SpriteAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SpriteCharacter", "fieldInfos": [{"name": "m_Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SpriteGlyph", "fieldInfos": [{"name": "sprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Style", "fieldInfos": [{"name": "m_Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OpeningDefinition", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClosingDefinition", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OpeningTagArray", "type": "System.Int32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClosingTagArray", "type": "System.Int32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OpeningTagUnicodeArray", "type": "System.UInt32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClosingTagUnicodeArray", "type": "System.UInt32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_StyleSheet", "fieldInfos": [{"name": "m_StyleList", "type": "System.Collections.Generic.List`1[TMPro.TMP_Style]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SubMesh", "fieldInfos": [{"name": "m_fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_spriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_sharedMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isDefaultMaterial", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_padding", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_renderer", "type": "UnityEngine.Renderer", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextComponent", "type": "TMPro.TextMeshPro", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SubMeshUI", "fieldInfos": [{"name": "m_fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_spriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_sharedMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isDefaultMaterial", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_padding", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextComponent", "type": "TMPro.TextMeshProUGUI", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_materialReferenceIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Text", "fieldInfos": [{"name": "m_text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isRightToLeft", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_sharedMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSharedMaterials", "type": "UnityEngine.Material[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontMaterials", "type": "UnityEngine.Material[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontColor32", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableVertexGradient", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_colorMode", "type": "TMPro.ColorMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontColorGradient", "type": "TMPro.VertexGradient", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontColorGradientPreset", "type": "TMPro.TMP_ColorGradient", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_spriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_tintAllSprites", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_StyleSheet", "type": "TMPro.TMP_StyleSheet", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextStyleHashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_overrideHtmlColors", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_faceColor", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSizeBase", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontWeight", "type": "TMPro.FontWeight", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableAutoSizing", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSizeMin", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSizeMax", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontStyle", "type": "TMPro.FontStyles", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalAlignment", "type": "TMPro.HorizontalAlignmentOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalAlignment", "type": "TMPro.VerticalAlignmentOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_textAlignment", "type": "TMPro.TextAlignmentOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_characterSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_wordSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_lineSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_lineSpacingMax", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_paragraphSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_charWidthMaxAdj", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableWordWrapping", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_wordWrappingRatios", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_overflowMode", "type": "TMPro.TextOverflowModes", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_linkedTextComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "parentLinkedComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableKerning", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableExtraPadding", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "checkPaddingRequired", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isRichText", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_parseCtrlCharacters", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isOrthographic", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isCullingEnabled", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_horizontalMapping", "type": "TMPro.TextureMappingOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_verticalMapping", "type": "TMPro.TextureMappingOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_uvLineOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_geometrySortingOrder", "type": "TMPro.VertexSortingOrder", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsTextObjectScaleStatic", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VertexBufferAutoSizeReduction", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_useMaxVisibleDescender", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_pageToDisplay", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_margin", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isUsingLegacyAnimationComponent", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isVolumetricText", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_TextInfo", "fieldInfos": [{"name": "textComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spriteCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spaceCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "wordCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "linkCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lineCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pageCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "materialCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.VertexGradient", "fieldInfos": [{"name": "topLeft", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "topRight", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomLeft", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomRight", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.Mesh_Extents", "fieldInfos": [{"name": "min", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "max", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextMeshPro", "fieldInfos": [{"name": "m_hasFontAssetChanged", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_renderer", "type": "UnityEngine.Renderer", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_maskType", "type": "TMPro.MaskingTypes", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "_Sorting<PERSON>ayer", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "_SortingLayerID", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "_SortingOrder", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextMeshProUGUI", "fieldInfos": [{"name": "m_hasFontAssetChanged", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_baseMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_maskOffset", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextContainer", "fieldInfos": [{"name": "m_pivot", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_anchorPosition", "type": "TMPro.TextContainerAnchors", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_rect", "type": "UnityEngine.Rect", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_margins", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}]}, {"name": "UnityEngine.UI", "path": "E:\\unity learn\\Buff In ARPG\\Library\\ScriptAssemblies\\UnityEngine.UI.dll", "types": [{"className": "UnityEngine.UI.AnimationTriggers", "fieldInfos": [{"name": "m_NormalTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HighlightedTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PressedTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectedTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisabledTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Button", "fieldInfos": [{"name": "m_OnClick", "type": "UnityEngine.UI.Button+ButtonClickedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Button+ButtonClickedEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.Selectable", "fieldInfos": [{"name": "m_Navigation", "type": "UnityEngine.UI.Navigation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Transition", "type": "UnityEngine.UI.Selectable+Transition", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Colors", "type": "UnityEngine.UI.ColorBlock", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SpriteState", "type": "UnityEngine.UI.SpriteState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AnimationTriggers", "type": "UnityEngine.UI.AnimationTriggers", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Interactable", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TargetGraphic", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.UIBehaviour", "fieldInfos": []}, {"className": "UnityEngine.UI.ColorBlock", "fieldInfos": [{"name": "m_NormalColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HighlightedColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PressedColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectedColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisabledColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ColorMultiplier", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FadeDuration", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown", "fieldInfos": [{"name": "m_Template", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaptionText", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaptionImage", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ItemText", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ItemImage", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Value", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Options", "type": "UnityEngine.UI.Dropdown+OptionDataList", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.Dropdown+DropdownEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AlphaFadeSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown+DropdownItem", "fieldInfos": [{"name": "m_Text", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Image", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RectTransform", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Toggle", "type": "UnityEngine.UI.Toggle", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown+OptionData", "fieldInfos": [{"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Image", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown+OptionDataList", "fieldInfos": [{"name": "m_Options", "type": "System.Collections.Generic.List`1[UnityEngine.UI.Dropdown+OptionData]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown+DropdownEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.FontData", "fieldInfos": [{"name": "m_Font", "type": "UnityEngine.Font", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FontSize", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FontStyle", "type": "UnityEngine.FontStyle", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BestFit", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MinSize", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxSize", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Alignment", "type": "UnityEngine.TextAnchor", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AlignByGeometry", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RichText", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalOverflow", "type": "UnityEngine.HorizontalWrapMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalOverflow", "type": "UnityEngine.VerticalWrapMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LineSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Graphic", "fieldInfos": [{"name": "m_Material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Color", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RaycastTarget", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RaycastPadding", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.GraphicRaycaster", "fieldInfos": [{"name": "m_IgnoreReversedGraphics", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlockingObjects", "type": "UnityEngine.UI.GraphicRaycaster+BlockingObjects", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlockingMask", "type": "UnityEngine.LayerMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.BaseRaycaster", "fieldInfos": []}, {"className": "UnityEngine.UI.Image", "fieldInfos": [{"name": "m_Sprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Type", "type": "UnityEngine.UI.Image+Type", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreserveAspect", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FillCenter", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FillMethod", "type": "UnityEngine.UI.Image+FillMethod", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FillAmount", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FillClockwise", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Fill<PERSON><PERSON><PERSON>", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseSpriteMesh", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PixelsPerUnitMultiplier", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.MaskableGraphic", "fieldInfos": [{"name": "m_Maskable", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnCullStateChanged", "type": "UnityEngine.UI.MaskableGraphic+CullStateChangedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.MaskableGraphic+CullStateChangedEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.InputField", "fieldInfos": [{"name": "m_TextComponent", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Placeholder", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ContentType", "type": "UnityEngine.UI.InputField+ContentType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputType", "type": "UnityEngine.UI.InputField+InputType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AsteriskChar", "type": "System.Char", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_KeyboardType", "type": "UnityEngine.TouchScreenKeyboardType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LineType", "type": "UnityEngine.UI.InputField+LineType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HideMobileInput", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterValidation", "type": "UnityEngine.UI.InputField+CharacterValidation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterLimit", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnEndEdit", "type": "UnityEngine.UI.InputField+SubmitEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.InputField+OnChangeEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaretColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CustomCaretColor", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectionColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaretBlinkRate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON>tWidth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReadOnly", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ShouldActivateOnSelect", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.InputField+SubmitEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.InputField+OnChangeEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.AspectRatioFitter", "fieldInfos": [{"name": "m_AspectMode", "type": "UnityEngine.UI.AspectRatioFitter+AspectMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AspectRatio", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.CanvasScaler", "fieldInfos": [{"name": "m_UiScaleMode", "type": "UnityEngine.UI.CanvasScaler+ScaleMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReferencePixelsPerUnit", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScaleFactor", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReferenceResolution", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScreenMatchMode", "type": "UnityEngine.UI.CanvasScaler+ScreenMatchMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MatchWidthOrHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PhysicalUnit", "type": "UnityEngine.UI.CanvasScaler+Unit", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FallbackScreenDPI", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultSpriteDPI", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DynamicPixelsPerUnit", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PresetInfoIsWorld", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ContentSizeFitter", "fieldInfos": [{"name": "m_HorizontalFit", "type": "UnityEngine.UI.ContentSizeFitter+FitMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalFit", "type": "UnityEngine.UI.ContentSizeFitter+FitMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.GridLayoutGroup", "fieldInfos": [{"name": "m_<PERSON><PERSON>orner", "type": "UnityEngine.UI.GridLayoutGroup+Corner", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_StartAxis", "type": "UnityEngine.UI.GridLayoutGroup+Axis", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CellSize", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Spacing", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Constraint", "type": "UnityEngine.UI.GridLayoutGroup+Constraint", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ConstraintCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.LayoutGroup", "fieldInfos": [{"name": "m_Padding", "type": "UnityEngine.RectOffset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildAlignment", "type": "UnityEngine.TextAnchor", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.HorizontalLayoutGroup", "fieldInfos": []}, {"className": "UnityEngine.UI.HorizontalOrVerticalLayoutGroup", "fieldInfos": [{"name": "m_Spacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildForceExpandWidth", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildForceExpandHeight", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildControl<PERSON>idth", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildControlHeight", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildScaleWidth", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildScaleHeight", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReverseArrangement", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.LayoutElement", "fieldInfos": [{"name": "m_IgnoreLayout", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON><PERSON>", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON>ght", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreferredWidth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreferredHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FlexibleWidth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FlexibleHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LayoutPriority", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.VerticalLayoutGroup", "fieldInfos": []}, {"className": "UnityEngine.UI.Mask", "fieldInfos": [{"name": "m_ShowMaskGraphic", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Navigation", "fieldInfos": [{"name": "m_Mode", "type": "UnityEngine.UI.Navigation+Mode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_WrapAround", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectOnUp", "type": "UnityEngine.UI.Selectable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectOnDown", "type": "UnityEngine.UI.Selectable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectOnLeft", "type": "UnityEngine.UI.Selectable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectOnRight", "type": "UnityEngine.UI.Selectable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.RawImage", "fieldInfos": [{"name": "m_Texture", "type": "UnityEngine.Texture", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UVRect", "type": "UnityEngine.Rect", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.RectMask2D", "fieldInfos": [{"name": "m_Padding", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Softness", "type": "UnityEngine.Vector2Int", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ScrollRect", "fieldInfos": [{"name": "m_Content", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Horizontal", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Vertical", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MovementType", "type": "UnityEngine.UI.ScrollRect+MovementType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Elasticity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Inertia", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DecelerationRate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Viewport", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalScrollbar", "type": "UnityEngine.UI.Scrollbar", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbar", "type": "UnityEngine.UI.Scrollbar", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalScrollbarVisibility", "type": "UnityEngine.UI.ScrollRect+ScrollbarVisibility", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbarVisibility", "type": "UnityEngine.UI.ScrollRect+ScrollbarVisibility", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalScrollbarSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbarSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.ScrollRect+ScrollRectEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ScrollRect+ScrollRectEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.Scrollbar", "fieldInfos": [{"name": "m_HandleRect", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Direction", "type": "UnityEngine.UI.Scrollbar+Direction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Value", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Size", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_NumberOfSteps", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.Scrollbar+ScrollEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Scrollbar+ScrollEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.Slider", "fieldInfos": [{"name": "m_FillRect", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HandleRect", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Direction", "type": "UnityEngine.UI.Slider+Direction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MinValue", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxValue", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_WholeNumbers", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Value", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.Slider+SliderEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Slider+SliderEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.SpriteState", "fieldInfos": [{"name": "m_HighlightedSprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PressedSprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectedSprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisabledSprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Text", "fieldInfos": [{"name": "m_FontData", "type": "UnityEngine.UI.FontData", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Toggle", "fieldInfos": [{"name": "toggleTransition", "type": "UnityEngine.UI.Toggle+ToggleTransition", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "graphic", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Group", "type": "UnityEngine.UI.ToggleGroup", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "onValueChanged", "type": "UnityEngine.UI.Toggle+ToggleEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsOn", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Toggle+ToggleEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.ToggleGroup", "fieldInfos": [{"name": "m_AllowSwitchOff", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.BaseMeshEffect", "fieldInfos": []}, {"className": "UnityEngine.UI.Outline", "fieldInfos": []}, {"className": "UnityEngine.UI.Shadow", "fieldInfos": [{"name": "m_EffectColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EffectDistance", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseGraphicAlpha", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.PositionAsUV1", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.EventSystem", "fieldInfos": [{"name": "m_FirstSelected", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_sendNavigationEvents", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DragT<PERSON><PERSON><PERSON>", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.EventTrigger", "fieldInfos": [{"name": "m_Delegates", "type": "System.Collections.Generic.List`1[UnityEngine.EventSystems.EventTrigger+Entry]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.EventTrigger+TriggerEvent", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.EventTrigger+Entry", "fieldInfos": [{"name": "eventID", "type": "UnityEngine.EventSystems.EventTriggerType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "callback", "type": "UnityEngine.EventSystems.EventTrigger+TriggerEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.BaseInput", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.BaseInputModule", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.PointerInputModule", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.StandaloneInputModule", "fieldInfos": [{"name": "m_HorizontalAxis", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalAxis", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SubmitButton", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CancelButton", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputActionsPerSecond", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Repeat<PERSON><PERSON>y", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ForceModuleActive", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.TouchInputModule", "fieldInfos": [{"name": "m_ForceModuleActive", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.Physics2DRaycaster", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.PhysicsRaycaster", "fieldInfos": [{"name": "m_EventMask", "type": "UnityEngine.LayerMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxRayIntersections", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}]}, {"name": "Newtonsoft.Json", "path": "E:\\unity learn\\Buff In ARPG\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@2.0.0\\Runtime\\AOT\\Newtonsoft.Json.dll", "types": [{"className": "Newtonsoft.Json.JsonException", "fieldInfos": []}, {"className": "Newtonsoft.Json.JsonReaderException", "fieldInfos": []}, {"className": "Newtonsoft.Json.JsonSerializationException", "fieldInfos": []}, {"className": "Newtonsoft.Json.JsonWriterException", "fieldInfos": []}, {"className": "Newtonsoft.Json.Schema.JsonSchemaException", "fieldInfos": []}]}, {"name": "Newtonsoft.Json", "path": "E:\\unity learn\\Buff In ARPG\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@2.0.0\\Runtime\\Portable\\Newtonsoft.Json.dll", "types": []}, {"name": "Newtonsoft.Json", "path": "E:\\unity learn\\Buff In ARPG\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json@2.0.0\\Runtime\\Newtonsoft.Json.dll", "types": [{"className": "Newtonsoft.Json.JsonException", "fieldInfos": []}, {"className": "Newtonsoft.Json.JsonReaderException", "fieldInfos": []}, {"className": "Newtonsoft.Json.JsonSerializationException", "fieldInfos": []}, {"className": "Newtonsoft.Json.JsonWriterException", "fieldInfos": []}, {"className": "Newtonsoft.Json.Schema.JsonSchemaException", "fieldInfos": []}]}, {"name": "nunit.framework", "path": "E:\\unity learn\\Buff In ARPG\\Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll", "types": [{"className": "NUnit.Framework.ResultStateException", "fieldInfos": []}, {"className": "NUnit.Framework.AssertionException", "fieldInfos": []}, {"className": "NUnit.Framework.IgnoreException", "fieldInfos": []}, {"className": "NUnit.Framework.InconclusiveException", "fieldInfos": []}, {"className": "NUnit.Framework.SuccessException", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.InvalidDataSourceException", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.InvalidTestFixtureException", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.NUnitException", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.RuntimeFramework", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestFilter+EmptyFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.PropertyFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.ValueMatchFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.TestNameFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.ClassNameFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.MethodNameFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.IdFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.AndFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.CategoryFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.NotFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.OrFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.FullNameFilter", "fieldInfos": []}]}]}