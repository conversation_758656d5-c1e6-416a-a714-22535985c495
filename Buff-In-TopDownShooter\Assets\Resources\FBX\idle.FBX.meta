fileFormatVersion: 2
guid: bfd44368c17054c48adce6072f660c3e
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 22
  fileIDToRecycleName:
    100000: Bip001
    100002: Bip001 Footsteps
    100004: Bip001 Head
    100006: Bip001 HeadNub
    100008: Bip001 L Calf
    100010: Bip001 L Clavicle
    100012: Bip001 L Foot
    100014: Bip001 L Forearm
    100016: Bip001 L Hand
    100018: Bip001 L Thigh
    100020: Bip001 L Toe0
    100022: Bip001 L Toe0Nub
    100024: Bip001 L UpperArm
    100026: Bip001 Neck
    100028: Bip001 Pelvis
    100030: Bip001 R Calf
    100032: Bip001 R Clavicle
    100034: Bip001 R Foot
    100036: Bip001 R Forearm
    100038: Bip001 R Hand
    100040: Bip001 R Thigh
    100042: Bip001 R Toe0
    100044: Bip001 R Toe0Nub
    100046: Bip001 R UpperArm
    100048: Bip001 Spine
    100050: Bip001 Spine1
    100052: Box001
    100054: //RootNode
    100056: Object007
    100058: Object009
    100060: "\xA1\xA1\xD7\xFD\xD7\xFB"
    100062: "\xD7\xFE\xD7\xFC"
    400000: Bip001
    400002: Bip001 Footsteps
    400004: Bip001 Head
    400006: Bip001 HeadNub
    400008: Bip001 L Calf
    400010: Bip001 L Clavicle
    400012: Bip001 L Foot
    400014: Bip001 L Forearm
    400016: Bip001 L Hand
    400018: Bip001 L Thigh
    400020: Bip001 L Toe0
    400022: Bip001 L Toe0Nub
    400024: Bip001 L UpperArm
    400026: Bip001 Neck
    400028: Bip001 Pelvis
    400030: Bip001 R Calf
    400032: Bip001 R Clavicle
    400034: Bip001 R Foot
    400036: Bip001 R Forearm
    400038: Bip001 R Hand
    400040: Bip001 R Thigh
    400042: Bip001 R Toe0
    400044: Bip001 R Toe0Nub
    400046: Bip001 R UpperArm
    400048: Bip001 Spine
    400050: Bip001 Spine1
    400052: Box001
    400054: //RootNode
    400056: Object007
    400058: Object009
    400060: "\xA1\xA1\xD7\xFD\xD7\xFB"
    400062: "\xD7\xFE\xD7\xFC"
    2100000: 'Material #3'
    2100002: 'Material #4'
    2100004: 'Material #0'
    2300000: Box001
    3300000: Box001
    4300000: Box001
    4300002: Object007
    4300004: Object009
    7400000: Take 001
    9500000: //RootNode
    13700000: Object007
    13700002: Object009
  externalObjects: {}
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 0
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
