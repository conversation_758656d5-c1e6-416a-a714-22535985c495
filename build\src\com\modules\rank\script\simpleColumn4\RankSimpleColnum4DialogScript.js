import { RankDataCenter } from "../../data/RankDataCenter";
import { RankSimpleColnum4ItemScript } from "./RankSimpleColnum4ItemScript";
import { RankConst } from "../../../../auto/ConstAuto";
/**
 * 通用4列排行榜脚本
 */
export class RankSimpleColnum4DialogScript extends RankSimpleColnum4ItemScript {
    /**动态设置列名 */
    static setColNames(rank_key, { col1 = window.iLang.L2_PAI_MING.il(), col2 = window.iLang.L2_WAN_JIA_MING_CHENG.il(), col3 = window.iLang.L2_ZHAN_LI.il(), col4 = window.iLang.L2_JI_FEN.il() } = {}) {
        RankSimpleColnum4DialogScript.colNameMap[rank_key] = [col1, col2, col3, col4];
    }
    get tempView() {
        return super.tempView;
    }
    get headBox() {
        if (!this.tempView)
            return null;
        return this.tempView.headBox;
    }
    get txtName() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColVal_1;
    }
    /**排行数值 */
    get txtSortVal() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColVal_3;
    }
    get txtColTitle_0() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColTitle_0;
    }
    get txtColTitle_1() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColTitle_1;
    }
    get txtColTitle_2() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColTitle_2;
    }
    get txtColTitle_3() {
        if (!this.tempView)
            return null;
        return this.tempView.txtColTitle_3;
    }
    onAwake() {
        super.onAwake();
        this.initColNames();
        this.isShowFcRank = false;
    }
    initColNames() {
        let colNames = RankSimpleColnum4DialogScript.colNameMap[this.rank_key];
        if (colNames) {
            this.txtColTitle_0 && (this.txtColTitle_0.text = colNames[0]);
            this.txtColTitle_1 && (this.txtColTitle_1.text = colNames[1]);
            this.txtColTitle_2 && (this.txtColTitle_2.text = colNames[2]);
            this.txtColTitle_3 && (this.txtColTitle_3.text = colNames[3]);
            return;
        }
        this.txtColTitle_1.text = window.iLang.L2_WAN_JIA_MING_CHENG.il();
        switch (this.rank_key) {
            case RankConst.RANK_KEY_CSC_FMSOLO: //公会战
                this.txtColTitle_2.text = window.iLang.L2_XING_SHU.il();
                this.txtColTitle_3.text = window.iLang.L2_ZHAN_JI.il();
                break;
            case RankConst.RANK_KEY_CSC_CLANSOLO: //家族战
                this.txtColTitle_2.text = window.iLang.L2_XING_SHU.il();
                this.txtColTitle_3.text = window.iLang.L2_ZHAN_JI.il();
                break;
        }
    }
    /**更新RankDetailDialog界面中底部的 我的排行数据 */
    updateMyRankView(rank_key, server_type = 1) {
        super.updateMyRankView(rank_key, server_type);
        let val2 = "";
        let val3 = "";
        let itemVo = RankDataCenter.instance.getMyRankItemVoByRankKey(rank_key, server_type);
        if (itemVo) {
            val2 = "" + itemVo.getEntityExt(0);
            val3 = "" + itemVo.getEntityExt(1);
        }
        this.txtColVal_2.text = val2;
        this.txtColVal_3.text = val3;
    }
}
/**列名 */
RankSimpleColnum4DialogScript.colNameMap = {};
