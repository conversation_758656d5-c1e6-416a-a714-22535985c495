
import { p_auction_house_goods } from "../../../proto/common/p_auction_house_goods";
import { m_auction_house_info_toc } from "../../../proto/line/m_auction_house_info_toc";
import { m_auction_house_logs_toc } from "../../../proto/line/m_auction_house_logs_toc";
import { m_auction_house_op_toc } from "../../../proto/line/m_auction_house_op_toc";
import { m_auction_house_tips_toc } from "../../../proto/line/m_auction_house_tips_toc";
import { SocketCommand } from "../../../proto/SocketCommand";
import { TipsUtil } from "../../../util/TipsUtil";
import { BaseController } from "../../BaseController";
import { WorkShopDataCenter } from "../../DivineWorkShop/data/WorkShopDataCenter";
import { GoodsDataCenter } from "../../goods/data/GoodsDataCenter";
import { GoodsVO } from "../../goods/GoodsVO";
//import AuctionHouseDialog from "../dialog/AuctionHouseDialog";
import { ModuleCommand } from "../../ModuleCommand";
import { AuctionHouseDataCenter, EAuctionHouseOpType } from "../data/AuctionHouseDataCenter";
import AuctionHouseBiddingDialog from "../dialog/AuctionHouseBiddingDialog";
import AuctionHouseDialog from "../dialog/AuctionHouseDialog";
import AuctionHouseGetDialog from "../dialog/AuctionHouseGetDialog";
import AuctionHouseListDialog from "../dialog/AuctionHouseListDialog";
import AuctionHouseLogDialog from "../dialog/AuctionHouseLogDialog";

//控制器不在任何类引用(包括本模块，只在ModuleController里面注册)
//模块里面的类其他模块都不会去引用的，删除某个模块代码正常编译，正常游戏
//这个类是无状态的
export class AuctionHouseController extends BaseController {

    static instance: AuctionHouseController;
    constructor() {
        super();
        AuctionHouseController.instance = this;
    }

    protected initModuleListeners(): void {
        this.registerDialog(AuctionHouseDialog, ModuleCommand.OPEN_AUCTION_HOUSE_DIALOG, ModuleCommand.CLOSE_AUCTION_HOUSE_DIALOG);
        this.registerDialog(AuctionHouseLogDialog, ModuleCommand.OPEN_AUCTION_HOUSE_LOG_DIALOG);
        this.registerDialog(AuctionHouseListDialog, ModuleCommand.OPEN_AUCTION_HOUSE_LIST_DIALOG, ModuleCommand.CLOSE_AUCTION_HOUSE_LIST_DIALOG);
        this.registerDialog(AuctionHouseBiddingDialog, ModuleCommand.OPEN_AUCTION_HOUSE_BIDDING_DIALOG, ModuleCommand.CLOSE_AUCTION_HOUSE_BIDDING_DIALOG);
        this.registerDialog(AuctionHouseGetDialog, ModuleCommand.OPEN_AUCTION_HOUSE_GET_DIALOG, ModuleCommand.CLOSE_AUCTION_HOUSE_GET_DIALOG);



    }

    protected initNetListeners(): void {
        this.addSocketListener(SocketCommand.AUCTION_HOUSE_INFO, this.m_auction_house_info_toc);
        this.addSocketListener(SocketCommand.AUCTION_HOUSE_OP, this.m_auction_house_op_toc);
        this.addSocketListener(SocketCommand.AUCTION_HOUSE_TIPS, this.m_auction_house_tips_toc);
        this.addSocketListener(SocketCommand.AUCTION_HOUSE_LOGS, this.m_auction_house_logs_toc);
    }

    protected reset(): void {
        super.reset();

    }

    //-------------协议接收 start -------------
    m_auction_house_info_toc(param: m_auction_house_info_toc) {
        if (!AuctionHouseDataCenter.instance.auctionInfo.has(param.op_type)) {
            AuctionHouseDataCenter.instance.auctionInfo.set(param.op_type, new Map());
        }

        let secondLayerMap: Map<number, p_auction_house_goods[]> = new Map();
        secondLayerMap.set(param.type, param.list);

        AuctionHouseDataCenter.instance.auctionInfo.set(param.op_type, secondLayerMap);

        this.dispatchEvent(ModuleCommand.UPDATE_AUCTION_HOUSE_INFO, param);
    }

    private m_auction_house_op_toc(param: m_auction_house_op_toc): void {
        this.dispatchEvent(ModuleCommand.UPDATE_AUCTION_HOUSE_OP, param);
        switch (param.op_type) {
            case EAuctionHouseOpType.LIST:
                TipsUtil.showTips("拍卖道具上架成功");
                break;
            case EAuctionHouseOpType.DELIST:
                TipsUtil.showTips("拍卖道具下架成功");
                break;
            case EAuctionHouseOpType.JOIN:
                TipsUtil.showTips("道具竞拍成功！");
                this.dispatchEvent(ModuleCommand.CLOSE_AUCTION_HOUSE_BIDDING_DIALOG);
                break;
            case EAuctionHouseOpType.BUY_FIXED:
                TipsUtil.showTips("道具一口价购买成功！");
                this.dispatchEvent(ModuleCommand.CLOSE_AUCTION_HOUSE_BIDDING_DIALOG);
                break;
        }
    }

    m_auction_house_tips_toc(param: m_auction_house_tips_toc) {

    }

    m_auction_house_logs_toc(param: m_auction_house_logs_toc) {
        AuctionHouseDataCenter.instance.setLogsList(param.list);
        this.dispatchEvent(ModuleCommand.UPDATE_AUCTION_HOUSE_LOG_DIALOG);
    }



    //-------------协议接收 end ---------------
}
