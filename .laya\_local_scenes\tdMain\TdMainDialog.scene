{"type": "BaseDialog", "props": {"width": 720, "height": 1280}, "compId": 2, "child": [{"type": "Box", "props": {"x": 0, "width": 720, "var": "topbox", "top": 102, "name": "topbox"}, "compId": 188, "child": [{"type": "<PERSON><PERSON>", "props": {"y": 129, "x": 455, "width": 73, "var": "scaleBtn", "skin": "common/btnYellow.png", "pivotY": 23, "pivotX": 37, "name": "scaleBtn", "label": "确认", "height": 46, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 247}, {"type": "TextInput", "props": {"y": 105, "x": 149.5, "width": 76, "var": "scalein<PERSON>", "skin": "common/bg9.png", "sizeGrid": "13,7,15,7", "promptColor": "#f8fffa", "prompt": "缩放", "height": 48, "fontSize": 26, "color": "#f8fffa"}, "compId": 246}, {"type": "TextInput", "props": {"y": 104, "x": 240, "width": 76, "var": "yoff", "skin": "common/bg9.png", "sizeGrid": "13,7,15,7", "promptColor": "#f8fffa", "prompt": "y偏移", "height": 48, "fontSize": 26, "color": "#f8fffa"}, "compId": 248}, {"type": "TextInput", "props": {"y": 105, "x": 334, "width": 76, "var": "fontsize", "skin": "common/bg9.png", "sizeGrid": "13,7,15,7", "promptColor": "#f8fffa", "prompt": "字放大", "height": 48, "fontSize": 26, "color": "#f8fffa"}, "compId": 249}, {"type": "TextInput", "props": {"y": 160.66666666666666, "x": 278, "width": 132, "skin": "common/bg9.png", "sizeGrid": "13,7,15,7", "promptColor": "#f8fffa", "prompt": "怪物皮肤", "height": 48, "fontSize": 26, "color": "#f8fffa", "var": "inputMonsterSkin", "text": "chuan_zhang"}, "compId": 251}, {"type": "Image", "props": {"y": 0, "x": 501, "var": "showtips", "skin": "tdMain/zztf_1.png", "sizeGrid": "0,0,0,0", "name": "showtips"}, "compId": 184, "child": [{"type": "HTMLDivElement", "props": {"y": 8, "x": 51, "width": 140, "var": "levelTxt", "height": 22, "runtime": "Laya.HTMLDivElement"}, "compId": 234}, {"type": "HTMLDivElement", "props": {"y": 41, "x": 51, "width": 140, "var": "remainnum", "height": 22, "runtime": "Laya.HTMLDivElement"}, "compId": 235}, {"type": "Image", "props": {"y": 65, "x": 17, "var": "autopic", "skin": "tdMain/img_tips2.png", "name": "autopic"}, "compId": 244}]}, {"type": "Image", "props": {"y": -37, "x": 528, "var": "remainbg", "skin": "tdTrial/fount_30.png", "name": "remainbg"}, "compId": 206, "child": [{"type": "FontClip", "props": {"y": 0, "x": 97, "width": 60, "var": "txtremain", "value": "2", "skin": "tdTrial/fount_31.png", "sheet": "1234567890", "name": "txtremain", "height": 33, "align": "center"}, "compId": 207}]}, {"type": "<PERSON><PERSON>", "props": {"y": 72.5, "x": 501, "var": "btnBack", "stateNum": 3, "skin": "tdTrial/btn_backend_exit.png"}, "compId": 200}, {"type": "ComboBox", "props": {"y": 172, "x": 528, "var": "cbFightMode", "skin": "common/select_frame.png", "labelSize": 24, "itemSize": 24}, "compId": 199}, {"type": "<PERSON><PERSON>", "props": {"y": -14, "x": 100, "var": "stopgamebtn", "skin": "godTrial/btn_exit.png", "pivotY": 30, "pivotX": 89.5, "name": "stopgamebtn"}, "compId": 236}, {"type": "<PERSON><PERSON>", "props": {"y": 35, "x": 360, "width": 200, "var": "startgamebtn", "skin": "common/btnGreen.png", "pivotY": 30, "pivotX": 89.5, "name": "startgamebtn", "height": 60, "labelSize": 24, "labelColors": "#b1e6bf,#b1e6bf,#b1e6bf", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 230, "child": [{"type": "Image", "props": {"y": 30, "x": 100, "var": "startgamepic", "skin": "tdMain/zztf_2.png", "name": "startgamepic", "anchorY": 0.5, "anchorX": 0.5}, "compId": 231}, {"type": "Label", "props": {"y": -31, "x": 48.5, "width": 105, "var": "passtext", "text": "第33关", "name": "passtext", "height": 28, "fontSize": 28, "color": "#ffeca1", "bold": true, "align": "center"}, "compId": 241}, {"type": "Image", "props": {"y": 59, "x": 23, "var": "imgTips", "skin": "tdMain/img_tips.png"}, "compId": 242}]}]}, {"type": "Box", "props": {"y": 130, "x": 10, "width": 100, "var": "boxLeftTop", "name": "boxLeftTop", "height": 350}, "compId": 220}, {"type": "<PERSON><PERSON>", "props": {"y": 768, "x": 658, "var": "ratebtn", "stateNum": 1, "skin": "fight2/rate1.png", "name": "ratebtn", "labelStrokeColor": "#7f0c01,#7f0c01,#7f0c01", "labelStroke": 3, "labelSize": 18, "labelPadding": "1,0,0,10", "labelColors": "#ffc969,#ffc969,#ffc969", "label": "X2"}, "compId": 201}, {"type": "<PERSON><PERSON>", "props": {"y": 503, "x": 93, "width": 105, "var": "lotterbtn", "stateNum": 1, "skin": "tdMain/zztf_7.png", "pivotY": 30, "pivotX": 80, "name": "lotterbtn", "labelStrokeColor": "#2b5f1f", "labelStroke": 5, "labelColors": "#f8fffa", "height": 104}, "compId": 240}, {"type": "Box", "props": {"x": -2, "width": 703, "var": "bottombox", "name": "bottombox", "mouseThrough": true, "height": 305, "bottom": 0}, "compId": 152, "child": [{"type": "<PERSON><PERSON>", "props": {"y": -103, "x": 717, "var": "onekeyBtn", "stateNum": 1, "skin": "tdMain/zztf_8.png", "pivotY": 30, "pivotX": 80, "name": "onekeyBtn", "labelStrokeColor": "#2b5f1f", "labelStroke": 5, "labelColors": "#f8fffa"}, "compId": 229}, {"type": "<PERSON><PERSON>", "props": {"y": -10, "x": 57, "width": 78, "var": "btnQuickFight", "stateNum": 1, "rotation": 0, "name": "btnQuickFight", "labelStrokeColor": "#7f450c", "labelStroke": 4, "labelSize": 24, "labelPadding": "60,0,0,0", "labelColors": "#fff9ef,#fff9ef,#fff9ef", "labelAlign": "center", "height": 81}, "compId": 208, "child": [{"type": "Image", "props": {"x": 39, "var": "showbox", "bottom": 41}, "compId": 209}, {"type": "<PERSON><PERSON><PERSON>", "props": {"y": 1, "x": 0, "playEvent": "mousedown", "runtime": "com.ui.res.animation.ButtonScaleEffectUI"}, "compId": 210}, {"type": "Label", "props": {"y": 70, "x": 0, "width": 96, "var": "lbTime", "text": "00:00:00", "strokeColor": "#7f450c", "stroke": 4, "height": 22, "color": "#fff9ef"}, "compId": 211}, {"type": "Poly", "props": {"y": 1.5, "x": -42.25, "renderType": "hit", "points": "4,25,41,-3,106,-37,277,82,2,80", "lineWidth": 1, "lineColor": "#ff0000", "fillColor": "#00ffff"}, "compId": 228}]}, {"type": "Box", "props": {"y": 107, "x": 8, "width": 251, "var": "boxMission", "height": 78, "bottom": 130}, "compId": 212, "child": [{"type": "Sprite", "props": {"y": 0, "x": 0, "texture": "tdMain/zztf_6.png"}, "compId": 219}, {"type": "HTMLDivElement", "props": {"y": 10, "x": 80, "width": 170, "var": "htmlMissionDesc", "height": 30, "runtime": "Laya.HTMLDivElement"}, "compId": 213}, {"type": "HTMLDivElement", "props": {"y": 40, "x": 80, "width": 170, "var": "htmlMissionProgress", "height": 30, "runtime": "Laya.HTMLDivElement"}, "compId": 214}]}, {"type": "<PERSON><PERSON>", "props": {"y": -47, "x": 540, "width": 95, "var": "btnBox", "stateNum": 1, "rotation": 0, "labelStrokeColor": "#7f450c", "labelStroke": 4, "labelSize": 24, "labelPadding": "60,0,0,0", "labelColors": "#fff9ef,#fff9ef,#fff9ef", "labelAlign": "center", "height": 143, "bottom": 219}, "compId": 222, "child": [{"type": "Image", "props": {"y": 98, "x": -3, "width": 100, "skin": "common/bg7.png", "height": 43}, "compId": 226}, {"type": "Label", "props": {"y": 121, "x": 50, "width": 88, "var": "lbBoxNum", "valign": "middle", "text": "X22", "stroke": 3, "pivotY": 11, "pivotX": 44, "height": 22, "fontSize": 25, "color": "#ffffff", "align": "center"}, "compId": 227}]}]}], "loadList": ["common/btnYellow.png", "common/bg9.png", "tdMain/zztf_1.png", "tdMain/img_tips2.png", "tdTrial/fount_30.png", "tdTrial/fount_31.png", "tdTrial/btn_backend_exit.png", "common/select_frame.png", "godTrial/btn_exit.png", "common/btnGreen.png", "tdMain/zztf_2.png", "tdMain/img_tips.png", "fight2/rate1.png", "tdMain/zztf_7.png", "tdMain/zztf_8.png", "tdMain/zztf_6.png", "common/bg7.png"], "loadList3D": []}