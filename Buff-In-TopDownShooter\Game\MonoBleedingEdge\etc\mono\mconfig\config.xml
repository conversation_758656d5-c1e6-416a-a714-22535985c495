<?xml version="1.0" encoding="utf-8"?>
<mconfig>
  <configuration>
    <handlers>
      <handler section="feature" 
	       type="Mono.MonoConfig.FeatureNodeHandler, mconfig, Version=*******, Culture=neutral, PublicKeyToken=null"
	       storageType="System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Mono.MonoConfig.FeatureNode, mconfig, Version=*******, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
      <handler section="configBlock"
	       type="Mono.MonoConfig.ConfigBlockNodeHandler, mconfig, Version=*******, Culture=neutral, PublicKeyToken=null"
	       storageType="System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Mono.MonoConfig.ConfigBlockBlock, mconfig, Version=*******, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
      <handler section="default"
	       type="Mono.MonoConfig.DefaultNodeHandler, mconfig, Version=*******, Culture=neutral, PublicKeyToken=null"
	       storageType="System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Mono.MonoConfig.DefaultNode, mconfig, Version=*******, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
      <handler section="defaultConfigFile"
	       type="Mono.MonoConfig.DefaultConfigFileNodeHandler, mconfig, Version=*******, Culture=neutral, PublicKeyToken=null"
	       storageType="System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Mono.MonoConfig.DefaultConfigFile, mconfig, Version=*******, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
    </handlers>
  </configuration>

  <feature name="AJAX" target="web">
    <description><![CDATA[
      Adds entries to your Web.config file which are required by any .NET 3.5 AJAX.NET application.
    ]]></description>
    <blocks>
      <block name="AJAX config sections"/>
      <block name="AJAX controls registration"/>
      <block name="AJAX compilation"/>
      <block name="AJAX HTTP handlers"/>
      <block name="AJAX HTTP modules"/>
      <block name="AJAX CodeDOM 3.5 settings"/>
      <block name="AJAX runtime settings"/>
      <block name="AJAX system.webServer"/>
      <block name="" />
    </blocks>
  </feature>

  <feature name="AJAX1" target="web">
    <description><![CDATA[
      Adds entries to your Web.config file which are required by any AJAX.NET 1.0 application.
    ]]></description>
    <blocks>
      <block name="AJAX1 config sections"/>
      <block name="AJAX1 controls registration"/>
      <block name="AJAX1 compilation"/>
      <block name="AJAX1 HTTP handlers"/>
      <block name="AJAX1 HTTP modules"/>
      <block name="AJAX1 system.web.extensions"/>
      <block name="AJAX1 system.webServer"/>
      <block name="" />
    </blocks>
  </feature>

  <feature name="DynamicData" target="web">
    <description><![CDATA[
      Adds entries to your Web.config file which are required by any ASP.NET DynamicData application.
    ]]></description>
    <blocks>
      <block name="AJAX config sections"/>
      <block name="AJAX controls registration"/>
      <block name="AJAX compilation"/>
      <block name="AJAX HTTP handlers"/>
      <block name="AJAX HTTP modules"/>
      <block name="AJAX system.web.extensions"/>
      <block name="AJAX system.webServer"/>

      <block name="DynamicData controls registration" />
      <block name="DynamicData compilation" />
      <block name="DynamicData HTTP modules" />
    </blocks>
  </feature>

  <feature name="SettingsMapProtection" target="web">
    <description><![CDATA[
      Adds an entry to your config file which will prevent download of the 'settings.map' file. The
file is used by the settings mapping manager to modify configuration settings depending on the operating
system under which your application runs. Add this feature only if you have your own custom 'settings.map'
file in the top-level directory of your ASP.NET application.
]]></description>
    <blocks>
      <block name="SettingsMap Handler"/>
    </blocks>
  </feature>

  <!-- configuration blocks required by the features -->
  <configBlock name="SettingsMap Handler">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="httpHandlers" defaultBlockName="system.web.httpHandlers"/>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
<add verb="*" path="settings.map" type="System.Web.HttpForbiddenHandler, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
]]>
    </contents>
  </configBlock>

  <!-- AJAX.NET 3.5 start -->
  <configBlock name="AJAX config sections">
    <requires>
      <section name="configuration">
	<section name="configSections" attachPoint="true"/>
      </section>
    </requires>

    <contents>
      <![CDATA[
<sectionGroup name="system.web.extensions" type="System.Web.Configuration.SystemWebExtensionsSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
        <sectionGroup name="scripting" type="System.Web.Configuration.ScriptingSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
          <section name="scriptResourceHandler" type="System.Web.Configuration.ScriptingScriptResourceHandlerSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication"/>
          <sectionGroup name="webServices" type="System.Web.Configuration.ScriptingWebServicesSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
            <section name="jsonSerialization" type="System.Web.Configuration.ScriptingJsonSerializationSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="Everywhere" />
            <section name="profileService" type="System.Web.Configuration.ScriptingProfileServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
            <section name="authenticationService" type="System.Web.Configuration.ScriptingAuthenticationServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
            <section name="roleService" type="System.Web.Configuration.ScriptingRoleServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
          </sectionGroup>
        </sectionGroup>
      </sectionGroup>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX controls registration">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="pages" defaultBlockName="system.web.pages">
	    <section name="controls" defaultBlockName="system.web.pages.controls"/>
	  </section>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
          <add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
          <add tagPrefix="asp" namespace="System.Web.UI.WebControls" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX compilation">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="compilation" defaultBlockName="system.web.compilation">
	    <section name="assemblies" defaultBlockName="system.web.compilation.assemblies"/>
	  </section>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
	    <add assembly="System.Core, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
            <add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
            <add assembly="System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
            <add assembly="System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>

]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX HTTP handlers">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="httpHandlers" defaultBlockName="system.web.httpHandlers"/>
	</section>
      </section>
    </requires>
    
    <contents>
      <![CDATA[
	<remove verb="*" path="*.asmx"/>
        <add verb="*" path="*.asmx" validate="false" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add verb="*" path="*_AppService.axd" validate="false" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" validate="false"/>   
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX HTTP modules">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="httpModules" defaultBlockName="system.web.httpModules"/>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
<add name="ScriptModule" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX CodeDOM 3.5 settings">
    <requires>
      <section name="configuration">
	<section name="system.codedom">
	  <section name="compilers" defaultBlockName="system.codedom.compilers"/>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
	<compiler language="c#;cs;csharp" extension=".cs" warningLevel="4"
                  type="Microsoft.CSharp.CSharpCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <providerOption name="CompilerVersion" value="v3.5"/>
          <providerOption name="WarnAsError" value="false"/>
        </compiler>
        <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4"
                  type="Microsoft.VisualBasic.VBCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <providerOption name="CompilerVersion" value="v3.5"/>
          <providerOption name="OptionInfer" value="true"/>
          <providerOption name="WarnAsError" value="false"/>
        </compiler>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX runtime settings">
    <requires>
      <section name="configuration">
	<section name="runtime"/>
      </section>
    </requires>

    <contents>
      <![CDATA[
	<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
        <dependentAssembly>
          <assemblyIdentity name="System.Web.Extensions" publicKeyToken="31bf3856ad364e35"/>
          <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
        </dependentAssembly>
        <dependentAssembly>
          <assemblyIdentity name="System.Web.Extensions.Design" publicKeyToken="31bf3856ad364e35"/>
          <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
        </dependentAssembly>
      </assemblyBinding>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX system.webServer">
    <requires>
      <section name="configuration">
	<section name="system.webServer"/>
      </section>
    </requires>

    <contents>
      <![CDATA[
      <validation validateIntegratedModeConfiguration="false"/>
      <modules>
        <remove name="ScriptModule" />
        <add name="ScriptModule" preCondition="managedHandler" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
      </modules>
      <handlers>
        <remove name="WebServiceHandlerFactory-Integrated"/>
        <remove name="ScriptHandlerFactory" />
        <remove name="ScriptHandlerFactoryAppServices" />
        <remove name="ScriptResource" />
        <add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode"
             type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode"
             type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add name="ScriptResource" preCondition="integratedMode" verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      </handlers>
]]>
    </contents>
  </configBlock>
  <!-- AJAX.NET 3.5 end -->

  <!-- AJAX.NET 1.0 start -->
  <configBlock name="AJAX1 config sections">
    <requires>
      <section name="configuration">
	<section name="configSections" attachPoint="true"/>
      </section>
    </requires>

    <contents>
      <![CDATA[
<sectionGroup name="system.web.extensions" type="System.Web.Configuration.SystemWebExtensionsSectionGroup, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <sectionGroup name="scripting" type="System.Web.Configuration.ScriptingSectionGroup, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
          <section name="scriptResourceHandler" type="System.Web.Configuration.ScriptingScriptResourceHandlerSection, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication"/>
        <sectionGroup name="webServices" type="System.Web.Configuration.ScriptingWebServicesSectionGroup, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
          <section name="jsonSerialization" type="System.Web.Configuration.ScriptingJsonSerializationSection, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="Everywhere" />
          <section name="profileService" type="System.Web.Configuration.ScriptingProfileServiceSection, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication" />
          <section name="authenticationService" type="System.Web.Configuration.ScriptingAuthenticationServiceSection, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication" />
        </sectionGroup>
      </sectionGroup>
    </sectionGroup>
      ]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX1 controls registration">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="pages" defaultBlockName="system.web.pages">
	    <section name="controls" defaultBlockName="system.web.pages.controls"/>
	  </section>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
<add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX1 compilation">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="compilation" defaultBlockName="system.web.compilation">
	    <section name="assemblies" defaultBlockName="system.web.compilation.assemblies"/>
	  </section>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
<add assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX1 HTTP handlers">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="httpHandlers" defaultBlockName="system.web.httpHandlers"/>
	</section>
      </section>
    </requires>
    
    <contents>
      <![CDATA[
<remove verb="*" path="*.asmx"/>
      <add verb="*" path="*.asmx" validate="false" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      <add verb="*" path="*_AppService.axd" validate="false" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      <add verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false"/>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX1 HTTP modules">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="httpModules" defaultBlockName="system.web.httpModules"/>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
<add name="ScriptModule" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX1 system.web.extensions">
    <requires>
      <section name="configuration">
	<section name="system.web.extensions"/>
      </section>
    </requires>

    <contents>
      <![CDATA[
<scripting>
      <webServices>
      <!-- Uncomment this line to customize maxJsonLength and add a custom converter -->
      <!--
      <jsonSerialization maxJsonLength="500">
        <converters>
          <add name="ConvertMe" type="Acme.SubAcme.ConvertMeTypeConverter"/>
        </converters>
      </jsonSerialization>
      -->
      <!-- Uncomment this line to enable the authentication service. Include requireSSL="true" if appropriate. -->
      <!--
        <authenticationService enabled="true" requireSSL = "true|false"/>
      -->

      <!-- Uncomment these lines to enable the profile service. To allow profile properties to be retrieved
           and modified in ASP.NET AJAX applications, you need to add each property name to the readAccessProperties and
           writeAccessProperties attributes. -->
      <!--
      <profileService enabled="true"
                      readAccessProperties="propertyname1,propertyname2"
                      writeAccessProperties="propertyname1,propertyname2" />
      -->
      </webServices>
      <!--
      <scriptResourceHandler enableCompression="true" enableCaching="true" />
      -->
    </scripting>
]]>
    </contents>
  </configBlock>

  <configBlock name="AJAX1 system.webServer">
    <requires>
      <section name="configuration">
	<section name="system.webServer"/>
      </section>
    </requires>

    <contents>
      <![CDATA[
<validation validateIntegratedModeConfiguration="false"/>
    <modules>
      <add name="ScriptModule" preCondition="integratedMode" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
    </modules>
    <handlers>
      <remove name="WebServiceHandlerFactory-Integrated" />
      <add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode"
           type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      <add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode"
           type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
      <add name="ScriptResource" preCondition="integratedMode" verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    </handlers>
]]>
    </contents>
  </configBlock>
  <!-- AJAX.NET 1.0 end -->

  <configBlock name="DynamicData controls registration">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="pages" defaultBlockName="system.web.pages">
	    <section name="controls" defaultBlockName="system.web.pages.controls"/>
	  </section>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
<add tagPrefix="asp" namespace="System.Web.DynamicData" assembly="System.Web.DynamicData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
]]>
    </contents>
  </configBlock>

  <configBlock name="DynamicData compilation">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="compilation" defaultBlockName="system.web.compilation">
	    <section name="assemblies" defaultBlockName="system.web.compilation.assemblies"/>
	  </section>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
<add assembly="System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
<add assembly="System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
<add assembly="System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
<add assembly="System.Data.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
<add assembly="System.Web.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
<add assembly="System.Web.Routing, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
<add assembly="System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
<add assembly="System.Web.DynamicData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
]]>
    </contents>
  </configBlock>

  <configBlock name="DynamicData HTTP modules">
    <requires>
      <section name="configuration">
	<section name="system.web">
	  <section name="httpModules" defaultBlockName="system.web.httpModules"/>
	</section>
      </section>
    </requires>

    <contents>
      <![CDATA[
<add name="UrlRoutingModule" type="System.Web.Routing.UrlRoutingModule, System.Web.Routing, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
]]>
    </contents>
  </configBlock>

  <!-- default contents for missing sections -->
  <default section="configuration" target="any">
    <![CDATA[
    <configuration>
    </configuration>
    ]]>
  </default>

  <default section="configSections" target="any">
    <![CDATA[
    <configSections>
    </configSections>
    ]]>
  </default>

  <default section="system.web" target="web">
    <![CDATA[
    <system.web>
    </system.web>
    ]]>
  </default>

  <default section="system.web.pages" target="web">
    <![CDATA[
<pages>
</pages>
]]>
  </default>

  <default section="system.web.pages.controls" target="web">
    <![CDATA[
<controls>
</controls>
]]>
  </default>

  <default section="system.web.compilation" target="web">
    <![CDATA[
<compilation debug="true">
</compilation>
]]>
  </default>

  <default section="system.web.compilation.assemblies" target="web">
    <![CDATA[
<assemblies>
</assemblies>
]]>
  </default>

  <default section="system.web.httpHandlers" target="web">
    <![CDATA[
<httpHandlers>
</httpHandlers>
]]>
  </default>

  <default section="system.web.httpModules" target="web">
    <![CDATA[
<httpModules>
</httpModules>
]]>
  </default>

  <default section="system.web.extensions" target="web">
    <![CDATA[
<system.web.extensions>
</system.web.extensions>
]]>
  </default>

  <default section="system.webServer" target="web">
    <![CDATA[
<system.webServer>
</system.webServer>
]]>
  </default>

  <default section="system.web.customErrors" target="web">
    <![CDATA[
<customErrors mode="RemoteOnly"/>
]]>
  </default>

  <default section="system.codedom" target="any">
    <![CDATA[
    <system.codedom>
    </system.codedom>
]]>
  </default>

  <default section="system.codedom.compilers" target="any">
    <![CDATA[
    <compilers>
    </compilers>
]]>
  </default>

  <default section="runtime" target="any">
    <![CDATA[
    <runtime>
    </runtime>
]]>
  </default>

  <!-- default config file definitions -->
  <defaultConfigFile name="web.config" fileName="Web.config" target="web">
    <section name="configuration">
      <section name="system.web">
	<section name="compilation" defaultBlockName="system.web.compilation"/>
	<section name="customErrors" defaultBlockName="system.web.customErrors"/>
      </section>
    </section>
  </defaultConfigFile>
</mconfig>
