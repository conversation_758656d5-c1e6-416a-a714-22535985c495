# Glossary

## Asset terms

**Asset**:

Any media or data that can be used in your game or project. An asset may come from a file created outside of Unity, like a 3D model, an audio file, or an image. You can also create some asset types in Unity, such as an **Animator Controller** , an Audio Mixer, or a **Render Texture**. See [Asset workflow](https://docs.unity3d.com/Manual/AssetWorkflow.html).

**Asset package**:

A collection of files and data from Unity projects or elements of projects, compressed and stored in one file, similar to Zip files, with the .unitypackage extension. **Asset packages** are a handy way of sharing and re-using Unity projects and collections of assets. See [Asset packages](https://docs.unity3d.com/Manual/AssetPackages.html).

**Asset Store**:

A growing library of free and commercial assets created by Unity and members of the community. Offers a wide variety of textures, models, animations, project examples, tutorials, and Editor extensions. See [Using the Asset Store](https://docs.unity3d.com/Manual/AssetStore.html).

**Package**:

A container that stores various types of features and assets for Unity. Packages are self-contained units that the Unity Package Manager can share across Unity projects. Most of the time, these are called _packages_, but occasionally they are called **Unity Package Manager (UPM) packages**. See [Unity&#39;s Package Manager](https://docs.unity3d.com/Manual/Packages.html).

## General terms

**Ignore file**:

A special file used in many **Version Control** Systems which specifies files to be excluded from **version control**. In Unity projects, several files can be excluded from **version control**. Using an Ignore File is the best way to achieve this. See [Using external version control systems with Unity](https://docs.unity3d.com/Manual/ExternalVersionControlSystemSupport.html).

**Project**:

In Unity, you use a project to design and develop a game. A project stores all of the files related to a game, such as the asset and **Scene** files. See [2D or 3D projects](https://docs.unity3d.com/Manual/2Dor3D.html).

**Version Control**:

A system for managing file changes. You can use Unity in conjunction with most **version control** tools, including **Perforce** , Git, Mercurial, and PlasticSCM. See [Version Control](https://docs.unity3d.com/Manual/VersionControl.html).

## Plastic SCM terms

**Checkin**:

Checkin is the act of submitting changes to the repo. You must enter a comment in the text box before you can check in your changes.

**Developer Mode**:

Developers have access to the branch explorer directly from inside Unity and easily switch branches.

**Gluon Mode**:

Artists can take advantage of the Gluon visualized interface and workflow from inside Unity.

**Organization**:

The organization handles different sets of repositories in the Cloud. Inside the organization, you can create as many repositories as you need.

**Workspace**:

Your workspace interacts with the version control, where you download the files and make the required changes for each checkin.
