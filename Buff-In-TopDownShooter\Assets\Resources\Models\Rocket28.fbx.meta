fileFormatVersion: 2
guid: 86fc77829a6e78d43a007a5d05e6be45
ModelImporter:
  serializedVersion: 22
  fileIDToRecycleName:
    100000: //RootNode
    100002: Cube
    100004: Rocket01
    100006: Rocket02
    100008: Rocket03
    100010: Rocket04
    100012: Rocket05
    100014: Rocket06
    100016: Rocket07
    100018: Rocket08
    100020: Rocket09
    100022: Rocket10
    100024: Rocket11
    100026: Rocket12
    100028: Rocket13
    100030: Rocket14
    100032: Rocket15
    100034: Rocket16
    100036: Rocket17
    100038: Rocket18
    100040: Rocket19
    100042: Rocket20
    100044: Rocket21
    100046: Rocket22
    100048: Rocket23
    100050: Rocket24
    100052: Rocket25
    100054: Rocket26
    100056: Rocket27
    100058: Rocket28
    100060: Rocket29
    100062: Rocket30
    400000: //RootNode
    400002: Cube
    400004: Rocket01
    400006: Rocket02
    400008: Rocket03
    400010: Rocket04
    400012: Rocket05
    400014: Rocket06
    400016: Rocket07
    400018: Rocket08
    400020: Rocket09
    400022: Rocket10
    400024: Rocket11
    400026: Rocket12
    400028: Rocket13
    400030: Rocket14
    400032: Rocket15
    400034: Rocket16
    400036: Rocket17
    400038: Rocket18
    400040: Rocket19
    400042: Rocket20
    400044: Rocket21
    400046: Rocket22
    400048: Rocket23
    400050: Rocket24
    400052: Rocket25
    400054: Rocket26
    400056: Rocket27
    400058: Rocket28
    400060: Rocket29
    400062: Rocket30
    2100000: MainMaterial
    2100002: Material.015
    2300000: //RootNode
    2300002: Cube
    2300004: Rocket01
    2300006: Rocket02
    2300008: Rocket03
    2300010: Rocket04
    2300012: Rocket05
    2300014: Rocket06
    2300016: Rocket07
    2300018: Rocket08
    2300020: Rocket09
    2300022: Rocket10
    2300024: Rocket11
    2300026: Rocket12
    2300028: Rocket13
    2300030: Rocket14
    2300032: Rocket15
    2300034: Rocket16
    2300036: Rocket17
    2300038: Rocket18
    2300040: Rocket19
    2300042: Rocket20
    2300044: Rocket21
    2300046: Rocket22
    2300048: Rocket23
    2300050: Rocket24
    2300052: Rocket25
    2300054: Rocket26
    2300056: Rocket27
    2300058: Rocket28
    2300060: Rocket29
    2300062: Rocket30
    3300000: //RootNode
    3300002: Cube
    3300004: Rocket01
    3300006: Rocket02
    3300008: Rocket03
    3300010: Rocket04
    3300012: Rocket05
    3300014: Rocket06
    3300016: Rocket07
    3300018: Rocket08
    3300020: Rocket09
    3300022: Rocket10
    3300024: Rocket11
    3300026: Rocket12
    3300028: Rocket13
    3300030: Rocket14
    3300032: Rocket15
    3300034: Rocket16
    3300036: Rocket17
    3300038: Rocket18
    3300040: Rocket19
    3300042: Rocket20
    3300044: Rocket21
    3300046: Rocket22
    3300048: Rocket23
    3300050: Rocket24
    3300052: Rocket25
    3300054: Rocket26
    3300056: Rocket27
    3300058: Rocket28
    3300060: Rocket29
    3300062: Rocket30
    4300000: Rocket28
    4300002: Rocket30
    4300004: Rocket29
    4300006: Rocket27
    4300008: Rocket26
    4300010: Rocket25
    4300012: Rocket23
    4300014: Rocket22
    4300016: Rocket21
    4300018: Rocket19
    4300020: Rocket18
    4300022: Rocket17
    4300024: Rocket16
    4300026: Rocket14
    4300028: Rocket13
    4300030: Rocket11
    4300032: Rocket12
    4300034: Rocket10
    4300036: Rocket03
    4300038: Rocket01
    4300040: Rocket04
    4300042: Rocket02
    4300044: Rocket09
    4300046: Rocket15
    4300048: Rocket05
    4300050: Rocket20
    4300052: Rocket24
    4300054: Rocket07
    4300056: Cube
    4300058: Rocket06
    4300060: Rocket08
    2186277476908879412: ImportLogs
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
