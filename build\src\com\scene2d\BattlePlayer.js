import { ILaya } from "ILaya";
import { Sprite } from "laya/display/Sprite";
import { Point } from "laya/maths/Point";
import { Browser } from "laya/utils/Browser";
import { Handler } from "laya/utils/Handler";
import { UrlConfig } from "../../game/UrlConfig";
import { AnimationManager } from "../managers/AnimationManager";
import { ConfigManager } from "../managers/ConfigManager";
import { DispatchManager } from "../managers/DispatchManager";
import { GameSoundManager } from "../managers/GameSoundManager";
import { LayerManager } from "../managers/LayerManager";
import { ResManager } from "../managers/ResManager";
import { ESkeletonAction, ESkeletonType } from "../modules/baseModules/skeleton/SkeletonData";
import { SkeletonManager } from "../modules/baseModules/skeleton/SkeletonManager";
import { DataCenter } from "../modules/DataCenter";
import { FightReplay } from "../modules/fight/data/FightConst";
import { GuaJiDataCenter } from "../modules/guaJi/data/GuaJiDataCenter";
import { HeroUtil } from "../modules/hero/util/HeroUtil";
import { MiscConst } from "../modules/misc_config/MiscConst";
import { RoleDataCenter, RoleLookWay } from "../modules/role/data/RoleDataCenter";
import { p_fight_actor } from "../proto/common/p_fight_actor";
import { p_kv } from "../proto/common/p_kv";
import { ConsoleUtils } from "../util/ConsoleUtils";
import { ObjectUtil } from "../util/ObjectUtil";
import { TipsUtil } from "../util/TipsUtil";
import { XmlFormatVo } from "../util/XmlFormatVo";
import { EffectMgr } from "./effect/EffectMgr";
import { FlyStatus } from "./effect/EffectType";
import { RoleMgr } from "./RoleMgr";
import { ActorType, AtkPosType, BuffDelType, CombatState, CombatType, FightTurnType, HitFly, HurtExtrasType, HurtType, RoleStandIndex, SceneType, SkillFullEffType, SkillFullType } from "./SceneConst";
import { SkillAttackVo } from "./skill/SkillAttactVo";
import { BPError } from "./BPError";
import { HeroExtKey } from "./BattleConst";
import { BELog } from "./BELog";
import { EHeroExtType } from "../modules/hero/data/HeroConsts";
import { GlobalConfig } from "../../game/GlobalConfig";
import { MemMonitor } from "../managers/MemMonitor";
import { BaseKv } from "../modules/BaseKv";
import { StringUtil } from "../util/StringUtil";
import { CfgCacheMapMgr } from "../cfg/CfgCacheMapMgr";
import { MatchConst } from "../auto/ConstAuto";
import { FightDuoPingVo } from "../modules/fight/vo/FightDuoPingVo";
import { RentHeroDataCenter } from "../modules/rentHero/data/RentHeroDataCenter";
var EnterType;
(function (EnterType) {
    EnterType[EnterType["Both"] = 0] = "Both";
    EnterType[EnterType["Player"] = 1] = "Player";
    EnterType[EnterType["Enemy"] = 2] = "Enemy";
    EnterType[EnterType["None"] = 3] = "None";
})(EnterType || (EnterType = {}));
export class BattlePlayer extends Sprite {
    constructor(elementMgr) {
        super();
        /**小回合 */
        this._steps = [];
        /**战斗类型 */
        this._matchType = 0;
        /**第几场战斗 */
        this._turnIndex = 0;
        /**第几回合 */
        this._roundIndex = 0;
        /**第几步 */
        this._stepIndex = 0;
        /**技能的步骤 */
        this._skillIndex = 0;
        /**已经运行技能的总次数 */
        this._skillRunAllIndex = 0;
        /**是否有玩家本人 */
        this._isMy = false;
        this._myRoundHurt = 0;
        /*战斗状态 */
        this.combat_state = CombatState.None;
        /**上次一次运行时间 */
        this._nextRunTime = 0;
        /**战斗重播？？？？ */
        this._review = false;
        /**战斗结束 */
        this._isRunStop = false;
        /**小回合结束了 */
        this._fightStepEnd = false;
        /**跳过回合时，是否显示死亡动作 */
        this._isShowDieAction = false;
        /**英雄攻击步骤需要处理的技能  */
        this._skills = [];
        /**英雄技能触发的技能 */
        this._trigSkillCache = new Map();
        /**触发技能的行动数据  */
        this._trigStepSkill = [];
        /**是否激活（外在原因需要暂停战斗） */
        this._enabled = true;
        /**战斗加速 */
        this._playSpeed = 1;
        /**战斗速战 */
        this._isWaittingJump = false;
        this.left_actors = [];
        this.right_actors = [];
        /**场外BUFF飘字相关 */
        this.buff_name_map = new Map();
        /**战前BUFF飘字相关 */
        this.status_name_map = new Map();
        /**网络异常尝试请求结果 */
        this.reqEndCount = 5;
        //变身数据 src_actor_sn -> skillAttackVo
        this.roleTransformationMap = new Map();
        /**加载角色技能动作到第回合了 */
        this.roundRoleSkillActionIndex = -1;
        this.roleSkillActionLoadedMap = new Map();
        this.roleSkillLoadList = [];
        /**所有的战斗统计 */
        this._allStepFightArr = [];
        /**战斗的结果 */
        this._finishFight = false;
        this._elementMgr = elementMgr;
        this.init();
        this.addEvent();
    }
    init() {
    }
    get matchType() {
        return this._matchType;
    }
    addEvent() {
        //角色的技能触发（特效触发目标）
        DispatchManager.addEventListener("ROLE_HURT" /* ROLE_HURT */, this, this.onRoleHurt);
        //夺屏
        DispatchManager.addEventListener("FIGHT_DUO_PING_CONTINUE" /* FIGHT_DUO_PING_CONTINUE */, this, this.onfightContinue);
        //跳过回合
        DispatchManager.addEventListener("FIGHT_JUMP_ROUND" /* FIGHT_JUMP_ROUND */, this, this.jumpRound);
        //检测点击角色
        DispatchManager.addEventListener("FIGHT_CHECK_CLICK_ACTOR" /* FIGHT_CHECK_CLICK_ACTOR */, this, this.checkClickActor);
        DispatchManager.addEventListener("DESTROY_MAP" /* DESTROY_MAP */, this, this.onDestroyMap);
        if (this._elementMgr._sceneType == SceneType.MAIN_BATTLE) {
            //回放
            DispatchManager.addEventListener("FIGHT_REVIEW" /* FIGHT_REVIEW */, this, this.review);
            //对话结束
            DispatchManager.addEventListener("GUIDE_DIALOGUE_END" /* GUIDE_DIALOGUE_END */, this, this.onPause);
            //指引相关
            DispatchManager.addEventListener("GUIDE_END_ID" /* GUIDE_END_ID */, this, this.onGuideEndId);
            //重连成功
            DispatchManager.addEventListener("RECONNECT_SUCCESS" /* RECONNECT_SUCCESS */, this, this.onSocketOpened);
            //收到的战斗结果
            DispatchManager.addEventListener("FIGHT_FINISH_END" /* FIGHT_FINISH_END */, this, this.onFinishEnd);
        }
        //ModuleCommand.SOCKET_OPENED
    }
    removeEvent() {
        DispatchManager.removeEventListener("ROLE_HURT" /* ROLE_HURT */, this, this.onRoleHurt);
        DispatchManager.removeEventListener("FIGHT_DUO_PING_CONTINUE" /* FIGHT_DUO_PING_CONTINUE */, this, this.onfightContinue);
        DispatchManager.removeEventListener("FIGHT_JUMP_ROUND" /* FIGHT_JUMP_ROUND */, this, this.jumpRound);
        DispatchManager.removeEventListener("FIGHT_CHECK_CLICK_ACTOR" /* FIGHT_CHECK_CLICK_ACTOR */, this, this.checkClickActor);
        DispatchManager.removeEventListener("FIGHT_REVIEW" /* FIGHT_REVIEW */, this, this.review);
        DispatchManager.removeEventListener("GUIDE_DIALOGUE_END" /* GUIDE_DIALOGUE_END */, this, this.onPause);
        DispatchManager.removeEventListener("GUIDE_END_ID" /* GUIDE_END_ID */, this, this.onGuideEndId);
        DispatchManager.removeEventListener("RECONNECT_SUCCESS" /* RECONNECT_SUCCESS */, this, this.onSocketOpened);
        DispatchManager.removeEventListener("FIGHT_FINISH_END" /* FIGHT_FINISH_END */, this, this.onFinishEnd);
        DispatchManager.removeEventListener("DESTROY_MAP" /* DESTROY_MAP */, this, this.onDestroyMap);
    }
    onSocketOpened() {
        if (this._finishFight == false) {
            return;
        }
        DispatchManager.dispatchEvent("FIGHT_END" /* FIGHT_END */, this._fight_data.match_type);
        this.timer.once(1400, this, this.finishFight);
    }
    onFinishEnd(type, target_id) {
        if (this._fight_data != null && this._fight_data.match_type == type && (this._fight_data.target_id == 0 || this._fight_data.target_id == target_id)) {
            this._finishFight = true;
            this.combat_state = CombatState.End;
        }
    }
    onGuideEndId(guideId) {
    }
    getClickRoleBase(x, y, ignoreCamp = 0) {
        let allRoleBySort = Array.from(this._elementMgr.allRole().values()).sort(function (role1, role2) {
            return role2.zOrder - role1.zOrder;
        });
        for (let i = 0; i < allRoleBySort.length; ++i) {
            let roleBase = allRoleBySort[i];
            if (!roleBase) {
                continue;
            }
            if (roleBase.camp == ignoreCamp) {
                continue;
            }
            if (roleBase.destroyed || roleBase.isDead()) {
                continue;
            }
            if (roleBase.isSummoned) {
                continue;
            }
            let x1 = roleBase.x - roleBase.bodyWidth / 2;
            let y1 = roleBase.y - roleBase.bodyHeight;
            let x2 = roleBase.x + roleBase.bodyWidth / 2;
            let y2 = roleBase.y;
            if (x1 <= x && x <= x2 && y1 <= y && y <= y2) {
                return roleBase;
            }
        }
        return null;
    }
    checkClickActor(e, param) {
        let x = e.stageX;
        let y = e.stageY + this._elementMgr.getOffsetY();
        if (!this._fight_data || !MiscConst.battle_is_can_look_hero) {
            return;
        }
        let roleBase = this.getClickRoleBase(x, y);
        if (!roleBase) {
            return;
        }
        if (this._fight_data.replay_type == FightReplay.REVIEW) {
            TipsUtil.showTips("暂不支持查看非己方实时战报信息");
            return;
        }
        let matchTypeCfg = ConfigManager.cfg_match_typeCache.get(this._fight_data.match_type);
        let isCanLook = matchTypeCfg && matchTypeCfg.is_can_look == 1;
        let isSelf = roleBase.camp == 1; //是否为玩家自身队伍英雄
        if (!isSelf && !isCanLook) {
            TipsUtil.showTips("本玩法不支持查看对手");
            return;
        }
        let role_id;
        if (this._fight_data.match_type == MatchConst.MATCH_TYPE_MOCK_BATTLE) { //模拟对战特殊处理
            role_id = isSelf ? DataCenter.myRoleID : this._fight_data.target_id;
        }
        else {
            role_id = isSelf ? DataCenter.myRoleID : this._fight_data.common_info.uid64;
        }
        let server_key = 0;
        if (this._fight_data.match_type == MatchConst.MATCH_TYPE_MOCK_BATTLE) { //模拟对战特殊处理
            let myRoleInfo;
            let otherRoleInfo;
            for (const vo of this._fight_data.replay_roles) {
                if (vo.profile.role_id == DataCenter.myRoleID) {
                    myRoleInfo = vo;
                }
                else {
                    otherRoleInfo = vo;
                }
            }
            if (myRoleInfo && myRoleInfo.profile) {
                server_key = isSelf ? myRoleInfo.profile.server_key : otherRoleInfo.profile.server_key;
            }
        }
        else {
            server_key = isSelf ? 0 : this._fight_data.common_info.ext64;
        }
        let match_type = this._fight_data.match_type;
        let hero_id = roleBase.data.actor_id;
        if (roleBase.isLord) {
            RoleDataCenter.instance.m_role_look_lord_tos(role_id, RoleLookWay.LOOK_NORMAL, server_key, hero_id, match_type);
        }
        else {
            if (hero_id < 0) {
                RentHeroDataCenter.instance.m_rent_hero_look_tos(hero_id, match_type, server_key);
            }
            else {
                RoleDataCenter.instance.m_role_look_hero_tos(role_id, RoleLookWay.LOOK_NORMAL, server_key, match_type, hero_id);
            }
        }
        ConsoleUtils.log("查看战斗英雄 ", { "玩家ID": role_id, "服务器Key": server_key, "英雄ID": hero_id });
    }
    /**设置战斗数据 */
    setFightInfo(toc = null, stepIdx, roundIdx, turnIdx = 0, review = false, isReback = false) {
        this.reset();
        BPError.trigger("BattlePlayer", "BattlePlayer.setFightInfo");
        this._fight_data = toc;
        this.reqEndCount = 5;
        this._matchType = toc.match_type;
        this._stepIndex = stepIdx;
        this._roundIndex = roundIdx;
        this._turnIndex = turnIdx;
        this._skillRunAllIndex = 0;
        this._review = review;
        this.destroySceneEff();
        ConsoleUtils.log("-----设置战斗数据", this._fight_data);
        if (this._fight_data.turns.length > 0) {
            //创建场景元素 包含双方英雄 和 领主
            this.setTurn();
            let match_type = this._fight_data.match_type;
            let cfg = ConfigManager.cfg_match_typeCache.get(match_type);
            let enterType = cfg ? cfg.enter_type : EnterType.Both;
            if (!isReback) {
                if (enterType == EnterType.Both || enterType == EnterType.Player) {
                    this.heroRunEnter(1);
                }
                if (enterType == EnterType.Both || enterType == EnterType.Enemy) {
                    this.heroRunEnter(2);
                }
            }
            else {
                this.initPreValues();
            }
            let tmepIndex = 0;
            this.timer.once(this.delayTime(200), this, function () {
                /**提前加载一个回合特效资源 */
                this.load(this._skillRunAllIndex);
                /**提前加载二个回合特效资源 */
                this._skillRunAllIndex++;
                tmepIndex = this._skillRunAllIndex;
            });
            /**提前加载二个回合特效资源 */
            this.timer.once(this.delayTime(1000), this, function () {
                this.load(tmepIndex);
            });
        }
        this.setGuideInfo();
    }
    /**英雄跑步入场 */
    heroRunEnter(camp) {
        let symbol = camp == 1 ? -1 : 1;
        for (const roleBase of this._elementMgr.allRole().values()) {
            if (roleBase.camp == camp) {
                roleBase.x = roleBase.birthPosX + 300 * symbol;
            }
            roleBase.move(roleBase.birthPosX, roleBase.birthPosY);
        }
    }
    allStepSkill() {
        if (!this._fightTurn) {
            return;
        }
        this._allStepFightArr = [];
        for (const fightRnd of this._fightTurn.rounds) {
            for (const steps of fightRnd.steps) {
                for (const stepSkill of steps.skills) {
                    this._allStepFightArr.push(stepSkill);
                }
            }
            for (const stepSkill of fightRnd.dead_skills) {
                this._allStepFightArr.push(stepSkill);
            }
        }
    }
    setBubbleInfo(bubble_info = []) {
        this.bubble_info = bubble_info;
    }
    setGuideInfo(guide_info = []) {
        this.guide_info = guide_info;
        if (this._fight_data.match_type == MatchConst.MATCH_TYPE_MAIN_BATTLE) {
            let cfg = ConfigManager.cfg_main_battleCache.get(GuaJiDataCenter.instance.curGuajiPass);
            for (let i = 0; i < cfg.guide.length; i++) {
                this.guide_info.push(cfg.guide[i]);
            }
            if (GuaJiDataCenter.instance.curGuajiPass == 1) {
                let roleBase = this._elementMgr.roleMgr.findRole(1);
                if (roleBase) {
                    roleBase.visible = false;
                }
            }
            else if (GuaJiDataCenter.instance.curGuajiPass == 12) {
                let roleBase = this._elementMgr.roleMgr.findRole(3);
                if (roleBase) {
                    roleBase.visible = false;
                }
            }
        }
        else if (this._fight_data.match_type == MatchConst.MATCH_TYPE_NEW_STORY) {
            this.guide_info = [];
        }
    }
    checkStop(index1, index2) {
        // if (!this._fight_data) {
        //     return false;
        // }
        BPError.trigger("BattlePlayer", "BattlePlayer.checkStop");
        let guideid = 0;
        let matchType = this._fight_data.match_type;
        if (matchType == MatchConst.MATCH_TYPE_MAIN_BATTLE
            || matchType == MatchConst.MATCH_TYPE_NEW_STORY
            || matchType == MatchConst.MATCH_TYPE_FIGHT_PLOT) {
            for (let i = 0; i < this.guide_info.length; i++) {
                if (this.guide_info[i][1] == index1 && this.guide_info[i][2] == index2) {
                    guideid = this.guide_info[i][0];
                    this.guide_info.splice(i, 1);
                    break;
                }
            }
        }
        if (guideid > 0) {
            this.onPause(true);
            DispatchManager.dispatchEvent("STOP_FIGHT_FOR_GUIDE1" /* STOP_FIGHT_FOR_GUIDE1 */, [this._fight_data.match_type, guideid]);
            return true;
        }
        let bubble_id = 0;
        if (matchType == MatchConst.MATCH_TYPE_FIGHT_PLOT) {
            for (let i = 0; i < this.bubble_info.length; i++) {
                if (this.bubble_info[i][1] == index1 && this.bubble_info[i][2] == index2) {
                    bubble_id = this.bubble_info[i][0];
                    this.bubble_info.splice(i, 1);
                    break;
                }
            }
        }
        if (bubble_id > 0) {
            // to do 
            this.setLastRunTime(this._nextRunTime + 1000);
            let roleBase = this._elementMgr.findRole(index1);
            if (roleBase) {
                let x = roleBase.x + (roleBase.camp == 1 ? 1 : -1) * (roleBase.bodyWidth / 2 - 30);
                let y = roleBase.y - roleBase.bodyHeight + 30;
                let isFlipX = roleBase.camp == 2;
                DispatchManager.dispatchEvent("SHOE_STORY_BUBBLE" /* SHOE_STORY_BUBBLE */, [bubble_id, x, y, isFlipX]);
            }
        }
        return false;
    }
    onPause(ispause) {
        this._enabled = !ispause;
    }
    playSpeed(val) {
        this._playSpeed = val;
    }
    /**设置战斗数据 和 创建角色*/
    setTurn() {
        let curTurnIdx = this._turnIndex;
        this._fightTurn = this._fight_data.turns[this._turnIndex];
        this._turnIndex = this._fightTurn.turn; //下一轮次战斗的索引
        if (this._roundIndex != 0 || this._stepIndex != 0) {
            if (this._roundIndex < this._fightTurn.rounds.length) {
                this._curRound = this._fightTurn.rounds[this._roundIndex++];
            }
            if (this._curRound) {
                this._steps = this._curRound.steps;
            }
        }
        this.setFightTurnObj(this._fightTurn);
        this.allStepSkill();
        this.createElements();
        this.setRoundRoleSkillActionRes();
        DispatchManager.dispatchEvent("NEW_TURN_INDEX" /* NEW_TURN_INDEX */, curTurnIdx);
    }
    setFightTurnObj(fightTurn) {
        for (let actor of fightTurn.left_actors) {
            this._elementMgr.roleMgr.addObjData(actor);
        }
        for (let actor of fightTurn.right_actors) {
            this._elementMgr.roleMgr.addObjData(actor);
        }
        for (let actor of fightTurn.left_wpns) {
            this._elementMgr.roleMgr.addObjData(actor);
        }
        for (let actor of fightTurn.right_wpns) {
            this._elementMgr.roleMgr.addObjData(actor);
        }
    }
    review(match_type) {
        if (this._fight_data == null) {
            return;
        }
        let matchType = this._fight_data.match_type;
        let turns = this._fight_data.turns;
        this._elementMgr.reset();
        this._elementMgr.setMapBg(matchType, false, true);
        let turnIdx = matchType == MatchConst.MATCH_TYPE_GOD_TRIAL ? turns.length - 1 : 0;
        this._fight_data.replay_type = FightReplay.REVIEW;
        this.setFightInfo(this._fight_data, 0, 0, turnIdx, true);
        this.timer.once(this.delayTime(1000), this, this.startBattle);
    }
    jumpRound({ turnNum = 0, roundNum = 0, stepNum = 0, anyStep = false, isShowTip = true, isShowDieAction = false, } = {}) {
        if (!this._fight_data) {
            return;
        }
        BPError.trigger("BattlePlayer", "BattlePlayer.jumpRound");
        turnNum = turnNum > 0 ? turnNum : this._turnIndex;
        roundNum = roundNum > 0 ? roundNum : turnNum > 0 ? 1 : this._roundIndex;
        stepNum = stepNum > 0 ? stepNum : turnNum > 0 || roundNum > 0 ? 0 : this._stepIndex;
        if (turnNum > this._fight_data.turns.length) {
            if (isShowTip) {
                TipsUtil.showTips(window.iLang.L2_YI_TIAO_GUO_0_HUI_HE.il());
            }
            return;
        }
        let fightTurn = this._fight_data.turns[turnNum - 1];
        let maxRound = fightTurn && fightTurn.rounds ? fightTurn.rounds.length : 0;
        if (!anyStep) { //正常速战跳过一半回合
            roundNum = Math.max(Math.ceil(maxRound / 2), 1);
            if (roundNum == 1) {
                stepNum = fightTurn.rounds[roundNum - 1].steps.length;
            }
        }
        else if (roundNum > maxRound) { //指令速战
            roundNum = maxRound - 1;
        }
        let maxStep = fightTurn.rounds[roundNum - 1].steps.length;
        if (stepNum > maxStep) {
            if (isShowTip) {
                TipsUtil.showTips("已跳过0回合");
            }
            return;
        }
        let curVal = this._turnIndex * 10000 + this._roundIndex * 100 + this._stepIndex;
        let jumpVal = turnNum * 10000 + roundNum * 100 + stepNum;
        if (jumpVal > curVal) {
            this._isWaittingJump = !anyStep && this.combat_state != CombatState.None;
            let skipNum = roundNum - this._roundIndex;
            if (roundNum == maxRound && stepNum == maxStep) {
                skipNum = Math.max(skipNum, 1);
                this._isWaittingJump = true;
            }
            if (isShowTip) {
                TipsUtil.showTips(window.i18n("已跳过{0}回合", [skipNum]));
            }
        }
        else {
            if (isShowTip) {
                TipsUtil.showTips("已跳过0回合");
            }
        }
        if (anyStep || jumpVal > curVal) {
            this._turnIndex = turnNum;
            this._roundIndex = roundNum;
            this._stepIndex = stepNum;
            this._isShowDieAction = isShowDieAction;
            if (anyStep || !this._isWaittingJump) {
                if (anyStep) {
                    DispatchManager.dispatchEvent("NEW_TURN_INDEX" /* NEW_TURN_INDEX */, Math.max(0, this._turnIndex - 1));
                }
                this.jumpProcess(turnNum, roundNum, stepNum, isShowDieAction);
            }
        }
    }
    jumpProcess(turnNum = 1, roundNum = 1, stepNum = 1, isShowDieAction = true) {
        BPError.trigger("BattlePlayer", "BattlePlayer.jumpProcess");
        let isWaittingJump = this._isWaittingJump;
        DispatchManager.dispatchEvent("UPDATE_JUMP_ROUND" /* UPDATE_JUMP_ROUND */, [turnNum, roundNum, stepNum]);
        this._elementMgr.reset();
        this._elementMgr.setMapBg(this._fight_data.match_type);
        this._turnIndex = turnNum;
        this._roundIndex = roundNum;
        this._stepIndex = stepNum;
        this._isShowDieAction = isShowDieAction;
        this._fightTurn = this._fight_data.turns[this._turnIndex - 1];
        this._turnIndex = this._fightTurn.turn;
        if (this._roundIndex != 0 || this._stepIndex != 0) {
            if (this._roundIndex - 1 < this._fightTurn.rounds.length) {
                this._curRound = this._fightTurn.rounds[this._roundIndex - 1];
            }
            if (this._curRound) {
                this._steps = this._curRound.steps;
            }
        }
        this.setFightTurnObj(this._fightTurn);
        this.createElements();
        this.initPreValues();
        let tmepIndex = 0;
        this.timer.once(this.delayTime(200), this, function () {
            /**提前加载一个回合特效资源 */
            this.load(this._skillRunAllIndex);
            /**提前加载二个回合特效资源 */
            this._skillRunAllIndex++;
            tmepIndex = this._skillRunAllIndex;
        });
        /**提前加载二个回合特效资源 */
        this.timer.once(this.delayTime(1000), this, function () {
            this.load(tmepIndex);
        });
        this.setLastRunTime(Browser.now() + (isWaittingJump ? 1500 : 0));
        // this.newStep();
        this.combat_state = CombatState.Execute;
        let round = this._curRound ? this._curRound.round : this._roundIndex;
        this.setRoundRoleSkillActionRes();
        DispatchManager.dispatchEvent("UPDATE_FIGHT_ROUND" /* UPDATE_FIGHT_ROUND */, [Math.max(0, round - 1)]);
    }
    /** 考虑到多场跳转时。必须恢复到最新数据状态 */
    initPreValues() {
        BPError.trigger("BattlePlayer", "BattlePlayer.initPreValues");
        let roundIdx = 0;
        let stepIdx = 0;
        let stepMaxIdx = 0;
        //let isShowSceneEff: boolean = false;
        let _roundInfo;
        let _stepInfo;
        let turns = this._fight_data.turns[this._turnIndex - 1];
        if (turns) {
            for (; roundIdx <= this._roundIndex - 1; roundIdx++) {
                _roundInfo = turns.rounds[roundIdx];
                if (!_roundInfo) {
                    continue;
                }
                stepIdx = 0;
                if (roundIdx == this._roundIndex - 1) {
                    stepMaxIdx = this._stepIndex - 1;
                }
                else {
                    stepMaxIdx = _roundInfo.steps.length - 1;
                }
                /**为啥要从这里跳出,从w7合并过来的,w7已去除 svn版本116937 --yanghaidong 2024.10.14*/
                // if (stepMaxIdx < 0) {
                //     /**
                //      * 战斗速战跳过回合的时候，会直接从这里跳出循环，导致不会进行一些回合前操作，所以在这里执行一遍
                //      */
                //     // _roundInfo = turns.rounds[this._roundIndex - 2];
                //     this.addBuffList(_roundInfo.add_buffs, 0, false);
                //     this.initRoleMaxHP(_roundInfo.star_add_hurts);
                //     this.initHurtAndAnger(_roundInfo.star_add_hurts);
                //     break;
                // }
                this.addBuffList(_roundInfo.add_buffs, 0, false);
                if (roundIdx == 0) {
                    this.initRoleMaxHP(_roundInfo.star_add_hurts);
                }
                this.initHurtAndAnger(_roundInfo.star_add_hurts);
                // if (stepMaxIdx < 0) {
                //     break;
                // }
                while (stepIdx <= stepMaxIdx) {
                    _stepInfo = _roundInfo.steps[stepIdx++];
                    if (_stepInfo) {
                        this.initHurtAndAnger(_stepInfo.buff_hurts1);
                        _stepInfo.skills.forEach((stepskill) => {
                            this.initHurtAndAnger(stepskill.skill_hurts);
                            this.initHurtAndAnger(stepskill.other_hurts);
                            this.addBuffList(stepskill.add_buffs, 0, false);
                            if (stepskill.skill_id) {
                                this._skillRunAllIndex++;
                            }
                        });
                        this.initHurtAndAnger(_stepInfo.buff_hurts2);
                        this.delBuffList(_stepInfo.del_buffs, 0, BuffDelType.BEFORE_STEP);
                    }
                }
                if (stepMaxIdx >= _roundInfo.steps.length - 1) {
                    this.initHurtAndAnger(_roundInfo.end_buff_hurts);
                    this.delBuffList(_roundInfo.del_buffs, 0, BuffDelType.AFTER_ROUND);
                    _roundInfo.dead_skills.forEach((stepskill) => {
                        this.initHurtAndAnger(stepskill.skill_hurts);
                        this.initHurtAndAnger(stepskill.other_hurts);
                        this.addBuffList(stepskill.add_buffs, 0, false);
                        if (stepskill.skill_id) {
                            this._skillRunAllIndex++;
                        }
                    });
                }
                if (turns.round_scenes.length > 0) {
                    // for(let i = 0; i < turns.round_scenes.length; i++){
                    //     let roundScene = turns.round_scenes[i];
                    //     if(roundScene.round <= roundIdx){
                    //         isShowSceneEff = true;
                    //         break;
                    //     }
                    // }
                    this.showSceneEffect(turns, _roundInfo, true);
                }
            }
        }
        this._elementMgr.allRole().forEach(item => {
            if (item.hp <= 0) {
                item.ToDead(this._isShowDieAction);
                item.deadHide();
            }
        });
    }
    initHurtAndAnger(hurts) {
        BPError.trigger("BattlePlayer", "BattlePlayer.initHurtAndAnger");
        hurts.forEach((hurt, key) => {
            let targeter = this._elementMgr.findRole(hurt.dest_actor_sn);
            if (targeter) {
                if (hurt.hurt_type == HurtType.HP || hurt.hurt_type == HurtType.TD_DAILY) {
                    if (targeter.hp > 0) {
                        targeter.hp = hurt.dest_val;
                        targeter.updateCurrentHp(hurt.dest_val, false);
                        // targeter._bloodView.updateBlood(hurt.dest_val, targeter.maxHp, true);
                    }
                    for (const fightKV of hurt.extras) {
                        if (fightKV.key == HurtExtrasType.SHIELD_HURT) {
                            let hurt_val2 = fightKV.val1;
                            targeter.addShield(hurt_val2);
                        }
                    }
                }
                else if (hurt.hurt_type == HurtType.REVIVE
                    || hurt.hurt_type == HurtType.SUMMON) {
                    if (hurt.hurt_type == HurtType.SUMMON)
                        targeter.visible = true;
                    targeter.hp = hurt.dest_val;
                    targeter.updateCurrentHp(hurt.dest_val, false);
                    // targeter._bloodView.updateBlood(hurt.dest_val, targeter.maxHp, true);
                }
                else {
                    this.trigHurtEff(targeter, hurt, false);
                }
            }
        });
        DispatchManager.dispatchEvent("FIGHT_TRIG_HURT" /* FIGHT_TRIG_HURT */, [hurts, this.matchType]);
    }
    initRoleMaxHP(hurts) {
        for (let hurt of hurts) {
            let targeter = this._elementMgr.findRole(hurt.dest_actor_sn);
            if (targeter && hurt.hurt_type == HurtType.HP_LIMIT) {
                targeter.initMaxHp = hurt.dest_val;
            }
        }
    }
    /**显示场外buff飘字 */
    showOffcourtBuff() {
        BPError.trigger("BattlePlayer", "BattlePlayer.showOffcourtBuff");
        let duration = 0;
        if (this._roundIndex != 0 || this._stepIndex != 0) {
            return duration;
        }
        let buffEffPos = [];
        if (this.buff_name_map.size > 0) {
            let interval = this.delayTime(MiscConst.battle_status_interval);
            for (let actorSn of this.buff_name_map.keys()) {
                let dataList = this.buff_name_map.get(actorSn);
                let actor_duration = 0;
                let roleBase = this._elementMgr.findRole(actorSn);
                //只有在角色可见时，才会显示飘字
                if (roleBase && roleBase.visible) {
                    let camp = roleBase.camp;
                    let show_x = roleBase.x;
                    let show_y = roleBase.y - 60;
                    for (let kv of dataList) {
                        if (kv.key == 8000330) { //战后疲劳
                            let pos = this._elementMgr.getCampCenterPos(camp);
                            buffEffPos[camp] = this._elementMgr.battleLayer.localToGlobal(Point.TEMP.setTo(pos[0], pos[1]));
                        }
                        this._elementMgr.hurtMgr.showBuffById(actorSn, kv.key, kv.val, this._elementMgr.mapLayer.roleTopLayer, show_x, show_y, 0);
                        actor_duration += interval;
                    }
                }
                duration = Math.max(actor_duration - interval, duration);
            }
            if (buffEffPos.length > 0) {
                DispatchManager.dispatchEvent("FIGHT_TRIG_OFFCOURT_BUFF" /* FIGHT_TRIG_OFFCOURT_BUFF */, [buffEffPos, duration]);
            }
        }
        return duration;
    }
    /**显示战前buff飘字 */
    showStatusName() {
        BPError.trigger("BattlePlayer", "BattlePlayer.showStatusName");
        let duration = 0;
        if (this._roundIndex != 0 || this._stepIndex != 0) {
            return duration;
        }
        if (this.status_name_map.size > 0) {
            let interval = this.delayTime(MiscConst.battle_status_interval);
            for (let actorSn of this.status_name_map.keys()) {
                let dataList = this.status_name_map.get(actorSn);
                let actor_duration = 0;
                for (let kv of dataList) {
                    let cfg = ConfigManager.cfg_battle_fly_nameCache.m_get(kv.key, kv.val);
                    let roleBase = this._elementMgr.findRole(actorSn);
                    //只有在角色可见时，才会显示飘字
                    if (cfg && roleBase && roleBase.visible) {
                        this._elementMgr.hurtMgr.showSkillStatus(cfg.content, this._elementMgr.mapLayer.roleTopLayer, actorSn, { posx: roleBase.x, posy: roleBase.y - 60 });
                        actor_duration += interval;
                    }
                }
                duration = Math.max(actor_duration - interval, duration);
            }
        }
        return duration ? duration : 0;
    }
    /**开始战斗啦 */
    startBattle() {
        this.setLastRunTime(Browser.now(), 0);
        /**首回合数据 */
        this.checkStop(0, 0);
        this.combat_state = CombatState.Execute;
        this.combat_state = CombatState.ShowOffcourtBuff;
        this.updateFrame(0);
    }
    /**第几场战斗 */
    newTurn() {
        BPError.trigger("BattlePlayer", "BattlePlayer.newTurn");
        let matchType = this._fight_data.match_type;
        //最后一个回合了
        if (this._turnIndex >= this._fight_data.turns.length) {
            this.gotoFinish();
            return;
        }
        let cfgMatchType = CfgCacheMapMgr.cfg_match_typeCache.get(matchType);
        //是否为车轮战
        let isAttritionWar = cfgMatchType.turn_type == FightTurnType.ATTRITION_WAR;
        //此处判断双方，哪一方存活，存活方队伍在后一轮次战斗中，不需要进行战前BUFF飘字
        //但神魔塔多队伍战斗特殊处理，每轮战斗都需要进行战前BUFF飘字
        let isLeftWin = this._fightTurn && this._fightTurn.turn_result == 1;
        let isRightWin = this._fightTurn && this._fightTurn.turn_result == 2;
        let dieCamp = !isAttritionWar ? 0 : isLeftWin ? 2 : 1;
        this.status_name_map.clear();
        this.buff_name_map.clear();
        this._elementMgr.reset(dieCamp);
        this._elementMgr.setMapBg(matchType);
        this.setTurn(); //在创建角色期间，会重新填充status_name_map和buff_name_map
        if (isRightWin || !isAttritionWar) {
            this.heroRunEnter(1);
        }
        if (isLeftWin || !isAttritionWar) {
            this.heroRunEnter(2);
        }
        let matchCfg = ConfigManager.cfg_match_typeCache.get(this._matchType);
        if (matchCfg && matchCfg.lineup_num > 1) { //需要播放二队进场提示，所以延时长一些
            this.timer.once(this.delayTime(2000), this, this.startBattle);
        }
        else {
            this.timer.once(this.delayTime(1000), this, this.startBattle);
        }
    }
    gotoFinish() {
        //处理战斗结果
        if (this._finishFight == false) {
            DispatchManager.dispatchEvent("FIGHT_END" /* FIGHT_END */, this._fight_data.match_type);
            if (this._fight_data.match_type == MatchConst.MATCH_TYPE_MAIN_BATTLE) {
                this.timer.once(this.delayTime(200), this, this.finishFight);
            }
            else {
                this.timer.once(this.delayTime(800), this, this.finishFight);
            }
            this.reqEndCount--;
            //防止网络异常
            if (this.reqEndCount > 0) {
                this.setLastRunTime(Browser.now(), 3000);
            }
            else {
                //网络不好可不能怨我
                this.combat_state = CombatState.End;
                this._finishFight = true;
            }
        }
    }
    onFront() {
    }
    onBack() {
    }
    /**每帧更新，毫秒*/
    updateFrame(interval) {
        if (this.combat_state == CombatState.None ||
            this.combat_state == CombatState.End) {
            return;
        }
        if (this._nextRunTime < Browser.now()) {
            if (this.combat_state == CombatState.ShowOffcourtBuff) {
                let duration = this.showOffcourtBuff();
                this.combat_state = CombatState.ShowStatusName;
                this.setLastRunTime(Browser.now() + duration);
            }
            else if (this.combat_state == CombatState.ShowStatusName) {
                let duration = this.showStatusName();
                this.combat_state = CombatState.Execute;
                this.setLastRunTime(Browser.now() + duration);
            }
            else {
                if (!this._atkRoleBase || this._atkRoleBase.checkCanAttack()) {
                    this.newSkill();
                }
            }
        }
    }
    /**夺屏后继续战斗 */
    onfightContinue(vo) {
        this.onEnabled(true);
        if (this._cacheAttackVo == null) {
            return;
        }
        if (vo.fullType == SkillFullType.Hero) {
            this.roleAtk(this._cacheAttackVo);
        }
        else { //直接释放技能
            this._elementMgr.skillMgr.UserSkill(this._cacheAttackVo);
            let maxtime = Math.max(this._cacheAttackVo.skillInfo.maxtime, ConfigManager.cfg_client_w3_skill_maxtime);
            this.setLastRunTime(Browser.now(), this._cacheAttackVo.skillInfo.maxtime);
        }
        this._cacheAttackVo = null;
    }
    /**停止执行战斗逻辑 */
    set isRunStop(end) {
        this._isRunStop = end;
    }
    /**激活战斗流程 */
    onEnabled(isEnable) {
        this._enabled = isEnable;
    }
    onRoleDead() {
    }
    /**角色显示伤害什么的 */
    onRoleHurt(effectId, vo, sceneType) {
        var _a, _b, _c, _d;
        BPError.trigger("BattlePlayer", "BattlePlayer.onRoleHurt");
        if (sceneType != this._elementMgr._sceneType) {
            return;
        }
        if (vo == null) {
            return;
        }
        if (!vo.stepSkill) {
            vo.recover();
            return;
        }
        let roleSkinId = (_a = vo.userRole) === null || _a === void 0 ? void 0 : _a.roleSkinId;
        let effect = ConfigManager.get_cfg_client_w3_effect(effectId, roleSkinId);
        let delay_harm = "";
        let delay = 0;
        /**伤害总显示时间 */
        let hurtTime = 0;
        /**间隔时间 */
        let intervalTime = 200;
        let isLastEff = effectId == vo.last_effect_id;
        let isLastHitEff = effectId == vo.last_hit_effect_id;
        if (effect != null) {
            //伤害的总延迟
            delay = EffectMgr.totalDelayHarm(effectId, roleSkinId);
            delay_harm = effect.delay_harm;
            hurtTime = delay + Math.max(effect.time_out - EffectMgr.firstDelayHarm(effectId, roleSkinId), 0);
        }
        else {
            let effectIds = vo.skillInfo.effect_id.split("|");
            if (effectIds.length > 0) {
                let eid = Number(effectIds[effectIds.length - 1]);
                let longTimeEff = ConfigManager.get_cfg_client_w3_effect(eid, roleSkinId);
                delay = EffectMgr.totalDelayHarm(eid, roleSkinId);
                delay_harm = longTimeEff.delay_harm;
                hurtTime = delay + Math.max(longTimeEff.time_out - EffectMgr.firstDelayHarm(effectId, roleSkinId), 0);
                isLastEff = eid == vo.last_effect_id;
                isLastHitEff = eid == vo.last_hit_effect_id;
            }
            else {
                hurtTime = 1000;
            }
        }
        let hurtList = [];
        if (vo.skillGroupList && vo.skillGroupList.length > 1) {
            vo.skillGroupList.every((skillGroup) => {
                hurtList.concat(skillGroup);
            });
        }
        else {
            hurtList = vo.stepSkill.skill_hurts;
        }
        //w9优化, 赵云反击技能可能被保护技能覆盖掉的bug
        if (this.getProtectorAndTarget(hurtList).length > 0) {
            hurtTime += 500;
        }
        if (vo.beginTime + vo.runTime > Browser.now() + hurtTime) {
            this.setLastRunTime(vo.beginTime, vo.runTime + intervalTime);
        }
        else {
            this.setLastRunTime(Browser.now(), hurtTime + intervalTime);
        }
        //英雄受击信息
        let actor_sn = vo.userRole != null ? vo.userRole.actor_sn : 0;
        let fly_type = HitFly.None;
        if (effect != null && effect.fly_type > 0) {
            fly_type = effect.fly_type;
        }
        let flash_params = "";
        if (effect != null && effect.flash_params.length > 0) {
            flash_params = effect.flash_params;
        }
        //容错,例如在SkillAttackVo.splitTarSkill中没有赋值skillGroupList
        // if(vo.skillGroupList == undefined){
        //     this.SetAttackSkill(vo);
        // }
        if (vo.skillGroupList && vo.skillGroupList.length > 1) {
            //策划需求：后端推送多个技能效果时，需要按照战斗编辑器设定的次数和延时播放
            //前端整合了技能效果，可在当前文档搜索SetAttackSkill
            this.showFightHurt2(vo.skillGroupList, actor_sn, effectId, (_b = vo.userRole) === null || _b === void 0 ? void 0 : _b.roleSkinId, delay_harm, fly_type, flash_params, false);
        }
        else {
            //如果只有一个技能效果，则需要按照战斗编辑器设定的次数和延时，对伤害进行分段
            this.showFightHurt(vo.stepSkill.skill_hurts, actor_sn, effectId, vo.stepSkill.skill_id, (_c = vo.userRole) === null || _c === void 0 ? void 0 : _c.roleSkinId, delay_harm, fly_type, flash_params, false, isLastHitEff);
        }
        //额外效果
        // if (vo.otherGroupList && vo.otherGroupList.length > 1) {
        //     this.showFightHurt2(vo.otherGroupList, actor_sn, effectId, delay_harm, fly_type, true);
        // }
        // else {
        this.showFightHurt(vo.stepSkill.other_hurts, actor_sn, effectId, vo.stepSkill.skill_id, vo.skillId, delay_harm, HitFly.None, flash_params, true, isLastHitEff);
        // }
        // //额外效果飘字
        // this.showEffectsFloatName(vo.stepSkill.other_hurts);
        let cfgSkill = ConfigManager.cfg_skillCache.get(vo.skillId);
        if (cfgSkill && cfgSkill.type == 2) {
            this.setFightHurtShock(vo.stepSkill.skill_hurts, effectId, (_d = vo.userRole) === null || _d === void 0 ? void 0 : _d.roleSkinId, delay_harm);
        }
        /**没伤害时 用buff里的目标模拟伤害表现 */
        if (vo.stepSkill.skill_hurts.length <= 0 && vo.stepSkill.add_buffs.length > 0) {
            let actorSnIds = [];
            for (const skillBuff of vo.stepSkill.add_buffs) {
                for (const pkv of skillBuff.actor_list) {
                    let actorId = pkv.key;
                    if (actorSnIds.indexOf(actorId) == -1) {
                        actorSnIds.push(actorId);
                        let targeter = this._elementMgr.findRole(actorId);
                        if (targeter) {
                            targeter.ToHurt(effect, null, { delay: delay });
                        }
                    }
                    this.updateBuffLayer(actorId, skillBuff.uid);
                }
            }
        }
        if (isLastEff) {
            if (vo.isLast == true) {
                let black_bg = vo.skillInfo ? vo.skillInfo.black_bg : null;
                this.timer.once(this.delayTime(hurtTime + intervalTime), this, function () {
                    this.resetRoleZOrder();
                    if (black_bg) {
                        let blackBgParams = black_bg.split("|");
                        let fadeOut = Number(blackBgParams[1]);
                        this.setMaskAlpha(0, fadeOut);
                    }
                });
            }
            let _this = this;
            this.timer.once(this.delayTime(delay), this, (attVo) => {
                // if (attVo.fightStep) {
                //     _this.visibleRoleCamp(attVo.fightStep.src_actor_sn, true);
                // }
                // if (attVo.skillInfo) { //抖屏
                //     _this.setCameraShock(attVo.skillInfo.shock);
                // }
                //飘buff的信息
                if (attVo.stepSkill) {
                    _this.addBuffList(attVo.stepSkill.add_buffs, 0, true, attVo.buffHurtMap);
                }
                if (attVo.skillInfo) {
                    DispatchManager.dispatchEvent("FIGHT_SKILL_END" /* FIGHT_SKILL_END */, attVo.stepSkill);
                }
                let whiteScreen = this._elementMgr.mapLayer._whiteScreen;
                if (whiteScreen && whiteScreen.isShow == true) {
                    whiteScreen.showEffect();
                    whiteScreen.hide();
                }
                if (attVo.userRole) {
                    attVo.userRole.hideWingEff(false);
                }
            }, [vo]);
        }
        this.timer.once(this.delayTime(5000), vo, this.recoverAtkVo, [vo]);
    }
    recoverAtkVo(vo) {
        if (vo) {
            vo.recover();
        }
    }
    /**修改 buff 的层数 目前只做 511类型 */
    updateBuffLayer(actorId, buff_type) {
        // if (buff_type == BuffDataVo.BUFF_TYPE_511) {
        //     let vo: BuffDataVo = BuffDataVo.getBuffDataVo(actorId, buff_type);
        //     if (vo) {
        //         vo.layer--;
        //     }
        //     if (vo.layer <= 0) {
        //         BuffDataVo.delBuff(actorId, buff_type);
        //     }
        //     DispatchManager.dispatchEvent(ModuleCommand.UPDATW_FIGHT_BUFF);
        // }
    }
    /**隐藏与我方阵容相同的英雄 */
    visibleRoleCamp(src_actor_sn, visible, exclude_actor_sn = 0) {
        if (RoleMgr.isOthers(src_actor_sn)) {
            //if(RoleMgr.isRight)
            if (RoleMgr.isRight(src_actor_sn) == true) {
                this._elementMgr.visibleRoleCamp(2, visible, exclude_actor_sn);
            }
            else {
                this._elementMgr.visibleRoleCamp(1, visible, exclude_actor_sn);
            }
        }
    }
    /**设置相机抖动 */
    setCameraShock(shock) {
        this._elementMgr.mapLayer.setCameraShock(shock);
    }
    setFightHurtShock(fightHurts, effectId = 0, heroSkinId, delay_harm = "") {
        BPError.trigger("BattlePlayer", "BattlePlayer.setFightHurtShock");
        let effect = ConfigManager.get_cfg_client_w3_effect(effectId, heroSkinId);
        let delay = 0;
        if (effect != null) {
            //伤害的总延迟
            delay = EffectMgr.firstDelayHarm(effectId, heroSkinId) + effect.delay;
            delay_harm = effect.delay_harm;
        }
        let hasHurt = false;
        for (const hurt of fightHurts) {
            let targeter = this._elementMgr.findRole(hurt.dest_actor_sn);
            if (targeter) {
                hasHurt = hurt.hurt_val < 0;
                break;
            }
        }
        //多段震屏
        if (hasHurt) {
            let shockParam = MiscConst.battle_hurt_shock;
            let interval = MiscConst.battle_hurt_interval;
            let arr = XmlFormatVo.GetVoArr(delay_harm);
            let delayTime = effect != null ? effect.delay : 0;
            if (arr.length > 0) {
                for (const vo of arr) {
                    delayTime += Math.max(interval, Number(vo.id));
                    for (const hurt of fightHurts) {
                        let targeter = this._elementMgr.findRole(hurt.dest_actor_sn);
                        if (targeter && hurt.hurt_val < 0) {
                            //单次飘字伤害量，超过5%血量时进行震屏
                            let rate = Math.floor(-hurt.hurt_val * Number(vo.vlaue1)) / targeter.maxHp;
                            if (rate >= 5) {
                                this.timer.once(delayTime, this, () => {
                                    this.setCameraShock(shockParam);
                                });
                                break;
                            }
                        }
                    }
                }
            }
            else {
                delayTime = Math.max(interval, delayTime);
                for (const hurt of fightHurts) {
                    let targeter = this._elementMgr.findRole(hurt.dest_actor_sn);
                    if (targeter && hurt.hurt_val < 0) {
                        //单次飘字伤害量，超过5%血量时进行震屏
                        let rate = -hurt.hurt_val / targeter.maxHp * 100;
                        if (rate >= 5) {
                            this.timer.once(delayTime, this, function () {
                                this.setCameraShock(shockParam);
                            });
                            break;
                        }
                    }
                }
            }
        }
    }
    /**本次出手连击次数和伤害统计 （战斗UI上的展示） */
    showBatter(actor_sn, battervalue, batternum, vo_hurtdelay) {
        if (batternum > 0 && battervalue.length > 1) {
            let value = 0;
            let num = 0;
            let delay = 0;
            for (let arrindex = 0; arrindex < vo_hurtdelay.length; arrindex++) {
                value += battervalue[arrindex];
                num += batternum;
                delay = vo_hurtdelay[arrindex].id;
                delay = delay < 300 ? 300 : delay;
                this.timer.once(this.delayTime(delay), this, this.delayShowBatter, [actor_sn, num, value], false);
            }
        }
    }
    delayTime(val) {
        return val / this._playSpeed;
    }
    /**通知界面显示 */
    delayShowBatter(actor_sn, num, value) {
        DispatchManager.dispatchEvent("UPDATE_BATTER" /* UPDATE_BATTER */, [RoleMgr.isMyTeam(actor_sn), num, value]);
    }
    /**触发伤害效果 */
    trigHurtEff(targeter, hurt, isShowBuff) {
        if (!targeter)
            return;
        if (hurt.hurt_type == HurtType.ADD_BUFF) {
            //添加一个新buff
            for (let i = 0; i < hurt.extras.length; ++i) {
                let fight_kv = hurt.extras[i];
                this.addRoleBuff(hurt.dest_actor_sn, fight_kv.key, fight_kv.val1, fight_kv.val2, fight_kv.val3, isShowBuff, hurt);
            }
        }
        else if (hurt.hurt_type == HurtType.DEL_BUFF_LAYER) {
            this._elementMgr.changeBuff(hurt.dest_actor_sn, hurt.extras, -1);
        }
        else if (hurt.hurt_type == HurtType.DEL_BUFF) {
            let isDoTransformation = false;
            for (const buffKV of hurt.extras) {
                let buffId = buffKV.key;
                let caster_sn = buffKV.val1;
                let shield = buffKV.val3;
                this._elementMgr.delBuff(hurt.dest_actor_sn, buffId, caster_sn);
                targeter.addShield(-shield);
                // isDoTransformation = this.roleTransform(hurt.dest_actor_sn, buffId, false) || isDoTransformation;
            }
            if (isDoTransformation) {
                this.setLastRunTime(this._nextRunTime, 800);
            }
        }
        else if (hurt.hurt_type == HurtType.HP_LIMIT) {
            targeter.setMaxHp(hurt.dest_val, hurt.hurt_val);
        }
        else if (hurt.hurt_type == HurtType.BUFF_MISS) {
        }
        else if (hurt.hurt_type == HurtType.REVIVE
            || hurt.hurt_type == HurtType.SUMMON) {
            //复活时，重新设置BUFF
            this._elementMgr.clearBuff(hurt.dest_actor_sn);
            for (let i = 0; i < hurt.extras.length; ++i) {
                let fight_kv = hurt.extras[i];
                this.addRoleBuff(hurt.dest_actor_sn, fight_kv.key, fight_kv.val1, fight_kv.val2, fight_kv.val3, isShowBuff, hurt);
            }
        }
    }
    /**
     * 保护人和受保护人
     */
    getProtectorAndTarget(prot_hurt) {
        let ret = [];
        for (let i = 0; i < prot_hurt.length; i++) {
            let prot_actor_sn = prot_hurt[i].prot_actor_sn;
            if (prot_actor_sn) {
                let targeter = this._elementMgr.findRole(prot_hurt[i].dest_actor_sn);
                if (targeter && targeter.actor_sn != prot_actor_sn && prot_actor_sn > 0) {
                    ret.push(prot_actor_sn);
                    ret.push(targeter.actor_sn);
                    break;
                }
            }
        }
        return ret;
    }
    /**显示伤害信息
     *
     * @param actor_sn 攻击者
     * @param isFightBack 反击伤害
     *
     * 如果技能数据只有一个Effect，则根据配置分段延时显示Effect伤害
    */
    showFightHurt(fightHurts, actor_sn, effectId = 0, skillId = 0, heroSkinId = 0, delay_harm = "", flyType = HitFly.None, flash_params = "", isOther = false, isLastEff = true) {
        BPError.trigger("BattlePlayer", "BattlePlayer.showFightHurt");
        let effect = ConfigManager.get_cfg_client_w3_effect(effectId, heroSkinId);
        let delay = 0;
        let totalDelay = 0;
        //统计伤害数据 要显示到战斗界面上的。（多少段伤害显示）
        let batternum = 0;
        let batternum_ = 0;
        let battervalue = [];
        let battervalue_ = [];
        if (effect != null) {
            //伤害的总延迟
            totalDelay = EffectMgr.totalDelayHarm(effectId, heroSkinId) - effect.delay;
            totalDelay = totalDelay > effect.delay ? totalDelay : effect.delay;
            delay = EffectMgr.firstDelayHarm(effectId, heroSkinId) + effect.delay;
            delay_harm = effect.delay_harm;
        }
        //这里需要优化
        let arr = XmlFormatVo.GetVoArr(delay_harm);
        if (arr.length > 1) {
            arr.forEach((percent) => {
                battervalue.push(0);
                battervalue_.push(0);
            });
        }
        else if (arr.length == 1 && !effect) {
            delay = arr[0].id;
        }
        //赵云，保护分摊伤害 start
        //保护者编号
        let prot_actor_sn = 0;
        //受保护者
        let targeter = null;
        for (const prot_hurt of fightHurts) {
            prot_actor_sn = prot_hurt.prot_actor_sn;
            if (prot_actor_sn) {
                targeter = this._elementMgr.findRole(prot_hurt.dest_actor_sn);
                break;
            }
        }
        /**保护英雄(如：赵云的分担分担伤害 ) */
        if (targeter && targeter.actor_sn != prot_actor_sn && prot_actor_sn > 0) {
            let protRole = this._elementMgr.findRole(prot_actor_sn);
            if (protRole && targeter) {
                protRole.clearAction();
                protRole.addAction(ESkeletonAction.RUN, 0, [targeter.x + (targeter.camp == 1 ? 80 : -80), targeter.y, 5000]);
                protRole.addAction(ESkeletonAction.RUN, totalDelay + 500, [protRole.birthPosX, protRole.birthPosY, 5000]);
            }
        }
        //赵云，保护分摊伤害 end
        //场景音效是否静音
        let isMute = this._elementMgr.isMute;
        //伤害分段总权重
        let weight = skillId > 0 ? EffectMgr.totalWeight(skillId, heroSkinId) : 100;
        //记录角色自身死亡状态
        let dieStateMap = {};
        /**技能受击信息 */
        for (const hurt of fightHurts) {
            let dest_actor_sn = hurt.dest_actor_sn;
            let targeter = this._elementMgr.findRole(dest_actor_sn);
            if (!(dest_actor_sn in dieStateMap)) {
                dieStateMap[dest_actor_sn] = !targeter || targeter.isDead();
            }
            /**除复活效果以外，死亡英雄不会参与其他效果 */
            let isReviveType = hurt.hurt_type == HurtType.REVIVE;
            let isSummon = hurt.hurt_type == HurtType.SUMMON;
            if (dieStateMap[dest_actor_sn]) {
                if (!isReviveType && !isSummon) {
                    continue;
                }
                dieStateMap[dest_actor_sn] = false;
            }
            if (hurt.hurt_type == HurtType.HP || hurt.hurt_type == HurtType.TD_DAILY) {
                if (hurt.hurt_val > 0) {
                    batternum++;
                }
                else {
                    batternum_++;
                }
            }
            else {
                let firstDelay = effect != null ? effect.delay : 0;
                let _this = this;
                this.timer.once(this.delayTime(firstDelay + (arr && arr[0] && arr[0].id || 0)), this, function () {
                    _this.trigHurtEff(targeter, hurt, true);
                });
            }
            let hurt_val = hurt.hurt_val;
            let hurt_val2 = 0;
            hurt.extras.forEach((pkv) => {
                if (pkv.key == HurtExtrasType.DI_XIAO) {
                    // hurt_val2 = pkv.val1;
                }
                else if (pkv.key == HurtExtrasType.SHIELD_HURT) {
                    hurt_val2 = pkv.val1; //对护盾的伤害
                    hurt_val = pkv.val2; //护盾抵消后的伤害
                }
                else if (pkv.key == HurtExtrasType.JIN_LIAO) { //禁疗
                    // hasJinLiao = true;
                }
            });
            if (targeter) {
                let delayTime = effect != null ? effect.delay : delay;
                this._showEffectFloatName(hurt, delayTime);
                if (actor_sn == 0 && hurt.hurt_type == HurtType.HP) {
                    if (hurt.hurt_val < 0) {
                        targeter.setfloorSkin(0, 0);
                    }
                    else {
                        targeter.setfloorSkin(0, targeter.camp);
                    }
                }
                //多少段伤害显示
                if (!isOther && arr.length > 1) {
                    let dest_val = targeter.hp;
                    let arrindex = 0;
                    for (const vo of arr) {
                        delayTime += vo.id;
                        arrindex++;
                        let rate = weight > 0 ? Number(vo.vlaue1) / weight : 0;
                        this._showHurt(targeter, hurt, {
                            effect: effect,
                            delayTime: delayTime,
                            hurt_val: hurt_val,
                            hurt_val2: hurt_val2,
                            rate: rate,
                            flyType: flyType,
                            flash_params: flash_params,
                            isLastEff: isLastEff && arrindex == arr.length,
                            isOther: isOther,
                            isMute: isMute,
                        });
                        // let final_hurt_val = Math.floor(hurt_val * rate);
                        // let final_hurt_val2 = Math.floor(hurt_val2 * rate);
                        // //最后一击才使用真实血量，比如：连击中最后一击才会击杀目标
                        // dest_val = isLastEff && arrindex == arr.length ? hurt.dest_val : Math.max(1, dest_val + final_hurt_val);
                        // //角色受击
                        // targeter.ToHurt(effect, hurt, {
                        //     hurt_val: final_hurt_val,
                        //     dest_val: dest_val,
                        //     hurt_val2: final_hurt_val2,
                        //     delay: delayTime,
                        //     flyType: HitFly.None,
                        //     isMute: isMute,
                        // });
                        // //伤害飘字
                        // this._elementMgr.hurtMgr.showFightHurt(targeter, hurt, this._elementMgr.mapLayer, targeter.x, targeter.y - 120, delayTime, hurt_val || hurt_val2, rate);
                        // let fightHurt = ObjectUtil.clone(hurt);
                        // fightHurt.hurt_val = Math.floor(final_hurt_val || final_hurt_val2);
                        // DispatchManager.dispatchEvent(ModuleCommand.FIGHT_TRIG_HURT, [[fightHurt]]);
                    }
                }
                else {
                    let delayTime = delay;
                    let vlaue1 = arr[0] && arr[0].vlaue1 || 100;
                    let rate = weight > 0 ? Number(vlaue1) / weight : 0;
                    if (isOther) { //额外伤害不进行伤害分段
                        rate = 1;
                    }
                    this._showHurt(targeter, hurt, {
                        effect: effect,
                        delayTime: delayTime,
                        hurt_val: hurt_val,
                        hurt_val2: hurt_val2,
                        rate: rate,
                        flyType: flyType,
                        flash_params: flash_params,
                        isLastEff: isLastEff,
                        isOther: isOther,
                        isMute: isMute,
                    });
                    // let final_hurt_val = hurt_val * rate;
                    // let final_hurt_val2 = hurt_val2 * rate;
                    // let dest_val: number = targeter.hp;
                    // //最后一击才使用真实血量，比如：连击中最后一击才会击杀目标
                    // dest_val = isLastEff ? hurt.dest_val : Math.max(1, dest_val + final_hurt_val);
                    // if (final_hurt_val > 0) {
                    //     dest_val = Math.min(dest_val, hurt.dest_val);
                    // }
                    // else if (final_hurt_val < 0) {
                    //     dest_val = Math.max(dest_val, hurt.dest_val);
                    // }
                    // //角色受击
                    // targeter.ToHurt(isOther == false ? effect : null, hurt, {
                    //     hurt_val: final_hurt_val,
                    //     dest_val: dest_val,
                    //     hurt_val2: final_hurt_val2,
                    //     delay: delayTime,
                    //     flyType: HitFly.None,
                    //     isMute: isMute,
                    // });
                    // //伤害飘字
                    // this._elementMgr.hurtMgr.showFightHurt(targeter, hurt, this._elementMgr.mapLayer, targeter.x, targeter.y - 120, delayTime, hurt_val || hurt_val2, rate);
                    // let fightHurt = ObjectUtil.clone(hurt);
                    // fightHurt.hurt_val = Math.floor(final_hurt_val || final_hurt_val2);
                    // DispatchManager.dispatchEvent(ModuleCommand.FIGHT_TRIG_HURT, [[fightHurt]]);
                }
                //如果有多个目标，则只有第一个目标会播放音效
                isMute = true;
            }
        }
    }
    /**如果技能数据有多个Effect，则根据配置延时显示Effect */
    showFightHurt2(fightHurts, actor_sn, effectId = 0, heroSkinId, delay_harm = "", flyType = HitFly.None, flash_params = "", isOther = false) {
        BPError.trigger("BattlePlayer", "BattlePlayer.showFightHurt2");
        let effect = ConfigManager.get_cfg_client_w3_effect(effectId, heroSkinId);
        let totalDelay = 0;
        if (effect != null) {
            totalDelay = EffectMgr.totalDelayHarm(effectId, heroSkinId) - effect.delay;
            totalDelay = totalDelay > effect.delay ? totalDelay : effect.delay;
            delay_harm = effect.delay_harm;
        }
        let protectIds = [];
        let lastIdxData = {};
        for (let i = 0; i < fightHurts.length; ++i) {
            let hurtList = fightHurts[i];
            for (const hurt of hurtList) {
                if (hurt.hurt_type == HurtType.HP || hurt.hurt_type == HurtType.TD_DAILY) {
                    lastIdxData[hurt.dest_actor_sn] = i; //记录每个角色的最后一次伤害的索引
                }
                //赵云，保护分摊伤害 start
                /**保护英雄(如：赵云的分担分担伤害 ) */
                let prot_actor_sn = hurt.prot_actor_sn;
                let targeter = this._elementMgr.findRole(hurt.dest_actor_sn);
                if (targeter && targeter.actor_sn != prot_actor_sn && prot_actor_sn > 0 && protectIds.indexOf(prot_actor_sn) == -1) {
                    let protRole = this._elementMgr.findRole(prot_actor_sn);
                    if (protRole && targeter) {
                        protRole.addAction(ESkeletonAction.RUN, 0, [targeter.x + (targeter.camp == 1 ? 80 : -80), targeter.y, 5000]);
                        protRole.addAction(ESkeletonAction.RUN, totalDelay + 500, [protRole.birthPosX, protRole.birthPosY, 5000]);
                        // protRole.curRoleFSMMgr.ChangeToProtect(targeter.x, targeter.y, protRole.camp == 1);
                        protectIds.push(prot_actor_sn);
                    }
                }
                //赵云，保护分摊伤害 end
            }
        }
        //记录角色自身死亡状态
        let dieStateMap = {};
        let delayTime = effect ? effect.delay : 0;
        let arr = XmlFormatVo.GetVoArr(delay_harm);
        for (let i = 0; i < fightHurts.length; ++i) {
            let hurtList = fightHurts[i];
            let index = Math.min(i, arr.length - 1);
            let vo = arr[index];
            let value = vo && vo.vlaue1 || 100;
            if (i < arr.length) {
                delayTime += vo ? vo.id : 0;
            }
            //场景音效是否静音
            let isMute = this._elementMgr.isMute;
            for (const hurt of hurtList) {
                let targeter = this._elementMgr.findRole(hurt.dest_actor_sn);
                if (!(hurt.dest_actor_sn in dieStateMap)) {
                    dieStateMap[hurt.dest_actor_sn] = !targeter || targeter.isDead();
                }
                /**除复活效果以外，死亡英雄不会参与其他效果 */
                let isReviveType = hurt.hurt_type == HurtType.REVIVE;
                let isSummon = hurt.hurt_type == HurtType.SUMMON;
                if (dieStateMap[hurt.dest_actor_sn]) {
                    if (!isReviveType && !isSummon) {
                        continue;
                    }
                    dieStateMap[hurt.dest_actor_sn] = false;
                }
                this._showEffectFloatName(hurt, delayTime);
                let _this = this;
                if (value !== null) {
                    this.timer.once(this.delayTime(delayTime), this, function () {
                        _this.trigHurtEff(targeter, hurt, true);
                    });
                }
                let hurt_val = hurt.hurt_val;
                let hurt_val2 = 0;
                // let hasShanBi = hurt.hurt_type == HurtType.SHAN_BI;
                // let hasWuDi = hurt.hurt_type == HurtType.WU_DI;
                // let hasJinLiao = false;
                hurt.extras.forEach((pkv) => {
                    if (pkv.key == HurtExtrasType.DI_XIAO) {
                        // hurt_val2 = pkv.val1;
                    }
                    else if (pkv.key == HurtExtrasType.SHIELD_HURT) {
                        hurt_val2 = pkv.val1; //对护盾的伤害
                        hurt_val = pkv.val2; //护盾抵消后的伤害
                    }
                    else if (pkv.key == HurtExtrasType.JIN_LIAO) { //禁疗
                        // hasJinLiao = true;
                    }
                });
                // let isShowHurtFont = hasShanBi || hasWuDi || hasJinLiao;
                if (targeter) {
                    if (actor_sn == 0 && hurt.hurt_type == HurtType.HP) {
                        if (hurt.hurt_val < 0) {
                            targeter.setfloorSkin(0, 0);
                        }
                        else {
                            targeter.setfloorSkin(0, targeter.camp);
                        }
                    }
                    this._showHurt(targeter, hurt, {
                        effect: effect,
                        delayTime: delayTime,
                        hurt_val: hurt_val,
                        hurt_val2: hurt_val2,
                        rate: 1,
                        flyType: flyType,
                        flash_params: flash_params,
                        isLastEff: i == lastIdxData[hurt.dest_actor_sn],
                        isOther: isOther,
                        isMute: isMute,
                    });
                    // //最后一击才使用真实血量，比如：连击中最后一击才会击杀目标
                    // let dest_val = i != lastIdxData[hurt.dest_actor_sn] ? Math.max(1, targeter.hp + hurt_val) : hurt.dest_val;
                    // //角色受击
                    // targeter.ToHurt(isOther ? null : effect, hurt, {
                    //     hurt_val: hurt_val,
                    //     dest_val: dest_val,
                    //     hurt_val2: hurt_val2,
                    //     hurt_rate: value,
                    //     delay: delayTime,
                    //     flyType: HitFly.None,
                    //     isMute: isMute,
                    // });
                    // //伤害飘字
                    // if (value !== null) {
                    //     this._elementMgr.hurtMgr.showFightHurt(targeter, hurt, this._elementMgr.mapLayer, targeter.x, targeter.y - 120, delayTime, hurt_val, 1);
                    //     let fightHurt = ObjectUtil.clone(hurt);
                    //     fightHurt.hurt_val = hurt_val;
                    //     DispatchManager.dispatchEvent(ModuleCommand.FIGHT_TRIG_HURT, [[fightHurt]]);
                    // }
                    //如果有多个目标，则只有第一个目标会播放音效
                    isMute = true;
                }
            }
        }
    }
    _showHurt(targeter, hurt, { effect = null, delayTime = 0, hurt_val = 0, hurt_val2 = 0, rate = 1, flyType = HitFly.None, flash_params = "", isLastEff = true, isOther = false, isMute = false, } = {}) {
        let final_hurt_val1 = Math.floor(hurt_val * rate);
        let final_hurt_val2 = Math.floor(hurt_val2 * rate);
        let dest_val = targeter.hp;
        //最后一击才使用真实血量，比如：连击中最后一击才会击杀目标
        dest_val = isLastEff ? hurt.dest_val : Math.max(1, dest_val + final_hurt_val1);
        if (final_hurt_val1 > 0) {
            dest_val = Math.min(dest_val, hurt.dest_val);
        }
        else if (final_hurt_val1 < 0) {
            dest_val = Math.max(dest_val, hurt.dest_val);
        }
        BELog.Log("伤害飘字, 延时：", delayTime);
        //角色受击
        targeter.ToHurt(isOther == false ? effect : null, hurt, {
            dest_val: dest_val,
            hurt_val: final_hurt_val1,
            hurt_val2: final_hurt_val2,
            delay: delayTime,
            flyType: flyType,
            flash_params: flash_params,
            isMute: isMute,
        });
        //伤害飘字，只有角色可见时才会显示伤害飘字
        if (targeter.visible) {
            this._elementMgr.hurtMgr.showFightHurt(targeter, hurt, this._elementMgr.mapLayer.roleTopLayer, targeter.x, targeter.y - 120, delayTime, hurt_val || hurt_val2, rate);
        }
        let fightHurt = ObjectUtil.clone(hurt);
        fightHurt.hurt_val = Math.floor(final_hurt_val1 || final_hurt_val2);
        DispatchManager.dispatchEvent("FIGHT_TRIG_HURT" /* FIGHT_TRIG_HURT */, [[fightHurt], this.matchType]);
    }
    /**触发技能 */
    trigSkillHandler(trigSkill) {
        BPError.trigger("BattlePlayer", "BattlePlayer.trigSkillHandler");
        if (!trigSkill.trig_skill_id) {
            return false;
        }
        let trig_skill_id = trigSkill.trig_skill_id;
        let cfg = ConfigManager.getTrigSkillCfg(trig_skill_id);
        let actor_sn = trigSkill.src_actor_sn;
        let attacker = this._elementMgr.findRole(actor_sn);
        // let cfgSkill: cfg_client_w3_skill = ConfigManager.cfg_client_w3_skillCache.get(trig_skill_id);
        let cfgSkill = ConfigManager.get_cfg_client_w3_skill(trig_skill_id, attacker === null || attacker === void 0 ? void 0 : attacker.roleSkinId);
        if (cfg && cfgSkill) {
            return false;
        }
        if (cfg && cfg.fight_show == 1) {
            let actor_sn = trigSkill.src_actor_sn;
            let targeter = this._elementMgr.findRole(actor_sn);
            //只有在角色可见时，才会显示飘字
            if (targeter && targeter.visible) {
                this._elementMgr.hurtMgr.showSkillStatus(cfg.skill_name, this._elementMgr.mapLayer.roleTopLayer, actor_sn, { posx: targeter.x, posy: targeter.y - 60 });
            }
        }
        else {
            //w10优化
            if (!cfg) {
                ConsoleUtils.error("配置cfg_trig_skill，找不到触发技能ID：", trig_skill_id);
            }
        }
        this.showFightHurt(trigSkill.skill_hurts, trigSkill.jump_actor_sn);
        this.showFightHurt(trigSkill.other_hurts, trigSkill.jump_actor_sn);
        this.addBuffList(trigSkill.add_buffs);
        return true;
    }
    /**添加英雄 buff
     *
     * @param before_hurt  1  表示攻击前添加的buff
     */
    addBuffList(add_buffs, before_hurt = 0, isShowBuff = true, add_buff_hurt = null) {
        BPError.trigger("BattlePlayer", "BattlePlayer.addBuffList");
        let isAddBuff = false;
        let trans_sn_skinid_list = [];
        for (const skillBuff of add_buffs) {
            if (skillBuff.before_hurt == before_hurt || isShowBuff == false) {
                isAddBuff = true;
                let buffId = skillBuff.uid;
                let buffCfg = ConfigManager.cfg_buffCache.get(buffId);
                if (buffCfg == null) {
                    TipsUtil.showDebugTips("buff不存在：" + buffId, true, "Errors");
                    continue;
                }
                let buffType = buffCfg.bus_type_id;
                let buffTypeCfg = ConfigManager.cfg_buff_typeCache.get(buffType);
                if (buffTypeCfg == null) {
                    TipsUtil.showDebugTips("buffType不存在：" + buffType, true, "Errors");
                    continue;
                }
                if (buffTypeCfg.client_show <= 0) {
                    continue;
                }
                for (let index = 0; index < skillBuff.actor_shield.length; index++) {
                    let shieldData = skillBuff.actor_shield[index];
                    let actorId = shieldData.key;
                    let shield = shieldData.val;
                    let targeter = this._elementMgr.findRole(actorId);
                    if (targeter && buffTypeCfg) {
                        targeter.addShield(shield);
                    }
                }
                for (let index = 0; index < skillBuff.actor_list.length; index++) {
                    let caster_sn = skillBuff.caster_sn;
                    let pkv = skillBuff.actor_list[index];
                    let actorSn = pkv.key;
                    let layer = pkv.val;
                    let shield = 0;
                    for (let index = 0; index < skillBuff.actor_shield.length; index++) {
                        let shieldData = skillBuff.actor_shield[index];
                        if (actorSn == shieldData.key) {
                            shield = shieldData.val;
                        }
                    }
                    let targeter = this._elementMgr.findRole(actorSn);
                    if (targeter && buffTypeCfg) {
                        //给角色添加 buff
                        //TODO
                        let buffId_hurt = add_buff_hurt === null || add_buff_hurt === void 0 ? void 0 : add_buff_hurt.get(actorSn);
                        let hurt = null;
                        if (buffId_hurt) {
                            hurt = buffId_hurt.get(buffId);
                        }
                        this._elementMgr.addBuff(actorSn, buffId, caster_sn, layer, shield, hurt);
                        targeter.refreshBuff();
                        //只有在角色可见时，才会显示飘字
                        if (targeter.visible && isShowBuff == true && buffTypeCfg.is_offcourt != 1) { //场外BUFF不在这里飘字
                            //buff产生的作用
                            this.showBuff(targeter.actor_sn, buffCfg, buffTypeCfg, layer, this._elementMgr.mapLayer.roleTopLayer, targeter.x, targeter.y - 60, 0);
                            // this._elementMgr.hurtMgr.showBuff(targeter.actor_sn, buffCfg, buffTypeCfg, layer, this._elementMgr.mapLayer.roleTopLayer, targeter.x, targeter.y - 60, 0);
                        }
                        let trans_sn_skinid = this.checkRoleBuffTransform(actorSn, buffId, true);
                        if (trans_sn_skinid) {
                            trans_sn_skinid_list.push(trans_sn_skinid);
                        }
                    }
                }
            }
        }
        if (trans_sn_skinid_list.length) {
            this.doRoleBuffTransform(trans_sn_skinid_list);
            // this.setLastRunTime(this._nextRunTime, 2000);
        }
        if (add_buffs.length > 0) {
            DispatchManager.dispatchEvent("UPDATW_FIGHT_BUFF" /* UPDATE_FIGHT_BUFF */);
            //this.setLastRunTime(this._nextRunTime, 500);
        }
        return isAddBuff;
    }
    /**回合前buff变身 */
    doRoleBuffTransform(trans_sn_skinid_list) {
        //回合前buff变身
        let transDuration = 3000;
        for (let i = 0; i < trans_sn_skinid_list.length; i++) {
            let sn_skinid = trans_sn_skinid_list[i];
            let targeter = this._elementMgr.findRole(sn_skinid.key);
            if (!targeter) {
                continue;
            }
            let isCurrTransformation = targeter.avtoar.isTransforming();
            if (isCurrTransformation) {
                continue;
            }
            this.timer.once(this.delayTime(500), this, () => {
                //变身
                targeter.transformation(sn_skinid.val + "");
            });
            this.fightRoleZOrder([targeter.actor_sn]);
            this.setMaskAlpha(0.8, 200);
            this.timer.once(this.delayTime(transDuration - 500), this, () => {
                this.setMaskAlpha(0, 400);
                this.resetRoleZOrder();
            });
            //然后英雄播放普攻动作，身上飘特效E209_skill4
            this._elementMgr.effectMgr.setDataId(7030093, targeter, targeter);
        }
        this.setLastRunTime(this._nextRunTime, transDuration);
        return transDuration;
    }
    checkRoleBuffTransform(actorSn, buffId, isAdd) {
        let ret_kv = null;
        let targeter = this._elementMgr.findRole(actorSn);
        let buffCfg = ConfigManager.cfg_buffCache.get(buffId);
        let buffTypeCfg = ConfigManager.cfg_buff_typeCache.get(buffCfg === null || buffCfg === void 0 ? void 0 : buffCfg.bus_type_id);
        if (buffTypeCfg == null || buffTypeCfg.skin_id_exchange == 0) {
            return ret_kv;
        }
        //是否变身
        var skin_id_exchange = buffTypeCfg.skin_id_exchange;
        //if (buffId == 40000231) {
        //   skin_id_exchange = 4054008;
        //}
        var isCurrTransformation = targeter.avtoar.isTransforming();
        var isDoTransformation = false;
        if (skin_id_exchange) {
            isDoTransformation = true;
            ret_kv = new p_kv();
            ret_kv.key = actorSn;
            ret_kv.val = skin_id_exchange;
        }
        return ret_kv;
    }
    /**给角色单独添加buff */
    addRoleBuff(actorSn, buff_id, caster_sn, layer, shield, isShowBuff = true, hurt) {
        BPError.trigger("BattlePlayer", "BattlePlayer.addRoleBuff");
        let targeter = this._elementMgr.findRole(actorSn);
        let buffCfg = ConfigManager.cfg_buffCache.get(buff_id);
        if (buffCfg == null) {
            TipsUtil.showDebugTips("buff不存在：" + buff_id, true, "Errors");
        }
        let buffType = buffCfg ? buffCfg.bus_type_id : 0;
        let buffTypeCfg = ConfigManager.cfg_buff_typeCache.get(buffType);
        if (buffTypeCfg && buffTypeCfg.client_show > 0) {
            if (targeter) {
                this._elementMgr.addBuff(actorSn, buff_id, caster_sn, layer, shield, hurt);
                targeter.addShield(shield);
                //只有在角色可见时，才会显示飘字
                if (targeter.visible && isShowBuff == true) {
                    //buff飘字
                    this.showBuff(targeter.actor_sn, buffCfg, buffTypeCfg, layer, this._elementMgr.mapLayer.roleTopLayer, targeter.x, targeter.y - 60, 0);
                    // this._elementMgr.hurtMgr.showBuff(targeter.actor_sn, buffCfg, buffTypeCfg, layer, this._elementMgr.mapLayer.roleTopLayer, targeter.x, targeter.y - 60, 0);
                }
            }
            DispatchManager.dispatchEvent("UPDATW_FIGHT_BUFF" /* UPDATE_FIGHT_BUFF */);
        }
    }
    showBuff(actor_sn, cfgBuff, cfgBuffType, layer, parent, x, y, delay) {
        this._elementMgr.hurtMgr.showBuff(actor_sn, cfgBuff, cfgBuffType, layer, parent, x, y, delay);
    }
    /**移除英雄的  buff */
    delBuffList(del_buffs, delay = 0, buffDelType) {
        BPError.trigger("BattlePlayer", "BattlePlayer.delBuffList");
        let isDoTransformation = false;
        for (const actorBuff of del_buffs) {
            let targeter = this._elementMgr.findRole(actorBuff.actor_sn);
            if (targeter) {
                //乐不思蜀buff
                // if (actorBuff.hurt_type == 99) {
                //     this._elementMgr.hurtMgr.show(HurtManager.FONT_NORMAL, 0, "fight/buff_48.png", this._elementMgr.mapLayer.roleTopLayer, targeter.x, targeter.y - 120, targeter.actor_sn, 0);
                // }
                let currbuffDelType = actorBuff.buff_del_type || BuffDelType.AFTER_ROUND;
                if (buffDelType != currbuffDelType) {
                    // if(currbuffDelType == BuffDelType.BEFORE_ROUND){
                    //     ConsoleUtils.log("------del buff, BEFORE_ROUND buff = ", actorBuff);
                    // }
                    continue;
                }
                for (const buffKV of actorBuff.buff_list2) {
                    this._elementMgr.delBuff(actorBuff.actor_sn, buffKV.key, buffKV.val1);
                    let shield = buffKV.val3;
                    targeter.addShield(-shield);
                    // isDoTransformation = this.roleTransform(actorBuff.actor_sn, buffKV.key, false) || isDoTransformation;
                }
            }
        }
        if (isDoTransformation) {
            this.setLastRunTime(this._nextRunTime, 800);
        }
        if (del_buffs.length > 0) {
            DispatchManager.dispatchEvent("UPDATW_FIGHT_BUFF" /* UPDATE_FIGHT_BUFF */);
        }
    }
    getActorClientExt() {
        if (!this._fight_data)
            return [];
        if (!ConfigManager.cfg_match_typeCache.has(this._fight_data.match_type))
            return [];
        let matchCfg = ConfigManager.cfg_match_typeCache.get(this._fight_data.match_type);
        let kv = new p_kv();
        kv.key = HeroExtKey.CLIENT_SHOW_BEAST;
        kv.val = matchCfg && matchCfg.is_show_beast || 0;
        return [kv];
    }
    getActorBuffList(extList, ignoreList) {
        let list = [];
        for (const pkv of extList) {
            if (ignoreList.indexOf(pkv.key) >= 0) {
                continue;
            }
            if (pkv.key == HeroExtKey.WIN_BUFF && pkv.val > 0) { //战后疲劳
                let kv = new p_kv();
                kv.key = 8000330;
                kv.val = pkv.val;
                list.push(kv);
            }
            else if (pkv.key == HeroExtKey.FAIL_BUFF && pkv.val > 0) { //哀兵必胜
                let kv = new p_kv();
                kv.key = 8000333;
                kv.val = pkv.val;
                list.push(kv);
            }
        }
        return list;
    }
    _createActor(camp, data, delayIndex, posIndex = -1) {
        // if(data["isDie"]){
        //     return;
        // }
        if (posIndex < 0) {
            posIndex = data.actor_sn % 100 + (camp - 1) * 100;
        }
        let pos = this._elementMgr.getRolePos(posIndex);
        let hasRole = this._elementMgr.hasRole(data.actor_sn);
        let roleBase = this._elementMgr.addRoleByPos(data, camp, pos, { delayIndex: delayIndex, clientExt: this.getActorClientExt() });
        roleBase.clearShield();
        for (const pkv of data.buff_list) {
            this.addRoleBuff(data.actor_sn, pkv.key, pkv.val1, pkv.val2, pkv.val3, false, null);
        }
        this.buff_name_map.set(data.actor_sn, this.getActorBuffList(data.hero_ext, hasRole ? [EHeroExtType.FAIL_BUFF] : []));
        if (!hasRole) {
            this.status_name_map.set(data.actor_sn, data.skill_levels);
        }
        roleBase.refreshBuff();
        roleBase.visible = data.hp != 0;
    }
    _createActors(camp, actorList) {
        let delayIndex = 0;
        this._elementMgr.clearBuffByCamp(camp);
        for (const data of actorList) {
            this._createActor(camp, data, delayIndex);
            delayIndex++;
        }
    }
    _createLord(camp, lord) {
        //TODO 领主
        if (!lord) {
            return;
        }
        let cfg = CfgCacheMapMgr.cfg_lord_baseCache.get(lord.lord_id);
        if (cfg == null) {
            return;
        }
        let actorData = new p_fight_actor();
        actorData.type_id = lord.lord_id;
        actorData.actor_sn = lord.sn;
        actorData.actor_id = lord.lord_id;
        actorData.hp = 99999999;
        actorData.actor_type = ActorType.Lord;
        let standIndex = camp == 1 ? RoleStandIndex.LeftLord : RoleStandIndex.RightLord;
        this._createActor(camp, actorData, 0, standIndex);
    }
    /**创建场景元素 包含双方英雄 和 领主 */
    createElements() {
        if (this._fightTurn.left_name != DataCenter.myRoleName && this._fight_data.common_info.leftname != DataCenter.myRoleName && this._fight_data.common_info.targetname == DataCenter.myRoleName) {
            this.left_actors = this._fightTurn.right_actors;
            this.right_actors = this._fightTurn.left_actors;
        }
        else {
            this.left_actors = this._fightTurn.left_actors;
            this.right_actors = this._fightTurn.right_actors;
        }
        this._createActors(1, this.left_actors);
        this._createActors(2, this.right_actors);
        this._createLord(1, this._fightTurn.left_lords[0]);
        this._createLord(2, this._fightTurn.right_lords[0]);
    }
    /**设置战斗大回合数据 */
    newRound() {
        if (!this._fightTurn || this._isRunStop == true) {
            return;
        }
        if (this._roundIndex >= this._fightTurn.rounds.length) {
            //处理指引相关
            if (this.checkStop(0, 100) == false) {
                this.newTurn();
            }
            return;
        }
        this._curRound = this._fightTurn.rounds[this._roundIndex];
        //执行小回合的数据
        this._steps = this._curRound.steps;
        this._stepIndex = 0;
        this._roundIndex++;
        this._fightStepEnd = false;
        //回合开始前的buff或伤害
        let _this = this;
        this.timer.once(this.delayTime(500), this, () => {
            if (_this._curRound && _this._curRound.add_buffs.length > 0) {
                _this.addBuffList(_this._curRound.add_buffs);
            }
            if (_this._curRound && _this._curRound.star_add_hurts.length > 0) {
                //伤害
                if (_this._roundIndex == 1) {
                    _this.initRoleMaxHP(_this._curRound.star_add_hurts);
                }
                _this.showFightHurt(_this._curRound.star_add_hurts, 0);
                //飘字
                _this.showEffectsFloatName(_this._curRound.star_add_hurts);
            }
            _this.delBuffList(_this._curRound.del_buffs, 0, BuffDelType.BEFORE_ROUND);
        });
        //回合前变身
        this.roleTransformationMap.clear();
        this._steps.forEach(step => {
            step.skills.forEach(p_step_skill => {
                this.roleTransformation(p_step_skill);
            });
        });
        let addTime = 500;
        if (this._curRound && (this._curRound.star_add_hurts.length > 0 || this._curRound.add_buffs.length > 0)) {
            addTime = 1000;
        }
        this.setLastRunTime(Browser.now(), addTime);
        this.setRoundRoleSkillActionRes();
        this.showSceneEffect(this._fightTurn, this._curRound, false);
        //更新界面上的回合数
        DispatchManager.dispatchEvent("UPDATE_FIGHT_ROUND" /* UPDATE_FIGHT_ROUND */, [Math.max(0, this._roundIndex - 1)]);
    }
    /**小回合的数据 */
    newStep() {
        if (!this._enabled) {
            return;
        }
        if (this._curStep) {
            //移除 buff 
            this.showFightHurt(this._curStep.buff_hurts2, 0);
            this.delBuffList(this._curStep.del_buffs, 0, BuffDelType.AFTER_ROUND);
            DispatchManager.dispatchEvent("FIGHT_STEP_END" /* FIGHT_STEP_END */, [this._matchType, this._turnIndex, this._roundIndex, this._stepIndex]);
        }
        //已经是最后一个小回合已经执行
        if (this._stepIndex >= this._steps.length) {
            if (this._fightStepEnd == false && this._curRound) {
                //最后一个回合移除的 buff
                this.showFightHurt(this._curRound.end_buff_hurts, 0);
                this.delBuffList(this._curRound.del_buffs, 0, BuffDelType.AFTER_ROUND);
                if (this._curRound.dead_skills.length > 0) {
                    if (this._curRound.end_buff_hurts.length > 0) {
                        this.setLastRunTime(this._nextRunTime, 500);
                    }
                    this._skills = this._curRound.dead_skills;
                    this._skillIndex = 0;
                    this._fightStepEnd = true;
                    return;
                }
            }
            this._fightStepEnd = true;
            //恢复变身状态
            this.roleTransformationMap.forEach((vo, role_sn) => {
                vo.userRole.revert_transformation();
            });
            this.roleTransformationMap.clear();
            this.newRound();
            DispatchManager.dispatchEvent("FIGHT_ROUND_END" /* FIGHT_ROUND_END */, [this._matchType, this._turnIndex, this._roundIndex, this._stepIndex]);
            return;
        }
        this._curStep = this._steps[this._stepIndex];
        if (this.checkStop(this._curStep.src_actor_sn, this._roundIndex) == true) {
            return;
        }
        //设置当前技能释放列表
        this._skills = this._curStep.skills;
        //行动前的 buff 伤害
        if (this._curStep.buff_hurts1.length > 0) {
            this.showFightHurt(this._curStep.buff_hurts1, 0);
            this.setLastRunTime(this._nextRunTime, 500);
        }
        this.delBuffList(this._curStep.del_buffs, 0, BuffDelType.BEFORE_STEP);
        this._stepIndex = this._curStep.step;
        this._skillIndex = 0;
        this.recordFight();
    }
    /**需要释放的技能 */
    newSkill() {
        BPError.trigger("BattlePlayer", "BattlePlayer.newSkill");
        if (!this._enabled) {
            return;
        }
        //重置角色的层次
        if (this._nextRunTime + 200 > Browser.now()) {
            this.resetRoleZOrder();
            return;
        }
        if (this._isWaittingJump) {
            this.jumpProcess(this._turnIndex, this._roundIndex, this._stepIndex, this._isShowDieAction);
        }
        //当前最后一个技能已经释放完了
        if (this._curStep == null || this._skillIndex >= this._skills.length) {
            this.newStep();
            return;
        }
        //即将释放的技能
        let stepSkill = this._skills[this._skillIndex];
        this._skillIndex++;
        this._skillRunAllIndex++;
        //行动前的 触发型技能
        if (!this.trigSkillHandler(stepSkill)) {
            //需要播放技能表现
            this.setStepSkill(stepSkill);
            //提前加载下一个回合技能特效
            let _this = this;
            this.timer.once(this.delayTime(200), this, () => {
                _this.load(this._skillRunAllIndex);
            });
        }
        /**当前回合最后一步 */
        if (this._skillIndex >= this._skills.length) {
            this.combat_state = CombatState.StepLast;
        }
    }
    setStepSkill(stepSkill, buffHurtMap = null) {
        BPError.trigger("BattlePlayer", "BattlePlayer.setStepSkill");
        this.combat_state = CombatState.Execute;
        this.setAtkRoleBase(stepSkill.src_actor_sn);
        let isAddBuff = this.addBuffList(stepSkill.add_buffs, 1, true, buffHurtMap);
        if (isAddBuff == true) {
            //只是单纯的播个施法动作
            let actor_sn = stepSkill.src_actor_sn > 0 ? stepSkill.src_actor_sn : this._curStep.src_actor_sn;
            let userRole = this._elementMgr.findRole(actor_sn);
            userRole.playAni(ESkeletonAction.KILL);
            this.timer.once(this.delayTime(1000), this, () => {
                //角色释放技能
                this.roleUseSkill(stepSkill, buffHurtMap);
            });
            this.setLastRunTime(Browser.now(), 1500);
        }
        else {
            //先变身
            this.roleTransformation(stepSkill);
            //角色释放技能
            this.roleUseSkill(stepSkill, buffHurtMap);
        }
    }
    getStepSkillId(stepSkill) {
        return stepSkill ? stepSkill.skill_id || stepSkill.trig_skill_id : 0;
    }
    setAtkRoleBase(src_actor_sn) {
        let actor_sn = src_actor_sn > 0 ? src_actor_sn : this._curStep.src_actor_sn;
        this._atkRoleBase = this._elementMgr.findRole(actor_sn);
    }
    clearAtkRoleBase() {
        this._atkRoleBase = null;
    }
    roleTransformation(p_step_skill) {
        let cfgSkill = ConfigManager.get_cfg_client_w3_skill(p_step_skill.skill_id);
        if (cfgSkill && cfgSkill.transform_next_round) {
            //是否变身
            let src_actor_sn = p_step_skill.src_actor_sn;
            let skillAttackVo = this.roleTransformationMap.get(src_actor_sn);
            if (!skillAttackVo) {
                let skillAttackVo = this.parseStepSkill(p_step_skill);
                skillAttackVo.userRole.transformation(cfgSkill.transform_next_round);
                this.roleTransformationMap.set(src_actor_sn, skillAttackVo);
            }
        }
    }
    /**提前加载即将释放的技能特效资源 */
    load(index) {
        if (index >= this._allStepFightArr.length) {
            return;
        }
        let last = this._allStepFightArr[index];
        if (last) {
            let skill_id = this.getStepSkillId(last);
            let attacker = this._elementMgr.findRole(last.src_actor_sn);
            // let cfgSkill: cfg_client_w3_skill = ConfigManager.cfg_client_w3_skillCache.get(trig_skill_id);
            this.setLoadSkillRes(skill_id, attacker === null || attacker === void 0 ? void 0 : attacker.roleSkinId, last.add_buffs);
        }
        // this.setAttackerActionRes(index);
    }
    /**
     * 提前加载本回合和下回合武将用到的action资源
     */
    setRoundRoleSkillActionRes() {
        let curRound = this._curRound ? this._curRound.round : this._roundIndex;
        var startIndex = Math.max(curRound - 1, 0);
        var endIndex = startIndex + 2;
        if (endIndex <= this.roundRoleSkillActionIndex) {
            return;
        }
        else {
            this.roundRoleSkillActionIndex = endIndex;
            this.roleSkillLoadList.length = 0;
        }
        //判断角色是否可能死亡,则提前加载死亡动画
        let roleHpMap = new Map();
        let p_step_skill_list = [];
        for (let index = startIndex; index < endIndex; index++) {
            let p_fight_rnd = this._fightTurn.rounds[index];
            if (p_fight_rnd) {
                for (const steps of p_fight_rnd.steps) {
                    for (const stepSkill of steps.skills) {
                        p_step_skill_list.push(stepSkill);
                    }
                }
                for (const stepSkill of p_fight_rnd.dead_skills) {
                    p_step_skill_list.push(stepSkill);
                }
            }
        }
        for (let i = 0; i < p_step_skill_list.length; i++) {
            let last = p_step_skill_list[i];
            let skill_id = this.getStepSkillId(last);
            let attacker = this._elementMgr.findRole(last.src_actor_sn);
            let avtoar = attacker === null || attacker === void 0 ? void 0 : attacker.avtoar;
            let skill = ConfigManager.get_cfg_client_w3_skill(skill_id, attacker === null || attacker === void 0 ? void 0 : attacker.roleSkinId);
            if (skill && avtoar) {
                let actor_skillId = last.src_actor_sn + "_" + skill_id;
                if (this.roleSkillActionLoadedMap.get(actor_skillId)) {
                    //不重复加载
                    // continue;
                }
                else {
                    this.roleSkillActionLoadedMap.set(actor_skillId, true);
                    let action = skill.action_list || skill.action;
                    if (action) {
                        // let actionList = action.split("|");
                        // avtoar.addSkDelayActions(actionList);
                        let role_skill = new BaseKv();
                        role_skill.key = attacker;
                        role_skill.value = skill;
                        this.roleSkillLoadList.push(role_skill);
                    }
                    // console.log(`-----------------setAttackerActionRes index = ${index}`);
                    // console.log(`--------------------角色:${attacker._cfgHero.name}, actionList = ${actionList},
                    //                 skName = ${avtoar.skeleton?.sp3name}`);
                }
            }
            else {
                // console.log("-----------------setAttackerActionRes index = " + index + ", skill_id = " + skill_id + ", attacker = " + attacker + ", avtoar = " + avtoar + ", skill = " + skill)
            }
            let hurtList = last.skill_hurts.concat(last.other_hurts);
            hurtList.forEach(hurt => {
                let role_sn = hurt.dest_actor_sn;
                let role = this._elementMgr.findRole(role_sn);
                if (role && hurt.hurt_type == HurtType.HP) {
                    let nowHp = role.hp;
                    if (!roleHpMap.has(role_sn)) {
                        roleHpMap.set(role_sn, nowHp);
                    }
                    let nextHp = roleHpMap.get(role_sn);
                    nextHp += hurt.hurt_val;
                    roleHpMap.set(role_sn, nextHp);
                }
            });
            //变身资源
            // let addBuffList: p_skill_buff[] = last.add_buffs;
            // addBuffList.forEach(buff =>{
            //     let buffCfg: cfg_buff = ConfigManager.cfg_buffCache.get(buff.buff_type);
            //     let buffTypeCfg: cfg_buff_type = ConfigManager.cfg_buff_typeCache.get(buff.buff_type);
            //     if (buffTypeCfg.skin_id_exchange) {
            //         let role = this._elementMgr.findRole(buff.caster_sn);
            //         if(role){
            //             //目的是提前加载动作
            //             role.avtoar.addSkDelayActions([ESkeletonAction.ATTACK]);
            //             // let show3d = new W73DHeroView_Battle(1024, 1, false);
            //             // show3d.loadSkin(buffTypeCfg.skin_id_exchange + "");
            //             // ILaya.timer.once(2000, ILaya.stage, ()=>{
            //             //     show3d.destroy();
            //             // })
            //         }
            //     }
            // })
        }
        for (let act_sn of roleHpMap.keys()) {
            let nextHp = roleHpMap.get(act_sn);
            let role = this._elementMgr.findRole(act_sn);
            if (!(role === null || role === void 0 ? void 0 : role.avtoar)) {
                continue;
            }
            let avtoar = role.avtoar;
            let preloadAction = "";
            if (nextHp <= 0) {
                //准备死了!
                preloadAction = ESkeletonAction.DEATH;
            }
            else {
                preloadAction = ESkeletonAction.GET_HIT;
            }
            let actor_action = act_sn + "_" + preloadAction;
            if (this.roleSkillActionLoadedMap.get(actor_action)) {
                //不重复加载
                continue;
            }
            else {
                avtoar.addSkDelayActions([preloadAction]);
                this.roleSkillActionLoadedMap.set(actor_action, true);
            }
        }
        if (this.roleSkillLoadList.length) {
            this.timer.loop(1000, this, this.timerLoadRoleSkillAction).runImmediately();
        }
    }
    timerLoadRoleSkillAction() {
        if (this.roleSkillLoadList.length) {
            let role_skill = this.roleSkillLoadList.shift();
            let role = role_skill.key;
            let skill = role_skill.value;
            let avtoar = role.avtoar;
            if (avtoar) {
                let action = skill.action_list || skill.action;
                let actionList = action.split("|");
                avtoar.addSkDelayActions(actionList);
            }
        }
        else {
            this.timer.clear(this, this.timerLoadRoleSkillAction);
        }
    }
    /**提交加载技能相关资源 */
    setLoadSkillRes(skill_id, heroSkinId, buffs) {
        if (MemMonitor.instance.mem_very_low) {
            //    return ;
        }
        BPError.trigger("BattlePlayer", "BattlePlayer.setLoadSkillRes");
        let url = [];
        // let skill: cfg_client_w3_skill = ConfigManager.cfg_client_w3_skillCache.get(skill_id);
        let skill = ConfigManager.get_cfg_client_w3_skill(skill_id, heroSkinId);
        if (skill) {
            if (skill.full_eff_name.length > 0) {
                let type = skill.full_eff_type;
                if (type == SkillFullEffType.Spine) { //骨骼动画
                    let resName = skill.full_eff_name;
                    this.loadSkUrls(resName, ESkeletonType.EFFECT, 0, url);
                }
                else if (type == SkillFullEffType.Png) { //图片
                    url.push({ url: UrlConfig.HERO_RES_URL + `2d/${skill.full_eff_name}.png`, type: ILaya.Loader.IMAGE, priority: 0 });
                    url.push({ url: UrlConfig.BASE_RES_URL + "skill_joint/" + skill.skill_id + ".png", type: ILaya.Loader.IMAGE, priority: 0 });
                }
                else if (type == SkillFullEffType.QiMou) { //龙魂特效
                    let resName = skill.full_eff_name;
                    this.loadSkUrls(resName, ESkeletonType.EFFECT, 0, url);
                    this.loadSkUrls("qimou_dian", ESkeletonType.EFFECT, 0, url);
                    url.push({ url: UrlConfig.FIGHT_RES_URL + `${skill.skill_id}_l.png`, type: ILaya.Loader.IMAGE, priority: 0 });
                    url.push({ url: UrlConfig.FIGHT_RES_URL + `${skill.skill_id}_r.png`, type: ILaya.Loader.IMAGE, priority: 0 });
                }
            }
            if (skill.short_effect.length > 1) {
                let strs = skill.short_effect.split(",");
                let path = strs[0].replace(".sk", "");
                this.loadSkUrls(path, ESkeletonType.EFFECT, 0, url);
            }
            if (skill.short_bg.length > 0) {
                url.push({ url: UrlConfig.FIGHT_MAP_URL + skill.short_bg + ".jpg", type: ILaya.Loader.IMAGE, priority: 0 });
            }
            if (skill.dub) {
                let sound_url = GameSoundManager.instance.getSoundUrl(skill.dub);
                url.push(sound_url);
            }
            let effectIds = skill.effect_id.split("|");
            for (const id of effectIds) {
                if (id.length > 1) {
                    this.getEffectUrl(Number(id), url, null, heroSkinId);
                }
            }
            for (const skillBuff of buffs) {
                let buffCfg = ConfigManager.cfg_buffCache.get(skillBuff.uid);
                if (buffCfg) {
                    let buffTypeCfg = ConfigManager.cfg_buff_typeCache.get(buffCfg.bus_type_id);
                    if (buffTypeCfg && buffTypeCfg.effect.length > 0) {
                        this.getEffectUrl(Number(buffTypeCfg.effect), url, null, heroSkinId);
                    }
                }
            }
        }
        ResManager.loadFightEffect(url, Handler.create(this, this.onComplete));
    }
    getEffectUrl(effId, url, loadedUrl = null, heroSkinId = 0) {
        if (loadedUrl != null && loadedUrl.indexOf(effId) > -1) {
            return;
        }
        let cfgEffect = ConfigManager.get_cfg_client_w3_effect(effId, heroSkinId);
        if (cfgEffect) {
            if (cfgEffect.effect && cfgEffect.effect != "0") {
                if (cfgEffect.effect_type == 1) {
                    this.loadCCAniUrls(cfgEffect.effect);
                }
                else {
                    this.loadSkUrls(cfgEffect.effect, ESkeletonType.EFFECT, 1, url);
                }
            }
            if (!loadedUrl)
                loadedUrl = [];
            loadedUrl.push(effId);
            if (cfgEffect.hit_effect_id > 0) {
                this.getEffectUrl(cfgEffect.hit_effect_id, url, loadedUrl);
            }
            if (cfgEffect.sound) {
                let sound_path_list = cfgEffect.sound.split("|");
                if (sound_path_list && sound_path_list.length > 0) {
                    for (let i = 0; i < sound_path_list.length; ++i) {
                        let sound_param = sound_path_list[i];
                        if (sound_param.length > 0) {
                            let params = sound_param.split(",");
                            let sound_name = params[0];
                            let sound_url = GameSoundManager.instance.getSoundUrl(sound_name);
                            url.push(sound_url);
                        }
                    }
                }
            }
        }
    }
    loadSkUrls(skName, skType, priority, url) {
        if (SkeletonManager.ins.checkHasTempletCache(skName, skType)) {
        }
        else {
            //提前缓存骨骼动画
            if (skName) {
                let sk = SkeletonManager.ins.createSkeleton(skName, skType);
                sk.destroy();
            }
        }
    }
    loadCCAniUrls(aniName) {
        let url = AnimationManager.getAnimationUrl(AnimationManager.EFFECT_TYPE, aniName);
        AnimationManager.loadAnimation(null, url, AnimationManager.EFFECT_TYPE, aniName);
    }
    onComplete() {
        //console.warn("加载完成");
    }
    /**调整与当前战斗相关角色的层次 */
    fightRoleZOrderByStep(atkRole, fightStep, tarZOrder = 0) {
        let actorCache = {};
        let actorSns = [];
        fightStep.skill_hurts.forEach(hurt => {
            if (!actorCache[hurt.dest_actor_sn]) {
                actorSns.push(hurt.dest_actor_sn);
            }
        });
        fightStep.other_hurts.forEach(hurt => {
            if (!actorCache[hurt.dest_actor_sn]) {
                actorSns.push(hurt.dest_actor_sn);
            }
        });
        fightStep.add_buffs.forEach(skillBuff => {
            skillBuff.actor_list.forEach(kv => {
                if (!actorCache[kv.key]) {
                    actorSns.push(kv.key);
                }
            });
        });
        this.fightRoleZOrder(actorSns, {
            atkRole: atkRole,
            tarZOrder: tarZOrder,
            skillId: fightStep ? (fightStep.skill_id || fightStep.trig_skill_id) : 0,
        });
    }
    /**调整相关角色的层次 */
    fightRoleZOrder(actor_sn_list, { atkRole = null, tarZOrder = 0, skillId = 0, } = {}) {
        this._elementMgr.mapLayer.fightRoleZOrder(actor_sn_list, {
            atkRole: atkRole,
            tarZOrder: tarZOrder,
            skillId: skillId,
        });
    }
    setMaskAlpha(tarAlpha, time = 200) {
        this._elementMgr.mapLayer.setMaskAlpha(tarAlpha, time);
    }
    /**重置到角色出生时的层次 */
    resetRoleZOrder() {
        this._elementMgr.roleMgr.resetRoleZOrder();
        this._elementMgr.buffMgr.resetBuffZOrder();
    }
    /**角色开始释放技能 */
    roleAtk(vo) {
        BPError.trigger("BattlePlayer", "BattlePlayer.roleAtk");
        let userRole = vo.userRole;
        let cfg = ConfigManager.cfg_skillCache.get(vo.skillId);
        if (userRole && !userRole.isDead()) {
            if (cfg && cfg.type == 2) {
                userRole.showSkillName(vo.skillId);
                let _this = this;
                this.timer.once(this.delayTime(400), this, (skillAtkVo) => {
                    let action = skillAtkVo.skillInfo.action_list || skillAtkVo.skillInfo.action;
                    userRole.ChangeState(action, _this.SetAttackSkill(skillAtkVo));
                    if (vo.skillInfo.is_hide_wing == 1) {
                        userRole.hideWingEff(true);
                    }
                    _this.playDub(skillAtkVo);
                }, [vo]);
            }
            else {
                let action = vo.skillInfo.action_list || vo.skillInfo.action;
                userRole.ChangeState(action, this.SetAttackSkill(vo));
                if (vo.skillInfo.is_hide_wing == 1) {
                    userRole.hideWingEff(true);
                }
                this.playDub(vo);
            }
        }
        else {
            this._elementMgr.skillMgr.UserSkill(vo);
            this.playDub(vo);
        }
        //只有在角色可见时，才会显示飘字
        if (userRole && userRole.visible) {
            if (vo.stepSkill.trig_buff_id > 0 || vo.stepSkill.big_type == GuaJiDataCenter.TRIG_SKILL_TYPE51) {
                let buffCfg = ConfigManager.cfg_buffCache.get(vo.stepSkill.trig_buff_id);
                if (buffCfg) {
                    let buffName = buffCfg.name_in_battle || buffCfg.name;
                    this._elementMgr.hurtMgr.showSkillStatus(buffName, this._elementMgr.mapLayer.roleTopLayer, userRole.actor_sn, { posx: userRole.x, posy: userRole.y - 60 });
                }
            }
            else if (vo.stepSkill.big_type == GuaJiDataCenter.TRIG_SKILL_TYPE10) {
                let trigSkillCfg = ConfigManager.getTrigSkillCfg(vo.skillId);
                if (trigSkillCfg) {
                    this._elementMgr.hurtMgr.showSkillStatus(trigSkillCfg.skill_name, this._elementMgr.mapLayer.roleTopLayer, userRole.actor_sn, { posx: userRole.x, posy: userRole.y - 60 });
                    // console.log("--")
                }
            }
            else if (vo.stepSkill.big_type == GuaJiDataCenter.TRIG_SKILL_TYPE2 && cfg && cfg.type != 2) {
                this._elementMgr.hurtMgr.showSkillStatus(cfg.name, this._elementMgr.mapLayer.roleTopLayer, userRole.actor_sn, { posx: userRole.x, posy: userRole.y - 60 });
            }
        }
        let short_icon = vo.skillInfo.short_icon;
        let short_bg = vo.skillInfo.short_bg;
        if (short_icon.length > 0 || short_bg.length > 0) {
            this._elementMgr.mapLayer.whiteScreen.show(short_icon, short_bg, this._playSpeed);
            this.setMaskAlpha(0);
        }
        else {
            if (vo.skillInfo.black_bg) {
                let blackBgParams = vo.skillInfo.black_bg.split("|");
                let fadeIn = Number(blackBgParams[0]);
                this.setMaskAlpha(0.6, fadeIn);
            }
        }
        //这里是一个最大的超时（避免时间计算出错等其它不确定因素）;  
        if (vo.skillInfo.action == ESkeletonAction.CONJURE) {
            this.setLastRunTime(Browser.now(), 1000);
        }
        else {
            let maxtime = Math.max(vo.skillInfo.maxtime, ConfigManager.cfg_client_w3_skill_maxtime);
            this.setLastRunTime(Browser.now(), vo.skillInfo.maxtime);
        }
    }
    SetAttackSkill(vo) {
        vo.skillGroupList = this._getSkillHurtsGroup(vo.stepSkill.skill_hurts, vo.skillId);
        // vo.otherGroupList = this._getSkillHurtsGroup(vo.stepSkill.other_hurts);
        return [vo];
    }
    /**策划的一个需求，如果有对一个目标造成多条伤害，且前端表现配置了伤害多段显示，则将多条伤害分别对应多段显示，而不是一条伤害分为多段 */
    _getSkillHurtsGroup(skill_hurts, skill_id = 0) {
        BPError.trigger("BattlePlayer", "BattlePlayer._getSkillHurtsGroup");
        let tempBuffHurt = [];
        //对技能效果进行分组
        let hurt_map = new Map(); //效果分组
        let last_group_id = -1;
        let other_group_id = 100;
        for (let i = 0; i < skill_hurts.length; ++i) {
            const hurt = skill_hurts[i];
            let group_id = hurt.group_id; //技能效果分组ID
            let skillEffectCfg = ConfigManager.cfg_skill_effectCache.get(hurt.effect_id);
            //是否为对自己的伤害
            let isSelfHurt = hurt.src_actor_sn == hurt.dest_actor_sn;
            //是否为BUFF相关
            let isBuffType = hurt.hurt_type == HurtType.ADD_BUFF
                || hurt.hurt_type == HurtType.DEL_BUFF_LAYER
                || hurt.hurt_type == HurtType.DEL_BUFF
                || hurt.hurt_type == HurtType.BUFF_MISS;
            //是否为对其他目标的额外伤害
            //如果在之前的伤害列表中，没有找到相同的攻击者和受击者的数据，则说明这条数据是对其他目标的额外伤害，合并到当前同一组效果中
            let find_hurt = this.findFightHurt(hurt_map, { src_actor_sn: hurt.src_actor_sn, dest_actor_sn: hurt.dest_actor_sn });
            let isExtraHurt = find_hurt.length <= 0;
            let tar_group_id = last_group_id;
            if (last_group_id == -1) { //第一条数据
                tar_group_id = group_id;
                if (isBuffType) {
                    //如果首条数据是BUFF相关的数据，则先保存起来，直到首条伤害数据分组时，再合并到一起
                    tempBuffHurt.push(hurt);
                    continue;
                }
                else {
                    last_group_id = group_id;
                }
            }
            else if ((skill_id == 31320176 || skill_id == 31320177) && skillEffectCfg && skillEffectCfg.target_id == 71002) { //张飞技能效果特殊处理
                tar_group_id = other_group_id++;
            }
            else if (!isSelfHurt && !isExtraHurt && !isBuffType) { //非对自己的伤害  非额外伤害  非buff相关
                tar_group_id = group_id;
                last_group_id = group_id;
            }
            let hurtList = hurt_map.get(tar_group_id) || [];
            if (tempBuffHurt.length > 0) {
                hurtList.push(...tempBuffHurt);
                tempBuffHurt.length = 0;
            }
            hurtList.push(hurt);
            hurt_map.set(tar_group_id, hurtList);
        }
        //技能效果分组列表
        let groupList = [];
        for (let hurt_list of hurt_map.values()) {
            if (hurt_list && hurt_list.length > 0) {
                groupList.push(hurt_list);
            }
        }
        return groupList;
    }
    findFightHurt(hurt_map, { group_id = null, src_actor_sn = null, dest_actor_sn = null, } = {}) {
        let result_list = [];
        for (let hurt_list of hurt_map.values()) {
            for (let i = 0; i < hurt_list.length; ++i) {
                let hurt = hurt_list[i];
                if ((group_id === null || group_id == hurt.group_id)
                    && (src_actor_sn === null || src_actor_sn == hurt.src_actor_sn)
                    && (dest_actor_sn === null || dest_actor_sn == hurt.dest_actor_sn)) {
                    result_list.push(hurt);
                }
            }
        }
        return result_list;
    }
    /**播放技能配音 */
    playDub(vo) {
        if (vo && vo.skillInfo && vo.skillInfo.dub && !this._elementMgr.isMute) {
            GameSoundManager.instance.playEffect(vo.skillInfo.dub);
        }
    }
    showEffectsFloatName(hurtList) {
        for (let i = 0; i < hurtList.length; ++i) {
            let hurtInfo = hurtList[i];
            this._showEffectFloatName(hurtInfo);
        }
    }
    _showEffectFloatName(hurtInfo, delay = 0) {
        if (!hurtInfo || hurtInfo.hurt_type == 2) {
            return;
        }
        let effectId = hurtInfo.effect_id > 0 ? hurtInfo.effect_id : 0;
        let destActorSn = hurtInfo.dest_actor_sn > 0 ? hurtInfo.dest_actor_sn : 0;
        let targetRole = this._elementMgr.findRole(destActorSn);
        //只有在角色可见时，才会显示飘字
        if (targetRole && targetRole.visible && ConfigManager.cfg_skill_effectCache.has(effectId)) {
            let cfg = ConfigManager.cfg_skill_effectCache.get(hurtInfo.effect_id);
            if (cfg.name_in_battle) {
                this._elementMgr.hurtMgr.showSkillStatus(cfg.name_in_battle, this._elementMgr.mapLayer.roleTopLayer, targetRole.actor_sn, {
                    posx: targetRole.x,
                    posy: targetRole.y - 60,
                    delay: delay,
                });
            }
        }
    }
    /**设置下次的战斗时间 */
    setLastRunTime(time, addTime = 1) {
        this._nextRunTime = time + addTime / this._playSpeed;
        if (GlobalConfig.IsDebug) {
            if (this._nextRunTime - Browser.now() > 10000) {
                console.error("-------setLastRunTime 时间过长,确定配置没问题吗?");
                console.error(StringUtil.Format("当前时间：{0}，增加时间：{1}，最终时间：", [Math.floor(time), addTime]) + this._nextRunTime);
            }
        }
    }
    createSkillAttackVo() {
        return SkillAttackVo.create();
    }
    /**解析p_step_skill，生成SkillAttackVo */
    parseStepSkill(stepSkill) {
        var _a, _b;
        BPError.trigger("BattlePlayer", "BattlePlayer.parseStepSkill");
        let src_actor_sn = stepSkill.src_actor_sn > 0 ? stepSkill.src_actor_sn : this._curStep.src_actor_sn;
        let dest_actor_sn = stepSkill.jump_actor_sn;
        let isLeft = src_actor_sn < 100 || 1000 < src_actor_sn && src_actor_sn < 2000;
        //角色数据进行过交换 交换接口：FightDataCenter.revertFightStart
        if (this._fight_data && this._fight_data["_isExchange_"]) {
            isLeft = !isLeft;
        }
        let skillId = this.getStepSkillId(stepSkill);
        let userRole = this._elementMgr.findRole(src_actor_sn);
        let tarRole = this._elementMgr.findRole(dest_actor_sn);
        //无效行动步骤 start
        //比如：神周瑜的天火降临，在战斗结束后没有敌人的情况下，可能还会触发，此步骤被视为无效步骤
        //author: liyongjie
        //date: 2023.02.13 
        let hasDestActor = !!dest_actor_sn;
        let hasSkillHurt = stepSkill.skill_hurts.length > 0;
        let hasOtherHurt = stepSkill.other_hurts.length > 0;
        let hasBuff = stepSkill.add_buffs.length > 0;
        if (!hasDestActor && !hasSkillHurt && !hasOtherHurt && !hasBuff) { //无效步骤不执行
            return null;
        }
        //无效行动步骤 end
        //完整的攻击技能封装
        let vo = this.createSkillAttackVo();
        vo.skillId = skillId;
        // vo.skillInfo = ConfigManager.cfg_client_w3_skillCache.get(skillId);
        vo.skillInfo = ConfigManager.get_cfg_client_w3_skill(skillId, userRole === null || userRole === void 0 ? void 0 : userRole.roleSkinId);
        vo.stepSkill = stepSkill;
        //技能使用者
        vo.userRole = userRole;
        //技能目标
        vo.targetInfo = tarRole;
        vo.useCamp = userRole ? userRole.camp : isLeft ? 1 : 2;
        if (vo.skillInfo == null) {
            // TipsUtil.showDebugTips(window.i18n("技能ID不存在：{0}, 请检查 cfg_client_w3_skill", [skillId]), true, "Errors");
            if (userRole) {
                if (userRole.data.actor_type == ActorType.Hero) {
                    let normalSkill = HeroUtil.getHeroNormalSkillId(userRole._cfgHero.type_id);
                    let cfgNormalSkill = ConfigManager.get_cfg_client_w3_skill(normalSkill, userRole === null || userRole === void 0 ? void 0 : userRole.roleSkinId);
                    vo.skillInfo = cfgNormalSkill;
                }
                // let skillIds = [];
                // let actorData = userRole.data;
                // if (actorData.actor_type == ActorType.Hero) {
                //     skillIds = HeroUtil.getHeroAllSkillIds(actorData.type_id);
                // }
                // if (skillIds.length > 0) {
                //     // vo.skillInfo = ConfigManager.cfg_client_w3_skillCache.get(skillIds[0]);
                //     vo.skillInfo = ConfigManager.get_cfg_client_w3_skill(skillIds[0], userRole.roleSkinId) || cfgNormalSkill;
                // }
            }
        }
        let offsetX = 0;
        let offsetY = 0;
        if ((_a = vo.skillInfo) === null || _a === void 0 ? void 0 : _a.pos_offset_x) {
            let leftSymbol = isLeft ? 1 : -1;
            offsetX = leftSymbol * vo.skillInfo.pos_offset_x;
        }
        if ((_b = vo.skillInfo) === null || _b === void 0 ? void 0 : _b.pos_offset_y) {
            offsetY = vo.skillInfo.pos_offset_y;
        }
        if (vo.skillInfo
            && (vo.skillInfo.combat_type == CombatType.MoveToTar
                || vo.skillInfo.combat_type == CombatType.AtkAndMove
                || vo.skillInfo.combat_type == CombatType.JumpAndAtk
                || vo.skillInfo.combat_type == CombatType.JumpToAtk)
            && vo.skillInfo.atk_pos != AtkPosType.TargetPos) {
            if (vo.skillInfo.atk_pos == AtkPosType.MapCenter) {
                let pos = this._elementMgr.getMapCenterPos();
                vo.tarX = pos[0];
                vo.tarY = pos[1];
            }
            else if (vo.skillInfo.atk_pos == AtkPosType.CampCenter && tarRole) {
                let pos = this._elementMgr.getCampCenterPos(tarRole.camp);
                vo.tarX = pos[0];
                vo.tarY = pos[1];
            }
            else if (vo.skillInfo.atk_pos == AtkPosType.MyCampCenter && userRole) {
                let pos = this._elementMgr.getCampCenterPos(userRole.camp);
                vo.tarX = pos[0];
                vo.tarY = pos[1];
            }
        }
        else if (tarRole) {
            if (offsetX) {
                vo.tarX = vo.targetInfo.x;
            }
            else {
                vo.tarX = vo.targetInfo.x - vo.targetInfo.hurtX;
            }
            vo.tarY = vo.targetInfo.y;
        }
        else {
            let pos = this._elementMgr.getRolePos(src_actor_sn);
            if (pos) {
                vo.tarX = pos[0];
                vo.tarY = pos[1];
            }
        }
        vo.tarX += offsetX;
        vo.tarY += offsetY;
        /**缺少目标从 伤害害列表里选择一个目标 */
        if (vo.targetInfo == null && stepSkill.skill_hurts.length > 0) {
            let fightHurt = stepSkill.skill_hurts[0];
            vo.targetInfo = this._elementMgr.findRole(fightHurt.dest_actor_sn);
        }
        if (vo.targetInfo == null) {
            dest_actor_sn = stepSkill.jump_actor_sn == 0 ? src_actor_sn : stepSkill.jump_actor_sn;
            vo.targetInfo = this._elementMgr.findRole(dest_actor_sn);
        }
        /**是否有多个不同目标 */
        let actorSns = [];
        if (vo.targetInfo) {
            if (stepSkill.skill_hurts.length > 1) {
                for (const fightHurt of stepSkill.skill_hurts) {
                    if (actorSns.indexOf(fightHurt.dest_actor_sn) == -1) {
                        actorSns.push(fightHurt.dest_actor_sn);
                    }
                }
            }
        }
        /*是否是飞向多目标 */
        let isFlyMultiple = vo.isFlyMultiple;
        if (!isFlyMultiple) {
            if (actorSns.length > 0) {
                let effectIds = vo.skillInfo.effect_id.split("|");
                for (const id of effectIds) {
                    let cfgEffect = ConfigManager.get_cfg_client_w3_effect(Number(id), userRole === null || userRole === void 0 ? void 0 : userRole.roleSkinId);
                    if (cfgEffect && cfgEffect.fly == FlyStatus.FLY_MULTIPLE) {
                        isFlyMultiple = true;
                        break;
                    }
                }
            }
        }
        /*是否是飞向多目标 */
        if (isFlyMultiple == true && actorSns.length > 0) {
            //把额外伤害里的目标也算上
            for (const fightHurt of stepSkill.other_hurts) {
                if (actorSns.indexOf(fightHurt.dest_actor_sn) == -1) {
                    actorSns.push(fightHurt.dest_actor_sn);
                }
            }
            let otherTargetInfo = [];
            for (const actor_sn of actorSns) {
                let role = this._elementMgr.findRole(actor_sn);
                if (role) {
                    otherTargetInfo.push(role);
                }
            }
            vo.otherTargetInfo = otherTargetInfo;
        }
        vo.fightStep = this._curStep;
        return vo;
    }
    /**英雄释放技能 */
    roleUseSkill(stepSkill, buffHurtMap = null) {
        BPError.trigger("BattlePlayer", "BattlePlayer.roleUseSkill");
        let vo = this.parseStepSkill(stepSkill);
        if (vo == null) {
            return;
        }
        vo.buffHurtMap = buffHurtMap;
        let tarZOrder = vo.targetInfo != null ? vo.targetInfo.birthZOrder : 0;
        DispatchManager.dispatchEvent("FIGHT_USE_SKILL" /* FIGHT_USE_SKILL */, stepSkill);
        //提高与本次战斗相关的角色的层次
        this.fightRoleZOrderByStep(vo.userRole, stepSkill, tarZOrder);
        //该技能有夺屏效果
        let type = vo.skillInfo.full_eff_type;
        if (vo.skillInfo.full_eff_name.length > 1) {
            if (type == SkillFullEffType.QiMou) {
                this.setQiMouDuoPingEff(vo);
            }
            else if (vo.userRole) {
                this.setDuoPing(vo, SkillFullType.Hero, vo.useCamp == 1);
            }
            else {
                this.setDuoPing(vo, SkillFullType.Master, vo.useCamp == 1);
            }
            return;
        }
        let shock = vo.skillInfo.shock;
        if (shock) { //抖屏 震屏
            let shockList = shock.split(",");
            let delay = shockList[0];
            //删除delay
            shockList.shift();
            let shock2 = shockList.join(",");
            this.timer.once(+delay, this, () => {
                this.setCameraShock(shock2);
            });
        }
        //角色播放攻击动作 并 释放技能
        this.roleAtk(vo);
    }
    /**夺屏界面 */
    setDuoPing(vo, fullType, isLeft = true) {
        this.onEnabled(false);
        let duopingVo = new FightDuoPingVo();
        duopingVo.fullType = fullType;
        duopingVo.skill = vo.skillInfo;
        duopingVo.actor = vo.userRole.data;
        duopingVo.isLeft = isLeft;
        duopingVo.playSpeed = this._playSpeed;
        this._elementMgr.mapLayer.duoPingView.setData(duopingVo);
        this._cacheAttackVo = vo;
    }
    /**龙魂夺屏特效 */
    setQiMouDuoPingEff(vo) {
        this.onEnabled(false);
        let actorSn = vo.stepSkill.src_actor_sn;
        let objData = this._elementMgr.roleMgr.findObjData(actorSn);
        let typeId = objData ? objData.type_id : 0;
        let skillId = vo.stepSkill.skill_id;
        let effectName = vo.skillInfo.full_eff_name;
        let isLeft = vo.useCamp == 1;
        DispatchManager.dispatchEvent("SHOW_QIMOU_EFF" /* SHOW_QIMOU_EFF */, [effectName, typeId, skillId, isLeft]);
        // let qimouEff = this._elementMgr.mapLayer.qiMouEffView;
        // qimouEff.setData(effectName, vo.stepSkill.skill_id.toString(), level, isLeft);
        // qimouEff.play();
        if (vo.skillInfo.black_bg) {
            let blackBgParams = vo.skillInfo.black_bg.split("|");
            let fadeIn = Number(blackBgParams[0]);
            this.setMaskAlpha(0.6, fadeIn);
        }
        this._cacheAttackVo = vo;
    }
    /**战斗前场景buff特效 */
    showSceneEffect(fightTurn, curRound, isRestore) {
        let duration = 0;
        if (!curRound) {
            return duration;
        }
        if (!fightTurn || !fightTurn.round_scenes.length) {
            return duration;
        }
        let roundIndex = curRound.round;
        let p_fight_scene_list = fightTurn.round_scenes.filter(data => {
            if (isRestore) {
                return data.round <= roundIndex;
            }
            else {
                return data.round == roundIndex;
            }
        });
        if (!p_fight_scene_list.length) {
            return duration;
        }
        if (isRestore == false) {
            this.onEnabled(false);
        }
        for (let i = 0; i < p_fight_scene_list.length; i++) {
            let p_fight_scene = p_fight_scene_list[i];
            let round = p_fight_scene.round;
            let sceneId = p_fight_scene.scene_id;
            let isLeft = p_fight_scene.side == 1;
            let cfgRoleProfile = ConfigManager.cfg_profileCache.get(sceneId);
            if (!cfgRoleProfile || !cfgRoleProfile.ext_1) {
                continue;
            }
            let extList = cfgRoleProfile.ext_1.split("|");
            let sceneEffectName = extList[0];
            let duopingEffectName = extList[1] || "changjing_gold";
            if (isLeft) {
                var skEff = this.sceneEffLeft;
            }
            else {
                var skEff = this.sceneEffRight;
            }
            //若同一场战斗，有两个不同的场景装扮，则场景叠加显示场景特效，并且双方都可以看到叠加的场景特效；
            //若双方用的是同一场景装扮，则只使用1个场景，不叠加。
            if (!skEff || skEff.skName != sceneEffectName) {
                skEff && skEff.destroy();
                skEff = SkeletonManager.ins.createSkeleton(sceneEffectName, ESkeletonType.EFFECT);
                skEff.zOrder = 0;
                skEff.alpha = 1;
                this._elementMgr.addChildToLayer(skEff);
                let mapCenterPos = this._elementMgr.getMapCenterPos();
                skEff.pos(mapCenterPos[0], mapCenterPos[1] - 50);
            }
            if (isLeft) {
                this.sceneEffLeft = skEff;
            }
            else {
                this.sceneEffRight = skEff;
            }
            if (isRestore == false) {
                skEff._tweenFrom({ alpha: 0.1 }, 1000);
                let vo = new FightDuoPingVo();
                vo.matchType = this.matchType;
                vo.effName = duopingEffectName;
                vo.qiMouName = "scene_effect_" + sceneId;
                vo.star = 5;
                vo.isLeft = isLeft;
                DispatchManager.dispatchEvent("SHOW_SCENE_EFF" /* SHOW_SCENE_EFF */, vo);
                duration += 500;
                //以防意外
                // this.timer.once(3000, this, this.onfightContinue, [vo]);
            }
        }
        return duration;
    }
    finishFight(finish_type = 1) {
        if (LayerManager.currentMap) {
            //LayerManager.currentMap.match_type == this._fight_data.match_type
            //this._finishFight = true;
            if (this._fight_data.match_type == MatchConst.MATCH_TYPE_FIGHT_PLOT) {
                this.combat_state = CombatState.End;
                this._finishFight = true;
            }
            LayerManager.currentMap.finishFight({ finish_type: finish_type, match_type: this._fight_data.match_type, data: this._fight_data });
        }
    }
    /**记录战斗数据 */
    recordFight() {
        let currentMap = LayerManager.currentMap;
        if (currentMap && currentMap.match_type == this._fight_data.match_type) {
            //第几场
            currentMap.turnIndex = this._turnIndex - 1;
            //大回合
            currentMap.roundIndex = this.combat_state == CombatState.None ? this._roundIndex : Math.max(0, this._roundIndex - 1);
            //小回合
            currentMap.stepIndex = this.combat_state == CombatState.None ? this._stepIndex : Math.max(0, this._stepIndex - 1);
        }
        return true;
    }
    clearBg() {
    }
    clear() {
        this.reset();
        //临时处理：在战斗中打开英雄信息Tip界面，在战斗结束后不会被关闭，再次手动关闭
        //但这样处理的话，会在切换到下一波战斗时，关闭英雄信息Tip界面
        DispatchManager.dispatchEvent("CLOSE_ROLE_OTHER_INFO_DIALOG" /* CLOSE_ROLE_OTHER_INFO_DIALOG */, true);
    }
    onDestroyMap(matchType, destroyType = 0) {
        if (!matchType || matchType == this.matchType) {
            this.destroySceneEff();
        }
    }
    destroySceneEff() {
        if (this.sceneEffLeft) {
            this.sceneEffLeft.destroy();
            this.sceneEffLeft = null;
        }
        if (this.sceneEffRight) {
            this.sceneEffRight.destroy();
            this.sceneEffRight = null;
        }
    }
    reset() {
        this.combat_state = CombatState.None;
        this._roundIndex = 0;
        this._stepIndex = 0;
        this._skillIndex = 0;
        this._skillRunAllIndex = 0;
        this.roundRoleSkillActionIndex = -1;
        this.roleSkillActionLoadedMap.clear();
        this._isRunStop = false;
        this._fightStepEnd = false;
        this._finishFight = false;
        this._enabled = true;
        this._review = false;
        this._isWaittingJump = false;
        this._isShowDieAction = false;
        this._atkRoleBase = null;
        this._cacheAttackVo = null;
        this._curStep = null;
        this._curRound = null;
        this._fightTurn = null;
        this._steps = [];
        this._skills = [];
        this._trigStepSkill = [];
        this.status_name_map.clear();
        if (this._leftShenBing) {
            this._leftShenBing.destroy();
            this._leftShenBing = null;
        }
        if (this._rightShenBing) {
            this._rightShenBing.destroy();
            this._rightShenBing = null;
        }
        if (this._leftPfm) {
            this._leftPfm.visible = false;
        }
        if (this._rightPfm) {
            this._rightPfm.visible = false;
        }
        this._allStepFightArr.length = 0;
        this.timer.clearAll(this);
    }
    destroy(destroyChild = true) {
        this.removeEvent();
        super.destroy(destroyChild);
    }
}
