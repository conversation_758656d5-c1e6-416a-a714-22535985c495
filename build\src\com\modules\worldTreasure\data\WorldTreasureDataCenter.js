import { Point } from "laya/maths/Point";
import { PanelIconMacro } from "../../../auto/PanelConstAuto";
import { Connection } from "../../../net/Connection";
import { m_world_secret_treasure_op_tos } from "../../../proto/line/m_world_secret_treasure_op_tos";
import { ArrayUtil } from "../../../util/ArrayUtil";
import { XmlFormatVo } from "../../../util/XmlFormatVo";
import MissionConst, { MissionStatus } from "../../mission/MissionConst";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { m_simp_mission_accept_tos } from "../../../proto/line/m_simp_mission_accept_tos";
import { m_simp_mission_finish_tos } from "../../../proto/line/m_simp_mission_finish_tos";
import { m_simp_mission_collect_tos } from "../../../proto/line/m_simp_mission_collect_tos";
import { m_simp_mission_fetch_tos } from "../../../proto/line/m_simp_mission_fetch_tos";
import { LineUpUtil } from "../../lineUp/util/LineUpUtil";
import { LineUpCheckUtil } from "../../lineUp/util/LineUpCheckUtil";
import { MatchConst } from "../../../auto/ConstAuto";
import { StringUtil } from "../../../util/StringUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { Handler } from "laya/utils/Handler";
import { WorldTreasureLineUpLimitHelper } from "../helper/WorldTreasureLineUpLimitHelper";
export class WorldTreasureDataCenter {
    constructor() {
        this.dataList = [];
        this._remainTimes = 0; //剩余任务次数
        this._remain_add_reward_times = 0; //守护者奖励额外产出次数
        this._remain_chal_gdn_times = 0; //今日剩余挑战成功次数
        this._treasure_boosMap = new Map();
    }
    static get instance() {
        if (WorldTreasureDataCenter._instance == null) {
            WorldTreasureDataCenter._instance = new WorldTreasureDataCenter();
        }
        return WorldTreasureDataCenter._instance;
    }
    /**设置藏宝图守护者信息 */
    setTreasureBoosMap(value) {
        this._treasure_boosMap = ArrayUtil.getMapByArray(value, "id");
    }
    get treasureBossMap() {
        return this._treasure_boosMap;
    }
    setTreasurePos(pos) {
        this._treasurePos = pos;
    }
    get treasurePos() {
        return this._treasurePos;
    }
    get playerFightPos() {
        return this._playerFightPos;
    }
    get remainAddRewardTimes() {
        return this._remain_add_reward_times;
    }
    set remainAddRewardTimes(value) {
        this._remain_add_reward_times = value;
    }
    get remainChallengeTimes() {
        return this._remain_chal_gdn_times;
    }
    set remainChallengeTimes(value) {
        this._remain_chal_gdn_times = value;
    }
    setplayerFightPos(value) {
        this._playerFightPos = null;
        if (value && value.length >= 2) {
            this._playerFightPos = new Point().setTo(value[0], value[1]);
        }
    }
    /**任务相关 */
    /** 1 targe 2 complete */
    getNowMissionTalkList(talkText) {
        return XmlFormatVo.GetVoArr(talkText);
    }
    get remainTimes() {
        return this._remainTimes;
    }
    set remainTimes(value) {
        this._remainTimes = value;
    }
    isTaskAllCompleted() {
        return this._remainTimes === 0;
    }
    setTaskMission(dataList) {
        if (dataList.length > 0) {
            this._taskMissionInfo = dataList[0];
            this._taskMissionCfg = CfgCacheMapMgr.cfg_world_secret_treasure_missionCache.get(this._taskMissionInfo.id);
        }
    }
    get taskMissionInfo() {
        return this._taskMissionInfo;
    }
    updateTaskMission(data) {
        this._taskMissionInfo = data;
        this._taskMissionCfg = CfgCacheMapMgr.cfg_world_secret_treasure_missionCache.get(this._taskMissionInfo.id);
    }
    checkMissionComplete() {
        if (this._taskMissionInfo.status == MissionStatus.ACC_REWARD) {
            return true;
        }
        if (this._taskMissionCfg && this._taskMissionInfo.status == MissionStatus.ACCEPT) {
            if (this._taskMissionCfg.event_type == MissionConst.MISSION_TYPE_STAGE_COPY_TALK) {
                return true;
            }
            else if (this._taskMissionCfg.event_type == MissionConst.MISSION_TYPE_COLLECT_GOOD) {
                if (this.taskMissionInfo.cur_progress >= this._taskMissionCfg.max_progress) {
                    return true;
                }
            }
        }
        return false;
    }
    createLineUpVo() {
        let lineUpVo = LineUpUtil.createVO(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE);
        lineUpVo.limitHelper = new WorldTreasureLineUpLimitHelper();
        lineUpVo.confirmHandler = Handler.create(this, (param) => {
            let emptyId = LineUpCheckUtil.getLineUpEmptyTeamId(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE);
            if (emptyId > 0) {
                let teamName = LineUpUtil.getLineUpIndexName(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE, emptyId);
                const str = StringUtil.FormatArr("{0}为空，需要至少上阵一个英雄", [teamName]);
                TipsUtil.showTips(str);
            }
            else {
                param && param.callBack.run();
            }
        }, [], false);
        return lineUpVo;
    }
    /**------------------------------------协议相关--------------------------- */
    /**
     * @param op_type 1:一键完成任务 2:刷新任务 3:开宝藏图 4:挖宝 5:进入地图 6:退出地图
     * @param param_list
     */
    m_world_secret_treasure_op_tos(op_type, param_list = []) {
        let tos = new m_world_secret_treasure_op_tos();
        tos.op_type = op_type;
        tos.param_list = param_list;
        Connection.instance.sendMessage(tos);
    }
    m_simp_mission_accept_tos(id) {
        var tos = new m_simp_mission_accept_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_WORLD_TREASURE;
        tos.id = id;
        tos.big_type = PanelIconMacro.WORLD_SECRET_TREASURE;
        Connection.instance.sendMessage(tos);
    }
    m_simp_mission_finish_tos(id) {
        var tos = new m_simp_mission_finish_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_WORLD_TREASURE;
        tos.id = id;
        tos.big_type = PanelIconMacro.WORLD_SECRET_TREASURE;
        Connection.instance.sendMessage(tos);
    }
    m_simp_mission_collect_tos(id) {
        var tos = new m_simp_mission_collect_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_WORLD_TREASURE;
        tos.id = id;
        Connection.instance.sendMessage(tos);
    }
    m_simp_mission_fetch_tos(id) {
        var tos = new m_simp_mission_fetch_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_WORLD_TREASURE;
        tos.id = id;
        Connection.instance.sendMessage(tos);
    }
}
WorldTreasureDataCenter._instance = null;
WorldTreasureDataCenter.TREASURE_MAP_MISSION_RED_POINT = 1;
