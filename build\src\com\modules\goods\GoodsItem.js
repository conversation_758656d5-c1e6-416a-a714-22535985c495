import { Sprite } from "laya/display/Sprite";
import { Text } from "laya/display/Text";
import { Render } from "laya/renders/Render";
import { FontClip } from "laya/ui/FontClip";
import { Image } from "laya/ui/Image";
import { Label } from "laya/ui/Label";
import { GlobalConfig } from "../../../game/GlobalConfig";
import { UrlConfig } from "../../../game/UrlConfig";
import { ItemMacro } from "../../auto/ItemMacro";
import { CfgCacheMapMgr } from "../../cfg/CfgCacheMapMgr";
import { AnimationManager } from "../../managers/AnimationManager";
import { ConfigManager } from "../../managers/ConfigManager";
import { DispatchManager } from "../../managers/DispatchManager";
import { CCAnimation } from "../../scene2d/d2/CCAnimation";
import { com } from "../../ui/layaMaxUI";
import { ColorUtil } from "../../util/ColorUtil";
import { GameUtil } from "../../util/GameUtil";
import { MoneyTransformUtil } from "../../util/MoneyTransformUtil";
import { PoolUtil } from "../../util/PoolUtil";
import { StringUtil } from "../../util/StringUtil";
import { TimerUtils } from "../../util/TimerUtils";
import { TipsUtil } from "../../util/TipsUtil";
import { GSkeleton } from "../baseModules/skeleton/GSkeleton";
import { ESkeletonType } from "../baseModules/skeleton/SkeletonData";
import { SkeletonManager } from "../baseModules/skeleton/SkeletonManager";
import { UIList } from "../baseModules/UIList";
import { UIProgressBar } from "../baseModules/UIProgressBar";
import { DataCenter } from "../DataCenter";
import { FishDataCenter } from "../fish/data/FishDataCenter";
import { HeroConsts } from "../hero/data/HeroConsts";
import { HeroDataCenter } from "../hero/data/HeroDataCenter";
import { HeroStarLevelItem } from "../hero/HeroStarLevelItem";
import CastSoulVo from "../hero/vo/CastSoulVo";
import { MasterCardDataCenter } from "../mastercard/data/MasterCardDataCenter";
import { GoodsVO } from "./GoodsVO";
import { ItemConst } from "./ItemConst";
import { GoodsEquipInfo } from "./view/GoodsEquipInfo";
export default class GoodsItem extends com.ui.res.base.GoodsSmallItemUI {
    /**
     * 尽量用对象池的,不要new
     * 因为这个类内容比较多,创建比较慢
     * 也尽量少继承GoodsItem
     *  */
    constructor() {
        super();
        this.index = -1;
        this.isShowEquipInfo = false;
        /**
         * 是否启用点击查看物品信息提示
         * **/
        this._clickEnable = true;
        this._isShowForgeBtn = false;
        this.isShowTips = true;
        this.isShowBtn = false;
        this.isShowNum = true;
        this.isBag = false;
        this.isShowNameBg = false;
        this.isShowProgress = false;
        this.isShowName = false;
        this.isPathBtn = true;
        this.isClick = true;
        this.isStopPropagation = true;
        this.equip_compose_red_point = false;
        this.hide_debris = false;
        this.titleUrl = "";
        this.titleShowId = 0;
        this.isGoldNotCrit = false; //钻石数量不翻倍
        this.isShowSelectIcon = true;
        this._wordWrap = false;
        /**显示玩家拥有的以及剩余的数量 */
        this._isShowCostNum = false;
        this.isAdd = false;
        this._isSelect = false;
        this._isHideHeroSoulBg = false;
    }
    getClassName() {
        return "GoodsItem";
    }
    static create() {
        return PoolUtil.getItemByClass(GoodsItem);
    }
    initUI() {
        this.Clean();
        this.starList.visible = false;
        this.boxDivine.visible = false;
        this.progress = UIProgressBar.SetUIProgressBar(this, this.progressBar);
        this.progress.SetValue(50, 100);
    }
    addClick() {
        this.addOnClick(this, this, this.OnThisClick);
    }
    OnThisClick(e) {
        if (this._clickEnable == false) {
            return;
        }
        if (e && this.isStopPropagation) {
            e.stopPropagation();
        }
        if (this._goodsVo == null) {
            return;
        }
        if (this.OnClickCallback != null) {
            this.OnClickCallback.run();
            return;
        }
        else if (this._goodsVo.ischeckItem) {
            this.onClickGoodsItem();
            return;
        }
        if (this._goodsVo.kind == ItemMacro.ITEM_KIND_GENERATE_MASTER_CARD) {
            MasterCardDataCenter.instance.m_master_card_op_tos(6 /* TYPE_PRELOOK */, [this._goodsVo.typeId]);
            return;
        }
        //这里的itemKindId可能是钓鱼卡道具的id
        if (this._goodsVo.kind == ItemMacro.ITEM_KIND_GENERATE_MASTER_CARD) {
            FishDataCenter.instance.m_fish_op_tos(FishDataCenter.OP_PREVIEW, [this._goodsVo.typeId]);
            return;
        }
        if (this._clickEnable == true && this._goodsVo.typeId > 0 && this.isShowTips) {
            // if (this._goodsVo.IsRide) {
            //     TipsUtil.openRideTips(this._goodsVo, this.isShowBtn);
            // }
            if (this._goodsVo.IsHero) {
                //弹英雄最大等级预览弹窗
                TipsUtil.OpenHeroInfoDialog(null, this._goodsVo.prop_value, 0, true);
            }
            else if (this._goodsVo.IsRide && this._goodsVo.oid == 0) {
                let bingfu_cfg = ConfigManager.cfg_bingfuCache.get(this._goodsVo.typeId);
                if (bingfu_cfg.lock_skill_1 != "") {
                    TipsUtil.ShowGoodsVoToTips(this._goodsVo, this.isShowBtn, this.isShowForgeBtn, null, this.isPathBtn, this.isBag, this._clickParam);
                }
                else {
                    DispatchManager.dispatchEvent("OPEN_BINGFU_COMPOSE_TIPS_DIALOG" /* OPEN_BINGFU_COMPOSE_TIPS_DIALOG */, { item_id: this._goodsVo.typeId });
                }
            }
            else {
                TipsUtil.ShowGoodsVoToTips(this._goodsVo, this.isShowBtn, this.isShowForgeBtn, null, this.isPathBtn, this.isBag, this._clickParam);
            }
        }
    }
    onClickGoodsItem() {
        TipsUtil.ShowPathTypeIdToTips(this.goodsVo.typeId);
    }
    set isShowForgeBtn(value) {
        this._isShowForgeBtn = value;
    }
    get isShowForgeBtn() {
        if (this.goodsVo.isEquipDebris) {
            return false;
        }
        if (this.goodsVo.hero_id > 0 && HeroDataCenter.checkIsInTempleByHeroId(this.goodsVo.hero_id)) {
            return false;
        }
        return this._isShowForgeBtn;
    }
    get clickEnable() {
        return this._clickEnable;
    }
    set clickEnable(value) {
        this._clickEnable = value;
        // if (this._clickEnable == false) {
        //     UIButton.RemoveAllOnClick(this);
        // } else {
        //     UIButton.RemoveAllOnClick(this);
        //     this.addClick();
        // }
    }
    /**通过GoodsVO来设置内容 一般用于有GoodsVO的物品 */
    SetData(vo) {
        this.SetGoodsVoData(vo);
    }
    /**通过goodsvo来设置内容 一般用于有p_goods的物品 */
    SetPgoodsData(p) {
        let vo = GoodsVO.GetVoByPGoods(p);
        this.SetGoodsVoData(vo);
    }
    /**通过goodsvo来设置内容 一般用于有p_goods的物品 */
    SetEquipData(p, hero_id) {
        let vo = GoodsVO.getVoByEquip(p, hero_id);
        this.SetGoodsVoData(vo);
    }
    /**通过物品id来查找配置表创建物品 , 与数据设置物品 id**/
    SetItemData(typeId, count = 1) {
        this.SetGoodsVoData(GoodsVO.GetVoByTypeId(typeId, count));
    }
    /**物品数据**/
    get goodsVo() {
        return this._goodsVo;
    }
    set isSelect(value) {
        this._isSelect = value;
        if (value == true && this._selectImg == null && this.goodsVo && this.isShowSelectIcon == true) {
            this._selectImg = new Image();
            this._selectImg.pos(this.box.width / 2, this.box.height / 2);
            this._selectImg.anchorX = 0.5;
            this._selectImg.anchorY = 0.5;
            this._selectImg.skin = UrlConfig.COMMON_PATH + "img_4.png";
            this.selectBg.addChild(this._selectImg);
        }
        if (this._selectImg) {
            this.selectBg.visible = this.goodsVo == null ? false : value;
        }
    }
    SetSelectImgSkin(str) {
        if (this._selectImg == null) {
            this._selectImg = new Image();
            this._selectImg.pos(this.box.width / 2, this.box.height / 2);
            this._selectImg.anchorX = 0.5;
            this._selectImg.anchorY = 0.5;
            this.selectBg.addChild(this._selectImg);
        }
        this._selectImg.skin = str;
    }
    get isSelect() {
        return this._isSelect;
    }
    UpdateItem(itemData, isSelect = false) {
        this.index = itemData.index;
        this.progressBar.visible = false;
        if (itemData.data != null) {
            this.setParameter(itemData.parameter);
            this.SetGoodsVoData(itemData.data);
        }
        this.isSelect = isSelect;
    }
    /**附带参数 */
    setParameter(parameter) {
        if (this.getParameterValue(parameter, "isClick") == null) {
            this.isClick = true;
        }
        else {
            this.isClick = this.getParameterValue(parameter, "isClick");
        }
        if (this.getParameterValue(parameter, "isSelect") == null) {
            this.isShowSelectIcon = true;
        }
        else {
            this.isShowSelectIcon = this.getParameterValue(parameter, "isSelect");
        }
        if (this.getParameterValue(parameter, "isShowBtn") != null) {
            this.isShowBtn = this.getParameterValue(parameter, "isShowBtn");
        }
        if (this.getParameterValue(parameter, "isShowNum") != null) {
            this.isShowNum = this.getParameterValue(parameter, "isShowNum");
        }
        if (this.getParameterValue(parameter, "isShowNameBg") != null) {
            this.isShowNameBg = this.getParameterValue(parameter, "isShowNameBg");
        }
        if (this.getParameterValue(parameter, "isShowProgress") != null) {
            this.isShowProgress = this.getParameterValue(parameter, "isShowProgress");
        }
        if (this.getParameterValue(parameter, "isBag") != null) {
            this.isBag = this.getParameterValue(parameter, "isBag");
        }
        if (this.getParameterValue(parameter, "hide_debris") != null) {
            this.hide_debris = this.getParameterValue(parameter, "hide_debris");
        }
        if (this.getParameterValue(parameter, "equip_compose_red_point") != null) {
            this.equip_compose_red_point = this.getParameterValue(parameter, "equip_compose_red_point");
        }
        if (this.getParameterValue(parameter, "isShowTips") != null) {
            this.isShowTips = this.getParameterValue(parameter, "isShowTips");
        }
        if (this.getParameterValue(parameter, "selectSkin") != null) {
            this.SetSelectImgSkin(this.getParameterValue(parameter, "selectSkin"));
        }
        if (this.getParameterValue(parameter, "titleShowId") != null) {
            this.titleShowId = this.getParameterValue(parameter, "titleShowId");
        }
        else {
            this.titleShowId = 0;
        }
        if (this.getParameterValue(parameter, "titleUrl") != null) {
            this.titleUrl = this.getParameterValue(parameter, "titleUrl");
        }
        else {
            this.titleUrl = "";
        }
        if (this.getParameterValue(parameter, "get_type") != null && this.getParameterValue(parameter, "get_type") == 999) {
            this.isGoldNotCrit = true;
        }
        else {
            this.isGoldNotCrit = false;
        }
        this._isShowCostNum = this.getParameterValue(parameter, "isShowCostNum");
        this.isAdd = this.getParameterValue(parameter, "isAdd");
        if (this.getParameterValue(parameter, "costNumLableColor")) {
            this._costNumLableColor = this.getParameterValue(parameter, "costNumLableColor");
        }
        else {
            this._costNumLableColor = "";
        }
    }
    /**设置装备品质框是否显示 */
    SetColorViside(value) {
        this.colorBg.visible = value;
    }
    /**设置角标 */
    setCornerMarkSkin(skin) {
        if (!this.cornerMarkImg && StringUtil.isEmpty(skin))
            return;
        if (!this.cornerMarkImg) {
            this.cornerMarkImg = new Image();
            this.icon.addChild(this.cornerMarkImg);
        }
        this.cornerMarkImg.skin = skin;
    }
    SetGoodsVoData(vo) {
        this.Clean();
        this._goodsVo = vo;
        if (!vo) {
            // console.trace();
            return;
        }
        this.SetIcon(this._goodsVo.skin);
        if (this.isGoldNotCrit) {
            this.SetGoodsNum(this._goodsVo.num);
        }
        else {
            this.SetGoodsNum(this._goodsVo.showUINum);
        }
        this.SetColor(this._goodsVo.color);
        this.setBigTypeShow(this._goodsVo.bigType);
        this.ShowColorEffect();
        this.setRareIcon();
        this.ShowItemEffect();
        this.setTimeTxt();
        this.setMinLevel();
        this.setCircleMask();
        this.setHeroSoulShow();
        this.setReward(this._goodsVo.isShowReward);
        this.setGuoQi(this._goodsVo.isShowGuoQi);
        this.setGemStack();
        this.checkPrivilege();
        this.showMiddle();
        this.showKuang();
        this.showCastSoul();
        this.showGodEquip();
        this.showDivineEquip();
        this.showCostNum();
        this.showAdd();
        this.setCostNumLableColor();
        if (this.goodsVo.kind == ItemMacro.ITEM_KIND_MASTER_CARD) {
            this.SetGoodsBgVisible(false);
        }
        if (vo && !StringUtil.isEmpty(vo.cornerMark)) {
            this.setCornerMarkSkin(UrlConfig.BASE_RES_UI_URL + `common/${vo.cornerMark}.png`);
        }
        if (this.isClick == false) {
            this.clickEnable = this.isClick;
        }
    }
    /**设置战魂的显示 */
    setHeroSoulShow() {
        if (!this._goodsVo || !this._goodsVo.isHeroSoul)
            return;
        this.isHideHeroSoulBg = this._isHideHeroSoulBg;
        this.heroSoulFrame.visible = true;
        this.heroSoulFrame.skin = UrlConfig.GOODS_RES_URL + `hero_soul_frame/item_frame_${this._goodsVo.color}.png`;
        let pos = this._goodsVo.kind % 10;
        let rotation = 0;
        switch (pos) {
            case 2:
                rotation = 90;
                break;
            case 3:
                rotation = 270;
                break;
            case 4:
                rotation = 180;
                break;
            default:
                rotation = 0;
                break;
        }
        this.heroSoulFrame.rotation = rotation;
        //this.refineTxt.text = "";//不显示精炼等级
    }
    setCircleMask() {
        if (this._goodsVo == null) {
            this.icon.mask = null;
            return;
        }
        if (this._goodsVo.isHeroSoul == true) {
            this.icon.mask = this.circleMask2;
        }
        else {
            this.icon.mask = null;
        }
    }
    get circleMask2() {
        if (this._circleMask == null) {
            this._circleMask = new Sprite();
            this._circleMask.name = "circleMask";
            this._circleMask.pos(48, 48);
            this._circleMask.graphics.drawCircle(0, 0, 36, "#eebb6f", 0);
            this._circleMask.visible = false;
            this.box.addChild(this._circleMask);
        }
        return this._circleMask;
    }
    /**是否隐藏战魂道具的背景 */
    set isHideHeroSoulBg(val) {
        this._isHideHeroSoulBg = val;
        if (!this._goodsVo || !this._goodsVo.isHeroSoul) {
            this.colorBg.mask = null;
            return;
        }
        if (val) {
            this.colorBg.mask = this.circleMask2;
            this.bg.visible = false;
        }
        else {
            this.colorBg.mask = null;
            this.bg.visible = true;
        }
    }
    get isHideHeroSoulBg() {
        return this._isHideHeroSoulBg;
    }
    setMinLevel() {
        if (this._goodsVo.isRoleEquip == true || (this._goodsVo.IsEquip == true && this.isShowForgeBtn == true) || this._goodsVo.item.min_level == 0) {
            //领主装备隐藏穿戴等级
            this.setMinLevelVisible(false);
            return;
        }
        if (this._minLevel == null) {
            this._minLevel = new FontClip();
            this._minLevel.name = "minLevel";
            this._minLevel.skin = "common/icon_ji_num.png";
            this._minLevel.sheet = "1234567890";
            this._minLevel.value = "0";
            this._minLevel.spaceX = -4;
            this._minLevel.pos(6, 68);
            this.box.addChild(this._minLevel);
            this._levelIcon = new Image();
            this._levelIcon.skin = "common/icon_ji.png";
            this._levelIcon.name = "levelIcon";
            this._levelIcon.pos(14, 0);
            this._minLevel.addChild(this._levelIcon);
        }
        this._minLevel.visible = this._goodsVo.item.min_level > 0;
        this._minLevel.value = this._goodsVo.item.min_level + "";
        this._levelIcon.x = 12 * this._minLevel.value.length + 2;
    }
    setMinLevelVisible(val) {
        if (this._minLevel) {
            this._minLevel.visible = val;
        }
    }
    setSelectTxt(str, color = "#ffffff", x = 90, y = 73, fontSize = 18, align = "right") {
        this.selectTxt.visible = true;
        this.selectTxt.text = str;
        this.selectTxt.color = color;
        this.selectTxt.pos(x, y);
        this.selectTxt.fontSize = fontSize;
        this.selectTxt.align = align;
    }
    equipVisible(val) {
        if (this._equipInfo) {
            this._equipInfo.visible = val;
        }
    }
    ShowEquipInfo() {
        if (this._equipInfo == null) {
            this._equipInfo = new GoodsEquipInfo();
            this.box.addChild(this._equipInfo);
        }
        this.equipVisible(true);
        this._equipInfo.setData(this.goodsVo);
        // if (this.goodsVo.strength > 0) {
        //     this.strengthTxt.text = this.goodsVo.strength.toString();
        //     this.strengthBg.skin = UrlConfig.COMMON_PATH + "strength_" + this.goodsVo.color + ".png";
        // }
        // else {
        //     this.strengthTxt.text = "";
        // }
        // this.strengthBg.visible = this.goodsVo.strength > 0;
        // this.carveColor.visible = this.goodsVo.carve > 0;
        // if (this.goodsVo.refine > 0) {
        //     this.refineTxt.text = "+" + this.goodsVo.refine;
        // }
        // else {
        //     this.refineTxt.text = "";
        // }
        // this.SetCarve();
    }
    //物品分类显示不同的内容
    setBigTypeShow(bigType) {
        switch (bigType) {
            case ItemConst.TYPE_EQUIP:
                let equipCfg = ConfigManager.cfg_equipCache.get(this._goodsVo.item.type_id);
                if (this._goodsVo.isHeroSoul == true) {
                    this.SetLevel(this._goodsVo.star);
                    this.imgBigStar.x = 48;
                }
                else if (this._goodsVo.isGodEquip == true) {
                    let godEquipCfg = ConfigManager.cfg_god_equipCache.get(this._goodsVo.item.type_id);
                    this.SetLevel(godEquipCfg.star, false);
                    this.imgBigStar.x = 48;
                }
                else {
                    if (equipCfg) {
                        this.SetLevel(equipCfg.star, false);
                    }
                    else {
                        this.SetLevel(this._goodsVo.equipStar, true);
                    }
                    this.imgBigStar.x = 74;
                }
                if (this.isShowEquipInfo || this._goodsVo.isHeroSoul) {
                    this.ShowEquipInfo();
                }
                break;
            case ItemConst.TYPE_ITEM:
                if (this._goodsVo.kind == ItemMacro.ITEM_KIND_HERO_FASHION) {
                    this.imgSkin.visible = true;
                }
                else if (this._goodsVo.kind == ItemMacro.ITEM_KIND_QI_MOU) {
                    this.SetLevel(this._goodsVo.star, false);
                }
                else if (this._goodsVo.kind == ItemMacro.ITEM_KIND_MASTER_CARD) {
                    this.mCard.visible = true;
                }
                if (this._goodsVo.isBox) {
                    this.SetLevel(this._goodsVo.star, false);
                }
                break;
            case ItemConst.TYPE_OTHER:
                this.SetLevel(this._goodsVo.star, false);
                break;
            case ItemConst.TYPE_DEBRIS:
                this.debris.visible = true;
                this.SetLevel(this._goodsVo.star, false);
                this.imgBigStar.x = 48;
                let cfgCompose = ConfigManager.cfg_item_composeCache.get(this._goodsVo.typeId);
                if (cfgCompose && this.isShowProgress) {
                    this.SetProgress(this._goodsVo.num, cfgCompose.need_num);
                }
                break;
            case ItemConst.TYPE_GIFT:
                this.ShowEquipInfo();
                break;
        }
        this.setNationImg(this._goodsVo.nation);
    }
    /**设置国家标志 */
    setNationImg(nation) {
        if (nation > 0) {
            this.nationImg.visible = true;
            this.nationImg.skin = UrlConfig.COMMON_PATH + "nation_" + nation + ".png";
        }
        else if (nation == 0 && this._goodsVo.kind == ItemMacro.ITEM_KIND_HERO_SOUL) {
            this.nationImg.visible = true;
            this.nationImg.skin = UrlConfig.COMMON_PATH + "nation_" + nation + ".png";
        }
        else {
            this.nationImg.visible = false;
        }
    }
    setDebrisImgVisible(val) {
        this.debris.visible = val;
    }
    /**设置物品星级 */
    SetLevel(level = 0, isHideStar = true) {
        /* 初始化星级 */
        this.starImg.visible = false;
        this.starMax.visible = false;
        this.unlockStarMax.visible = false;
        /* 龙魂单独处理星级 */
        if ((level >= 10 || isHideStar == true) && this.goodsVo.kind == ItemMacro.ITEM_KIND_QI_MOU) {
            //10星以上不显示星级
            if (this._list) {
                this.starList.visible = false;
            }
            /* 是否显示十星解锁框 */
            // let isShowUnlock = false;
            // this.goodsVo.goods.other_vals.forEach(item => {
            //     if (item.key == 1001) {
            //         isShowUnlock = true;
            //     }
            // });
            this.imgBigStar.visible = this.txtBigStarNum.visible = false;
            this.starList.visible = false;
            this.starMax.visible = level - HeroConsts.changeShowStar > 0 ? true : false;
            this.starLabel.text = level - HeroConsts.changeShowStar > 0 ? "" : "";
            this.unlockStarMax.visible = false;
            this.starImg.visible = false;
            this.starImg.skin = level - HeroConsts.changeShowStar > 0 ? "common/bag_star4.png" : "common/bag_star3.png";
            this.bg.skin = level - HeroConsts.changeShowStar > 0 ? UrlConfig.COMMON_PATH + `itemBg${5}.png` : "common/itemBg0.png";
            this.starBgImg.visible = level - HeroConsts.changeShowStar > 0 ? false : true;
            this.starImg.visible = level - HeroConsts.changeShowStar > 0 ? false : true;
            // if (this.goodsVo.star == 10) {
            //     this.starBgImg.visible = isShowUnlock ? false : true;
            //     this.starImg.visible = !isShowUnlock ? true : false;
            //     this.unlockStarMax.visible = !isShowUnlock ? false : true;
            // } else {
            //     this.starBgImg.visible = level - HeroConsts.changeShowStar > 0 ? false : true;
            //     this.starImg.visible = level - HeroConsts.changeShowStar > 0 ? false : true;
            // }
            return;
        }
        /* 其他星级处理 */
        else {
            if (level >= 10 || isHideStar == true) {
                //10星以上不显示星级
                if (this._list) {
                    this.starList.visible = false;
                }
                this.imgBigStar.visible = this.txtBigStarNum.visible = false;
                this.starList.visible = false;
                this.starBgImg.visible = level - HeroConsts.changeShowStar > 0 ? false : true;
                this.starMax.visible = level - HeroConsts.changeShowStar > 0 ? true : false;
                ;
                this.starBgImg.visible = level > 0;
                this.starLabel.text = level - HeroConsts.changeShowStar > 0 ? "" : "";
                this.starBgImg.skin = level - HeroConsts.changeShowStar > 0 ? "" : "common3/bag_star3_bg.png";
                this.starImg.skin = level - HeroConsts.changeShowStar > 0 ? "common/bag_star4.png" : "common/bag_star3.png";
                this.bg.visible = true;
                this.bg.skin = level - HeroConsts.changeShowStar > 0 ? UrlConfig.COMMON_PATH + `itemBg${5}.png` : "common/itemBg0.png";
                this.starImg.visible = level - HeroConsts.changeShowStar > 0 ? false : true;
                return;
            }
        }
        this.starList.visible = true;
        this.starBgImg.visible = false;
        let arr = [];
        let newLevel = HeroDataCenter.getAwakenLevel(level);
        if (this.goodsVo.IsEquip) {
            newLevel = level;
        }
        for (let i = 1; i <= newLevel; i++) {
            arr.push(i);
        }
        if (!this.goodsVo.IsDivine) {
            if (arr.length > 0) {
                this.initStar();
                this._list.parameter = { level: level, vo: this.goodsVo };
                this._list.array = arr;
            }
            if (this._list) {
                this.starList.visible = arr.length > 0;
            }
        }
        else {
            this.starList.visible = false;
        }
    }
    initStar() {
        if (this._list != null) {
            return;
        }
        this.starList.mouseEnabled = false;
        this._list = UIList.SetUIList(this, this.starList, HeroStarLevelItem, null, false);
        this._list.SetSpace(-15, -7);
        this._list.SetRepeat(7, 1);
        this._list.isBoxCenter = true;
        this._list.scrollBarHide = true;
        this._list.isBoxCenter = true;
    }
    /**设置兵器宝石升级+num */
    setGemStack() {
        let stackNum = 0;
        if (this._goodsVo.isStone && this._goodsVo.item.prop_value != 0) {
            stackNum = this._goodsVo.item.prop_value;
        }
        else if (this._goodsVo.isRoleEquip && this._goodsVo.item.params_list != "0") {
            stackNum = Number(this._goodsVo.item.params_list);
        }
        if (stackNum && stackNum > 0) {
            if (this._imgStack == null) {
                this._imgStack = new Image();
                this._imgStack.name = "imgStack";
                this._imgStack.pos(50, 6);
                this._imgStack.size(40, 21);
                this._imgStack.skin = "common/img_13.png";
                this.box.addChild(this._imgStack);
                this._txtStack = new Label();
                this._txtStack.name = "txtStack";
                this._txtStack.pos(50, 8);
                this._txtStack.size(40, 20);
                this._txtStack.fontSize = 20;
                this._txtStack.bold = false;
                this._txtStack.color = "#ffffff";
                this._txtStack.strokeColor = "#000000";
                this._txtStack.fontSize = 20;
                this._txtStack.align = "center";
                this.box.addChild(this._txtStack);
            }
            this._imgStack.visible = true;
            this._txtStack.visible = true;
            this._txtStack.text = "+" + stackNum;
        }
        else {
            if (this._imgStack) {
                this._imgStack.visible = false;
                this._txtStack.visible = false;
            }
        }
    }
    setGemStackVisible(value) {
        if (this._imgStack) {
            this._imgStack.visible = value;
            this._txtStack.visible = value;
        }
    }
    /**设置进度条 暂时只有英雄碎片用 */
    SetProgress(cur, max) {
        this.progressBar.visible = true;
        this.progress.SetValue(cur, max);
    }
    /**设置进度条 暂时只有英雄碎片用 */
    HideProgress() {
        this.progressBar.visible = false;
    }
    /**隐藏物品数量*/
    HideGoodsNum() {
        this.SetGoodsNumStr("");
    }
    /**隐藏国家图标 */
    HideGoodsNation() {
        this.nationImg.visible = false;
    }
    /**显示活动奖励图标 */
    Awardicon() {
        this.award.visible = true;
    }
    SetGoodsBgVisible(val) {
        this.bg.visible = val;
    }
    SetNameBgVisible(val) {
        this.imgNameBg.visible = val;
    }
    SetNameBgSkin(skin) {
        this.imgNameBg.skin = skin;
    }
    /**设置物品数量*/
    SetGoodsNum(value) {
        if (!this.isShowNum) {
            this.SetGoodsNumStr("");
            return;
        }
        if (value > this.goodsVo.showNum) {
            this.SetGoodsNumStr(MoneyTransformUtil.MoneyFormat(value));
        }
        else {
            this.SetGoodsNumStr("");
        }
    }
    SetGoodsNumStr(value, color = "", fontSize = 0) {
        this.numTxt.text = value;
        if (this.goodsVo && this.goodsVo.isShowPossessNum) {
            this.numTxt.text = this.goodsVo.has_count + "/" + value;
        }
        this.numTxt.width = this.numTxt.textField.textWidth;
        this.numBg.visible = value.length > 0;
        this.numBg.width = this.numTxt.textField.textWidth + 10;
        if (fontSize > 0) {
            this.numTxt.fontSize = fontSize;
        }
        if (color != null && color != "") {
            this.numTxt.color = color;
        }
    }
    /**设置物品图标**/
    SetIcon(itemPicTempName) {
        this.icon.visible = true;
        this.icon.skin = itemPicTempName;
    }
    /**设置品质框**/
    SetColor(color) {
        var _a;
        if (this.goodsVo.kind == ItemMacro.ITEM_KIND_MASTER_CARD) {
            this.colorBg.visible = true;
            /* 名将卡特效 */
            this.colorBg.skin = `mastercart/${(_a = this.goodsVo.item) === null || _a === void 0 ? void 0 : _a.color}_10.png`;
        }
        else {
            this.colorBg.visible = true;
            this.colorBg.skin = UrlConfig.COMMON_PATH + "itemBg" + color + ".png";
        }
    }
    /**装备刻印 */
    // public SetCarve(): void {
    //     if (this.goodsVo.carve > 0) {
    //         let cfg: cfg_equip_carve_suit = this.goodsVo.cfgCarveSuit;
    //         this.carveName.text = cfg.suit_name;
    //         this.carveColor.skin = UrlConfig.COMMON_PATH + "carve_color_" + cfg.color + ".png";
    //     }
    // }
    /***
     *
     * 设置物品名称
     * @param len 为9表示要分多行
     * @param height 表示你要移动y轴的高度
     */
    setGoodsName(itemName = "", fontSize = 20, len = -1, color = "", height = 0) {
        itemName = itemName ? itemName : "";
        let oriLen = len;
        this._wordWrap = len >= 9 || len <= 0;
        if (this._wordWrap == true) {
            len = 9;
        }
        if (len > oriLen) {
            oriLen = len;
        }
        this._nameTxt = this.nameTxt;
        this._nameTxt.fontSize = fontSize;
        this._nameTxt.y -= height;
        if (itemName.length <= 0) {
            itemName = this.goodsVo.item.name || "";
        }
        if (itemName.length > (oriLen + 1) && len > 0) {
            itemName = itemName.substring(0, oriLen) + "...";
        }
        this._nameTxt.text = itemName;
        this._nameTxt.color = ColorUtil.GetQualityToColor(this.goodsVo.color);
        if (color != "") {
            this._nameTxt.color = color;
        }
    }
    /**道具名称控件 */
    get nameTxt() {
        if (this._nameTxt == null) {
            this._nameTxt = new Label();
            let offsetX = this._wordWrap == true ? -5 : -11;
            if (Render.isConchApp == true) {
                this._nameTxt.pos(offsetX, 102);
            }
            else {
                this._nameTxt.pos(offsetX, 105);
            }
            if (this._wordWrap == true) {
                this._nameTxt.size(110, 38);
            }
            else {
                this._nameTxt.size(160, 18);
            }
            this._nameTxt.align = "center";
            this._nameTxt.valign = "middle";
            this._nameTxt.font = Text.defaultFont;
            this._nameTxt.bold = false;
            this._nameTxt.leading = 2;
            this._nameTxt.wordWrap = this._wordWrap;
            this._nameTxt.color = ColorUtil.FONT_NORAML;
            this.box.addChild(this._nameTxt);
            this._nameTxt.centerX = 0;
        }
        return this._nameTxt;
    }
    /* 已经领取 */
    setReward(showReward = false) {
        if (showReward) {
            if (this._lingquMask == null) {
                this._lingquMask = new Image("common/guide_mask_bg.png");
                this._lingquMask.name = "lingquMask";
                this._lingquMask.pos(0, 0);
                this._lingquMask.size(96, 96);
                this.box.addChild(this._lingquMask);
            }
            else {
                this._lingquMask.visible = true;
            }
            if (this._yilingqu == null) {
                this._yilingqu = new Image("common3/yilingqu.png");
                this._yilingqu.name = "yilingqu";
                this._yilingqu.pos(-3.5, 9);
                this.box.addChild(this._yilingqu);
            }
            else {
                this._yilingqu.visible = true;
            }
        }
        else {
            if (this._lingquMask) {
                this._lingquMask.visible = false;
            }
            if (this._yilingqu) {
                this._yilingqu.visible = false;
            }
        }
    }
    /**设置是否过期 */
    setGuoQi(b) {
        if (b) {
            if (!this._imgGuoqi) {
                this._imgGuoqi = new Image("common/yiguoqi2.png");
                this._imgGuoqi.size(75, 32);
                this._imgGuoqi.pos(this.width - this._imgGuoqi.width - 5, 5);
                this._imgGuoqi.zOrder = 1000;
                this.addChild(this._imgGuoqi);
            }
            this._imgGuoqi.visible = true;
        }
        else {
            if (this._imgGuoqi) {
                this._imgGuoqi.visible = false;
            }
        }
    }
    get timeMask() {
        if (this._timeMask == null) {
            this._timeMask = new Image("common/item_mask.png");
            this._timeMask.pos(5, 5);
            this._timeMask.size(86, 27);
            this.box.addChild(this._timeMask);
        }
        return this._timeMask;
    }
    setTimeTxt() {
        if (!this.goodsVo || !this.goodsVo.item)
            return;
        if (this.goodsVo.item.kind == 1108 || this.goodsVo.item.kind == 1107 || this.goodsVo.item.open_day_limit > 0) {
            if (this.goodsVo.item.open_day_limit > 0 && DataCenter.serverOpenedDay >= this._goodsVo.item.open_day_limit) {
                return;
            }
            if (this._timeTxt == null) {
                this._timeTxt = new Label();
                this._timeTxt.size(84, 24);
                this._timeTxt.y = 4;
                this._timeTxt.fontSize = 20;
                this._timeTxt.color = "#ffffff";
                this._timeTxt.align = "center";
                this._timeTxt.bold = false;
                this._timeTxt.stroke = 1;
                this.timeMask.addChild(this._timeTxt);
            }
            this.timeMask.visible = true;
            if (this.goodsVo.item.open_day_limit > 0) {
                this._timeTxt.text = window.iLang.L2_WEI_JIE_SUO.il();
                this._timeTxt.color = ColorUtil.FONT_RED;
            }
            else {
                this._timeTxt.color = "#ffffff";
                this._timeTxt.text = this.goodsVo.item.prop_value + window.iLang.L_XS_HOUR.il();
            }
        }
    }
    checkPrivilege() {
        if (this.titleUrl && this.titleUrl.length > 3 && (this.titleShowId == 0 || this.goodsVo.typeId == this.titleShowId)) {
            if (this._titleshow == null) {
                this._titleshow = new Image();
                this._titleshow.name = "titleshow";
                this._titleshow.pos(-2, 0);
                this.box.addChild(this._titleshow);
            }
            this._titleshow.skin = this.titleUrl;
            this._titleshow.visible = true && GlobalConfig.showRecharge;
        }
        else {
            if (this._titleshow) {
                this._titleshow.visible = false;
            }
        }
    }
    SetLockVisible(val) {
        this.imgLock.visible = val;
    }
    showCastSoul(soul_hero_id = 0) {
        let hero_id = this.goodsVo.hero_id;
        if (soul_hero_id != 0) {
            hero_id = soul_hero_id;
        }
        if (ItemConst.isEquip(this.goodsVo.kind) && hero_id != 0) {
            let ph = HeroDataCenter.instance.getHero(hero_id);
            if (ph != undefined && ph.star >= 10 || this.goodsVo.IsDivine) {
                let castsoul_obj = HeroDataCenter.instance.checkHeroEquipCastSoul(hero_id, this.goodsVo.kind);
                if (castsoul_obj != null) {
                    let castSoulVo = CastSoulVo.create(castsoul_obj.csvo.goods);
                    if (castsoul_obj.equipvo && (castsoul_obj.equipvo.color == 5 || castsoul_obj.equipvo.IsDivine)) {
                        if (castsoul_obj.equipvo.IsDivine) {
                            this.imgCastsoul.skin = "common/cs_" + castSoulVo.level + ".png";
                        }
                        else {
                            this.imgCastsoul.skin = "common/cs_" + Math.min(castsoul_obj.equipvo.star, castSoulVo.level) + ".png";
                        }
                        this.imgCastsoul.visible = true;
                    }
                }
                else {
                    this.imgCastsoul.visible = false;
                }
            }
        }
    }
    showFiveHeroResonate(val) {
        this.imgFiveHeroResonate.visible = val;
    }
    showOtherHeroEquip(data) {
        if (data == undefined) {
            return;
        }
        let equip_vo = data.equip;
        let castsoul_pg = data.castsoul;
        if (equip_vo != null && equip_vo.color == 5 && castsoul_pg != null) {
            let castSoulVo = CastSoulVo.create(castsoul_pg);
            this.imgCastsoul.skin = "common/cs_" + Math.min(equip_vo.star, castSoulVo.level) + ".png";
            this.imgCastsoul.visible = true;
        }
        this._clickParam = { data: data };
    }
    showOtherHeroGodEquip(data) {
        if (data == undefined) {
            return;
        }
        this._clickParam = { data: data };
    }
    showMockPVPTujianQimou(data) {
        if (data == undefined) {
            return;
        }
        this._clickParam = { data: data };
    }
    showGodEquip() {
        if (ItemConst.isHeroGodEquip(this.goodsVo.kind)) {
            let skinName = "";
            switch (this.goodsVo.color) {
                case 3:
                    skinName = "";
                    break;
                case 5:
                    skinName = "legendary";
                    break;
                default:
                    skinName = "";
                    break;
            }
            if (skinName != "") {
                this.imgGodEquip.skin = "common/" + skinName + ".png";
                this.imgGodEquip.visible = true;
            }
            this.imgGodEquipEnchant.visible = this.goodsVo.goods.skills.length > 0;
        }
    }
    /**神临装备 */
    showDivineEquip() {
        if (ItemConst.isDivineEquipByColor(this.goodsVo.color, this.goodsVo.kind)) {
            this.boxDivine.visible = true;
            let lv = window.DivineDataCenter.instance.getDivineEquipLv(this.goodsVo);
            this.lbDivineLv.text = lv > 0 ? `+${lv}` : "";
            let suitId = this.goodsVo.suit_id;
            this.imgDivineKind.skin = UrlConfig.DIVINE_URL + `suit_${suitId}.png`;
            let heroTypeId = window.DivineDataCenter.instance.getInvalidActivateVal(this.goodsVo);
            if (heroTypeId) {
                this.imgDivineHead.skin = UrlConfig.DIVINE_HEAD_URL + heroTypeId + ".png";
                this.imgHeadKuang.visible = true;
            }
            else {
                this.imgHeadKuang.visible = false;
                this.imgDivineHead.skin = "";
            }
        }
        else {
            this.boxDivine.visible = false;
        }
    }
    showMiddle() {
        // if (this.goodsVo.kind == ItemMacro.ITEM_KIND_HONGBAO) {
        //     if (this._middleshow == null) {
        //         this._middleshow = new com.ui.res.base.GoodsMiddleShowUI();
        //         this._middleshow.pos(3, 37);
        //         this.box.addChild(this._middleshow);
        //     }
        //     this._middleshow.visible = true;
        //     let hongbaocfg: cfg_family_hongbao = ConfigManager.cfg_family_hongbaoItemCache.get(this.goodsVo.typeId);
        //     if (hongbaocfg) {
        //         let goodsVo: GoodsVO = GoodsVO.GetGoodsByString(hongbaocfg.total_rewards);
        //         this._middleshow.icon.skin = UrlConfig.getGoodsIconByTypeId(goodsVo.typeId);
        //         this._middleshow.numTxt.text = "" + goodsVo.showUINum;
        //     }
        // }
    }
    Clean() {
        this.equipVisible(false);
        this.setGemStackVisible(false);
        this.setMinLevelVisible(false);
        this.setGuoQi(false);
        this.icon.skin = "";
        this.icon.visible = false;
        //this.icon.destroyChildren();
        this.bg.visible = true;
        this.bg.skin = "common/itemBg0.png";
        this.colorBg.visible = false;
        this.box.gray = false;
        this.imgUsed.visible = false;
        this.starMax.visible = false;
        //this.carveColor.visible = false;
        // this.numTxt.text = "";
        this.SetGoodsNumStr("");
        this.numBg.visible = false;
        if (this._jiping) {
            this._jiping.visible = false;
        }
        this._goodsVo = null;
        if (this.cornerMarkImg) {
            this.cornerMarkImg.skin = "";
        }
        if (this._list) {
            this._list.visible = false;
        }
        this.imgBigStar.visible = false;
        this.starBgImg.visible = false;
        this.mCard.visible = false;
        this.debris.visible = false;
        this.imgSkin.visible = false;
        this.nationImg.visible = false;
        this.award.visible = false;
        if (this._nameTxt) {
            this._nameTxt.text = "";
        }
        if (this._timeMask) {
            this._timeMask.visible = false;
        }
        if (this._lingquMask) {
            this._lingquMask.visible = false;
        }
        if (this._yilingqu) {
            this._yilingqu.visible = false;
        }
        this.heroSoulFrame.visible = false;
        //this.icon.mask = null;
        //this.colorBg.mask = null;
        // this.imgStack.visible = false;
        // this.titleshow.visible = false;
        if (this._middleshow) {
            this._middleshow.visible = false;
        }
        this.imgNameBg.width = 115;
        this.selectTxt.text = "";
        this.selectTxt.visible = false;
        this.imgCastsoul.visible = false;
        this.imgGodEquip.visible = false;
        this.imgGodEquipEnchant.visible = false;
        this.imgLock.visible = false;
        this.imgFiveHeroResonate.visible = false;
        this.boxDivine.visible = false;
        this.imgHeadKuang.visible = false;
        this.imgDivineHead.skin = "";
        this.lbDivineLv.text = "";
    }
    destroy(destroyChild = true) {
        this.icon.skin = null;
        this.delayDestroyChidren();
        // super.destroy(true);
    }
    /**子控件太多了,很花时间,可以做优化,延迟delayChildren */
    // private destroyTime = 0;
    delayDestroyChidren() {
        //不能destroyed
        //this.destroyed = true;
        this._destroyAllComponent();
        this._parent && this._parent.removeChild(this);
        //移除所有事件监听
        this.offAll();
        // this.destroyTime = Browser.now();
        TimerUtils.callInFreeFrame(500, 4000, this, this.doDelayDestroyChidren, [true]);
    }
    doDelayDestroyChidren() {
        // console.log('------------yhd---------------destroyTime = ' + (Browser.now() - this.destroyTime));
        super.destroy();
    }
    set visible(bool) {
        this.set_visible(bool);
    }
    get visible() {
        return this.get_visible();
    }
    set gray(val) {
        this.box.gray = val;
    }
    //留动画接口
    /**显示品质特效**/
    ShowColorEffect(effName = null) {
        if (effName == null) {
            effName = GoodsItem.getColorEffectName(this._goodsVo);
        }
        if (this.ccAnimation != null) {
            this.ccAnimation.visible = !StringUtil.isEmpty(effName);
        }
        if (effName) {
            let ccUrl = UrlConfig.EFFECT_PATH + effName + AnimationManager.DEFINE_SUFFIX;
            if (UrlConfig.checkResExist(ccUrl)) {
                if (this.ccAnimation != null && this.ccAnimation instanceof CCAnimation) {
                    AnimationManager.updateEffect(this.ccAnimation, effName, true);
                }
                else {
                    if (this.ccAnimation != null) {
                        this.ccAnimation.destroy();
                    }
                    this.ccAnimation = this.ShowEffectCenter(effName, this.colorBg, true);
                }
            }
            else {
                if (this.ccAnimation != null && this.ccAnimation instanceof GSkeleton) {
                    this.ccAnimation = this.showGSkeleton(this.colorBg, effName, this.ccAnimation);
                }
                else {
                    if (this.ccAnimation != null) {
                        this.ccAnimation.destroy();
                    }
                    this.ccAnimation = this.showGSkeleton(this.colorBg, effName, null);
                }
            }
        }
    }
    /**设置图标 */
    setRareIcon() {
        if (this._goodsVo && this._goodsVo.item && this._goodsVo.item.light > 100 && this._goodsVo.item.light < 200) {
            if (this._jiping == null) {
                this._jiping = new Image();
                this._jiping.name = "jiping";
                this._jiping.pos(0, 48);
                this.box.addChild(this._jiping);
            }
            this._jiping.skin = StringUtil.Format("common/ji_ping_{0}.png", this._goodsVo.item.light);
            this._jiping.visible = true;
        }
    }
    /**显示物品独有特效 */
    ShowItemEffect(effName = null) {
        var _a, _b;
        if (!this.goodsVo || !this.goodsVo.item)
            return;
        if (effName == null) {
            effName = this.goodsVo.item.effect;
        }
        if (this.ccItemEffect != null) {
            this.ccItemEffect.visible = !StringUtil.isEmpty(effName);
        }
        if (this.goodsVo.kind == ItemMacro.ITEM_KIND_MASTER_CARD) {
            /* 名将卡特效 */
            if (this.ccItemEffect) {
                this.ccItemEffect.removeSelf();
                this.ccItemEffect.destroy();
                this.ccItemEffect = null;
                this.ccItemEffect = SkeletonManager.ins.createSkeleton(`card_item_${(_a = this.goodsVo.item) === null || _a === void 0 ? void 0 : _a.color}`, ESkeletonType.UI_EFFECT, { isLoop: true });
                this.ccItemEffect.scale(0.78, 0.78);
                this.ccItemEffect.pos(20, 25);
                this.effect.addChild(this.ccItemEffect);
                return;
            }
            else {
                this.ccItemEffect = SkeletonManager.ins.createSkeleton(`card_item_${(_b = this.goodsVo.item) === null || _b === void 0 ? void 0 : _b.color}`, ESkeletonType.UI_EFFECT, { isLoop: true });
                this.ccItemEffect.scale(0.78, 0.78);
                this.ccItemEffect.pos(20, 25);
                this.effect.addChild(this.ccItemEffect);
                return;
            }
        }
        if (effName && effName.length > 0) {
            if (this.ccItemEffect != null && this.ccItemEffect instanceof CCAnimation) {
                AnimationManager.updateEffect(this.ccItemEffect, effName, true);
            }
            else {
                this.ccItemEffect = this.ShowEffectCenter(effName, this.icon, true);
            }
            const cfgItem = CfgCacheMapMgr.cfg_itemCache.get(this._goodsVo.typeId);
            if (cfgItem && cfgItem.effect_scale) {
                const scale = cfgItem.effect_scale.split("|");
                this.ccItemEffect.scaleX = parseFloat(scale[0]) || 1;
                this.ccItemEffect.scaleY = parseFloat(scale[1]) || 1;
            }
            else {
                this.ccItemEffect.scaleX = 1;
                this.ccItemEffect.scaleY = 1;
            }
        }
    }
    HideItemEffect() {
        if (this.ccItemEffect != null) {
            this.ccItemEffect.destroy();
            this.ccItemEffect = null;
        }
    }
    /**屏蔽品质特效**/
    HideColorEffect() {
        if (this.ccAnimation != null) {
            this.ccAnimation.destroy();
            this.ccAnimation = null;
        }
    }
    /**获取品质特效名称 */
    static getColorEffectName(vo) {
        if (vo && vo.item && vo.item.light > 0 && vo.item.light < 100) {
            /**龙魂解锁终极升星后需要特殊处理特效 */
            if (vo.kind == ItemMacro.ITEM_KIND_QI_MOU && vo.star >= 10) {
                let isShowUnlock = false;
                vo.goods.other_vals.forEach(item => {
                    if (item.key == 1001) {
                        isShowUnlock = true;
                    }
                });
                if (isShowUnlock) {
                    let color = vo.star - HeroConsts.changeShowStar > 0 ? 6 : 5;
                    return StringUtil.Format("item_{0}", color);
                }
            }
            return StringUtil.Format("item_{0}", vo.item.color);
        }
        return null;
    }
    //设置是否展示名将卡详情
    setIsShowMasterCartdInfo(visible) {
        this.isGoMasterCartInfo = visible;
    }
    showKuang() {
        var _a;
        if (this.goodsVo.kind == ItemMacro.ITEM_KIND_MASTER_CARD) {
            this.kuang.visible = true;
            /* 名将卡特效 */
            this.kuang.skin = `mastercart/${(_a = this.goodsVo.item) === null || _a === void 0 ? void 0 : _a.color}_11.png`;
        }
        else {
            this.kuang.visible = false;
        }
    }
    showCostNum(isShow = this.goodsVo.isShowCostNum) {
        if (isShow || this._isShowCostNum) {
            GameUtil.setItemCostLabel(this.numLabel, this.goodsVo.typeId, this.goodsVo.num);
        }
        else {
            this.numLabel.text = "";
        }
    }
    showAdd(isShowAdd = this.goodsVo.isAdd) {
        if (isShowAdd || this.isAdd) {
            this.imgAdd.visible = true;
        }
        else {
            this.imgAdd.visible = false;
        }
    }
    setCostNumLableColor() {
        if (this._isShowCostNum && this._costNumLableColor) {
            this.numLabel.color = this._costNumLableColor;
        }
    }
}
