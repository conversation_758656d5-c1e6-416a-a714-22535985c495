import { CfgCacheMapMgr } from "../../../../cfg/CfgCacheMapMgr";
import { ConfigManager } from "../../../../managers/ConfigManager";
import { TipsUtil } from "../../../../util/TipsUtil";
import { FightDataCenter } from "../../../fight/data/FightDataCenter";
import { SysDataCenter } from "../../../system/data/SysDataCenter";
import { RankDataCenter } from "../../data/RankDataCenter";
import { BaseRankScript } from "../BaseRankScript";
import { MatchConst } from "../../../../auto/ConstAuto";
/**跨服排位 */
export class RandomPvPRankItemScript extends BaseRankScript {
    get tempView() {
        return super.tempView;
    }
    get txtFamilyName() {
        if (!this.tempView)
            return null;
        return this.tempView.txtFamilyName;
    }
    get headBox() {
        if (!this.tempView)
            return null;
        return this.tempView.headBox;
    }
    get txtName() {
        if (!this.tempView)
            return null;
        return this.tempView.txtRoleName;
    }
    /**战力 */
    get txtPower() {
        if (!this.tempView)
            return null;
        return this.tempView.txtPower;
    }
    /**段位 */
    get txtGradeName() {
        if (!this.tempView)
            return null;
        return this.tempView.txtGradeName;
    }
    /**排行数值 */
    get txtSortVal() {
        if (!this.tempView)
            return null;
        return this.tempView.txtScore;
    }
    /**点赞按钮 */
    get btnZan() {
        if (!this.tempView)
            return null;
        return this.tempView.btnZan;
    }
    /**勋章 */
    get medalslist() {
        if (!this.tempView)
            return null;
        return this.tempView.medalslist;
    }
    onAwake() {
        super.onAwake();
        this.isShowFcRank = true;
    }
    onClickBtnZan() {
        if (this.rankItemVo && this.btnZan && this.btnZan.tag) {
            // RankDataCenter.instance.m_ranking_worship_tos(this.rank_key, this.btnZan.tag);
            let usedTimes = SysDataCenter.instance.GetSysUseTime(11);
            if (usedTimes > 0) {
                FightDataCenter.instance.m_fight_start_tos(MatchConst.MATCH_TYPE_CROSS_CONTEST, this.btnZan.tag, {
                    target_arg: [this.rankItemVo.server_key],
                    uid: this.btnZan.tag,
                    ext: this.rankItemVo.server_key,
                });
            }
            else {
                let timesCfg = CfgCacheMapMgr.cfg_sys_use_timesCache.get(11);
                TipsUtil.showTips(window.iLang.L2_MEI_TIAN_ZHI_NENG_QIE_CUO_P0_CI.il([timesCfg.max_use_times]));
            }
        }
    }
    //更新单条Item数据
    updateZan(key, id) {
        if (this.rankItemVo) {
            if (this.rankItemVo.role_id == id && this.rankItemVo.rank_key == key) {
                let rankVo = RankDataCenter.instance.getRankVoByRankKey(this.rank_key, this.rankItemVo.server_type);
                if (this.btnZan) {
                    // this.btnZan.label = "" + this.rankItemVo.worship_times
                    // this.btnZan.gray = rankVo.isWorship(this.rankItemVo.role_id);
                    let usedTimes = SysDataCenter.instance.GetSysUseTime(11);
                    this.btnZan.label = window.iLang.L2_QIE_CUO.il();
                    this.btnZan.visible = rankVo.self_ranking > 0 && Math.abs(this.rankItemVo.order_id - rankVo.self_ranking) == 1;
                    this.btnZan.gray = usedTimes <= 0;
                }
            }
        }
    }
    updateRankItemView(itemVo) {
        super.updateRankItemView(itemVo);
        if (!itemVo)
            return;
        let familyName = itemVo.secnd_name && itemVo.secnd_name.length > 0 ? window.iLang.L2_FAMILY_ch05.il() + itemVo.secnd_name : window.iLang.L2_ZAN_WU_FAMILY.il();
        let rankVo = RankDataCenter.instance.getRankVoByRankKey(this.rank_key, itemVo.server_type);
        this.setFamilyName(familyName);
        this.setGradeName(Number(itemVo.getOther_vals(1)));
        if (this.btnZan) {
            // this.btnZan.label = "" + itemVo.worship_times;
            // this.btnZan.gray = rankVo.isWorship(itemVo.role_id);
            let usedTimes = SysDataCenter.instance.GetSysUseTime(11);
            this.btnZan.label = window.iLang.L2_QIE_CUO.il();
            this.btnZan.visible = rankVo.self_ranking > 0 && Math.abs(itemVo.order_id - rankVo.self_ranking) == 1;
            this.btnZan.gray = usedTimes <= 0;
        }
    }
    /**设置公会名称 */
    setFamilyName(text) {
        if (!this.txtFamilyName) {
            return;
        }
        this.txtFamilyName.text = text;
    }
    /**设置段位 */
    setGradeName(grade_id) {
        let cfg = ConfigManager.cfg_random_pvpAllCache.get(grade_id);
        if (!cfg) {
            //没有段位
            // this.imgSortValIcon.skin = "";
            this.txtGradeName.text = window.iLang.L2_DUAN_WEI_ch05_ZAN_WU_DUAN_WEI.il();
        }
        else {
            // this.imgSortValIcon.skin = UrlConfig.BASE_RES_UI_URL + `random_pvp/${cfg.icon}.png`;
            this.txtGradeName.text = window.iLang.L2_DUAN_WEI_ch05.il() + cfg.full_name;
        }
    }
}
