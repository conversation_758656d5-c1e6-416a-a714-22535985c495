import { ILaya } from "ILaya";
import { HTMLStyle } from "laya/html/utils/HTMLStyle";
import { Browser } from "laya/utils/Browser";
import { Handler } from "laya/utils/Handler";
import { GlobalConfig } from "../../game/GlobalConfig";
import { ConfigManager } from "../managers/ConfigManager";
import { DispatchManager } from "../managers/DispatchManager";
import { LayerManager } from "../managers/LayerManager";
import { DataCenter } from "../modules/DataCenter";
import { GodEquipVO } from "../modules/equip/data/GodEquipVO";
import { FightDataCenter } from "../modules/fight/data/FightDataCenter";
import FlyAttributeChangeDialog from "../modules/flyFont/dialog/FlyAttributeChangeDialog";
import FlyFontVo from "../modules/flyFont/vo/FlyFontVo";
import { GoodsVO } from "../modules/goods/GoodsVO";
import { ItemConst } from "../modules/goods/ItemConst";
import { HeroDataCenter } from "../modules/hero/data/HeroDataCenter";
import { HeroRecycleNewDataCenter } from "../modules/heroRecycle/data/HeroRecycleNewDataCenter";
import { BaseLineUpLimitHelper } from "../modules/lineUp/helper/BaseLineUpLimitHelper";
import { LineUpVO } from "../modules/lineUp/vo/LineUpVO";
import { MiscConst } from "../modules/misc_config/MiscConst";
import { PanelEventConstants } from "../modules/PanelEventConstants";
import { VipTeQuanConst } from "../modules/payment/VipTeQuanConst";
import VipTeQuanUtil from "../modules/payment/VipTeQuanUtil";
import { PaymentVO } from "../modules/payment/vo/PaymentVO";
import { FirstPayType, RechargeDataCenter } from "../modules/recharge/data/RechargeDataCenter";
import { RoleDataCenter } from "../modules/role/data/RoleDataCenter";
import { SettingDataCenter } from "../modules/setting/data/SettingDataCenter";
import { TodayTipSetting } from "../modules/setting/TodayTipSetting";
import { BagDataCenter } from "../modules/test_bag/data/BagDataCenter";
import { GoodsManager } from "../modules/test_bag/GoodsManager";
import { AlertDialogParam } from "../modules/tips/data/AlertConst";
import { ErrorConst } from "../modules/tips/data/ErrorConst";
import AlertDialog from "../modules/tips/view/AlertDialog";
import { AlertErrorDialog } from "../modules/tips/view/AlertErrorDialog";
import Toast from "../modules/tips/view/Toast";
import { YueKaDataCenter } from "../modules/welfare/data/YueKaDataCenter";
import { ArrayUtil } from "./ArrayUtil";
import { ColorUtil } from "./ColorUtil";
import { GameUtil } from "./GameUtil";
import { HtmlUtil } from "./HtmlUtil";
import { StringUtil } from "./StringUtil";
import { BaseDialog } from "../modules/BaseDialog";
import { CostAlertDialog, CostAlertDialogParam } from "../modules/riverTreasure/dialog/CostAlertDialog";
import { DateUtil } from "./DateUtil";
import { UrlConfig } from "../../game/UrlConfig";
import { MiscConstAuto } from "../auto/MiscConstAuto";
import { ItemMacro } from "../auto/ItemMacro";
import { CountryWarAlertDialog, WarAlertDialogParam } from "../modules/countryWar/dialog/CountryWarAlertDialog";
import { ConsoleUtils, EConsoleLevel } from "./ConsoleUtils";
export class TipsUtil {
    //public static  tipsPanel:TipsPanelView;
    //public static  roseEffectCon:RoseEffectCon;
    /**
     * @param caller 执行的类
     * @param content 内容
     * @param title 标题
     * @param okCall 确认回调
     * @param cancelCall 取消回调
     * @param args 参数回调 多个用Array 如:[参数1，参数2,..]
     * @param btnType 按钮类型
     * @param okName 按钮确认名称
     * @param cancelName 按钮取消名称
     * @param isFirstLine 启用首行缩进(默认true启用)
     * @param firstTimeKey 本次登录不再提示key
     * @param todayTipKey 今日不再提示id; 优先判断 firstTimeKey；如果有firstTimeKey则以firstTimeKey为准
     * @param onlyOneTipKey 只提示一次
     * @param isCloseWhenClickEmpty 是否点击空白处关闭
     * @param isSingle 是否进行单例判断
     * */
    static showDialog(caller, content, title = "", confirmCall = null, param = {}) {
        param = AlertDialogParam.cloneFrom(param);
        if (param.firstTimeKey.length > 0) {
            if (AlertDialog.alertDialogKey.has(param.firstTimeKey) == true) {
                let m_okCall;
                if (param.args instanceof Array) {
                    m_okCall = new Handler(caller, confirmCall, param.args);
                }
                else {
                    m_okCall = new Handler(caller, confirmCall, [param.args]);
                }
                if (m_okCall.method != null) {
                    m_okCall.run();
                }
                return null;
            }
        }
        else if (param.todayTipKey && param.todayTipKey.length > 0) {
            if (TodayTipSetting.checkTodayTip(param.todayTipKey) == false) {
                let m_okCall;
                if (param.args instanceof Array) {
                    m_okCall = new Handler(caller, confirmCall, param.args);
                }
                else {
                    m_okCall = new Handler(caller, confirmCall, [param.args]);
                }
                if (m_okCall.method != null) {
                    m_okCall.run();
                }
                return null;
            }
        }
        else if (param.onlyOneTipKey && param.onlyOneTipKey.length > 0) {
            if (SettingDataCenter.instance.getVal(param.onlyOneTipKey) > 1) {
                let m_okCall;
                if (param.args instanceof Array) {
                    m_okCall = new Handler(caller, confirmCall, param.args);
                }
                else {
                    m_okCall = new Handler(caller, confirmCall, [param.args]);
                }
                if (m_okCall.method != null) {
                    m_okCall.run();
                }
                return null;
            }
        }
        if (param.singleKey && param.singleKey.length > 0 && AlertDialog.isHave(param.singleKey)) {
            return null;
        }
        param.caller = caller;
        param.content = content;
        param.title = title;
        param.okCall = confirmCall;
        // DispatchManager.dispatchEvent(ModuleCommand.OPEN_TIPS_DIALOG, param);
        let dialog = new AlertDialog();
        dialog.open(false, param);
        return dialog;
    }
    static showErrorDialog(caller, content, confirmCall, param = {}) {
        param = AlertDialogParam.cloneFrom(param);
        param.caller = caller;
        param.content = content;
        param.okCall = confirmCall;
        let dialog = new AlertErrorDialog();
        dialog.open(false, param);
        return dialog;
    }
    static checkGoodsNumberAndShowTips(goodstype, goodsNum, tipsshow, confirmCall = null) {
        let p1 = HtmlUtil.GetHtmlImg(UrlConfig.getGoodsIconByTypeId(goodstype), 30, 30);
        let p2 = goodstype == ItemConst.COST_GOLD ? GameUtil.gold(goodsNum) : goodsNum;
        let p3 = tipsshow;
        let content = window.iLang.L2_SHI_FOU_HUA_FEI_P0_P1_GOU_MAI_P2_ch27.il([p1, p2, p3]);
        TipsUtil.showDialog(this, content, window.iLang.L2_TI_ch11_SHI.il(), confirmCall);
    }
    /**
     * @param title 标题
     * @param content 内容
     * @param fetchName 领取按钮名称
     * @param fetchedName 已领取按钮名称
     * @param caller 执行的类
     * @param fetchCall 领取回调
     * @param fetchedCall 确认回调
     * @param args 参数回调 多个用Array 如:[参数1，参数2,..]
     * */
    static showPreviewRewardDialog(goodsVoList = [], { title = "奖励预览", content = "", contentY = null, fetchName = "我知道了", fetchedName = "", caller = null, fetchCall = null, fetchedCall = null, args = null, cls = null, parameter = null, padding = null, contentFontSize = 20 } = {}) {
        let param = {
            goodsVoList: goodsVoList,
            title: title,
            content: content,
            contentY: contentY,
            fetchName: fetchName,
            fetchedName: fetchedName,
            caller: caller,
            fetchCall: fetchCall,
            fetchedCall: fetchedCall,
            args: args,
            cls: cls,
            parameter: parameter,
            padding: padding,
        };
        DispatchManager.dispatchEvent("OPEN_PREVIEW_REWARD_DIALOG" /* OPEN_PREVIEW_REWARD_DIALOG */, param);
    }
    /**
     * 提示通用的错误码和原因
     * @param {number} code
     * @param {string} reason
     * @param {[]} params
     */
    static showErrCode(code, reason, params = null) {
        if (ErrorConst.ERR_MATERIAL_NOT_ENOUGH == code) {
            if (params && params.length > 0) {
                let itemTypeID = parseInt(params[0]);
                let item = ConfigManager.cfg_itemCache.get(itemTypeID);
                if (item != null) {
                    TipsUtil.onShowItemNotHavePanel(itemTypeID);
                }
                else {
                    TipsUtil.showTips("物品id不存在:" + params[0]);
                }
            }
            else if (reason.length > 0) {
                TipsUtil.showTips(reason + "数量不足");
            }
            else {
                TipsUtil.showTips("材料数量不足");
            }
            return;
        }
        else if (code == ErrorConst.ERR_DIRECT) {
            if (params && params.length > 0) {
                TipsUtil.showTips(params[0] + "数量不足");
            }
            return;
        }
        else if (code >= ErrorConst.ERR_ATTR_NOT_ENOUGH_MIN && code < ErrorConst.ERR_ATTR_NOT_ENOUGH_MAX) {
            if (params && params.length > 0) {
                let itemTypeID = parseInt(params[0]);
                if (itemTypeID > 0) {
                    //DispatchManager
                    if (!this.specialHand(itemTypeID)) {
                        TipsUtil.ShowPathTypeIdToTips(itemTypeID, true);
                    }
                }
            }
            return;
        }
        else if (code == ErrorConst.ERR_MAX_HERO_COUNT) { //英雄背包已满
            let content = "";
            let eventId = 0;
            let childId = 0;
            if (!YueKaDataCenter.instance.isOpenFuliYueKa(4)) {
                content = "英雄背包空间数量不足，可通过激活月卡增加英雄携带数量，是否前往激活？";
                eventId = PanelEventConstants.PAYMENT;
                childId = PaymentVO.KEY_WELFARE_CARD;
            }
            else if (HeroRecycleNewDataCenter.instance.checkHasCanRebirth()) {
                content = "英雄背包空间数量不足，可通过献祭英雄清理英雄携带数量，是否前往献祭？";
                eventId = PanelEventConstants.RECYCLE;
                childId = 1;
            }
            else {
                content = "英雄背包空间数量不足，可通过提升VIP等级或购买增加英雄携带数量，是否前往购买？";
                eventId = PanelEventConstants.PAYMENT;
                childId = PaymentVO.KEY_SVIP_SHOP;
            }
            TipsUtil.showDialog(this, content, "提 示", () => {
                GameUtil.gotoSystemPanelById(eventId, childId);
            }, { okName: "前 往" });
            return;
        }
        else if (code == ErrorConst.ERR_CROSS_LADDER_ROLE_RANK_CHANGE) { //匹配的玩家排名发生变化
            TipsUtil.showDialog(this, "玩家排名发生改变，请刷新后再挑战！", "", function () {
                DispatchManager.dispatchEvent("CROSS_LADDER_REQ_REFRESH" /* CROSS_LADDER_REQ_REFRESH */);
            });
            return;
        }
        else if (code == ErrorConst.ERR_HERO_STAR_STAGE_UPDATE_LIMIT) { //英雄星阶升星条件不足
            // GameUtil.gotoSystemPanelById(PanelEventConstants.DUDUFU);
        }
        else if (code == ErrorConst.ERR_MAX_GOD_EQUIP_COUNT) { //英雄神装背包已满
            TipsUtil.showDialog(this, "神装背包空间不足，请领主出售多余神装或进行神装升星。", "", function () {
                GameUtil.gotoSystemPanelById(PanelEventConstants.BAG, BagDataCenter.TAB_GODEQUIP);
            });
        }
        else if (code == ErrorConst.ERR_CROSS_TEST_TOWER_UPDATE_LIMIT) {
            DispatchManager.dispatchEvent("CLOSE_FIGHT_DIALOG" /* CLOSE_FIGHT_DIALOG */);
            DispatchManager.dispatchEvent("CLOSE_CROSS_TEST_TOWER_DIALOG" /* CLOSE_CROSS_TEST_TOWER_DIALOG */);
            TipsUtil.onceOpenUI("OPEN_CITY_DIALOG" /* OPEN_CITY_DIALOG */);
            TipsUtil.showTips("不在开放时间");
            return;
        }
        else if (code == ErrorConst.ERR_CROSS_TEST_TOWER_DONEXT_UPDATE_LIMIT) {
            DispatchManager.dispatchEvent("CLOSE_FIGHT_DIALOG" /* CLOSE_FIGHT_DIALOG */);
            DispatchManager.dispatchEvent("CLOSE_CROSS_TEST_TOWER_DIALOG" /* CLOSE_CROSS_TEST_TOWER_DIALOG */);
            TipsUtil.onceOpenUI("OPEN_CITY_DIALOG" /* OPEN_CITY_DIALOG */);
            TipsUtil.showTips("不在开放时间");
            return;
        }
        else if (code == ErrorConst.ERR_CROSS_TEST_TOWER_LINEUP_UPDATE_LIMIT) {
            DispatchManager.dispatchEvent("CLOSE_FIGHT_DIALOG" /* CLOSE_FIGHT_DIALOG */);
            DispatchManager.dispatchEvent("CLOSE_CROSS_TEST_TOWER_DIALOG" /* CLOSE_CROSS_TEST_TOWER_DIALOG */);
            TipsUtil.onceOpenUI("OPEN_CITY_DIALOG" /* OPEN_CITY_DIALOG */);
            TipsUtil.onceOpenUI("OPEN_CROSS_TEST_TOWER_NATION_DIALOG" /* OPEN_CROSS_TEST_TOWER_NATION_DIALOG */);
            return;
        }
        let errMsg = this.getErrorReason(code, reason, params);
        if (errMsg != null && errMsg != "") {
            TipsUtil.showTips(StringUtil.Format(errMsg, params));
            return;
        }
        TipsUtil.showTips("错误码:" + code);
    }
    static showReplaceBuyDialog(caller, price, replaceCall = null, normalCall = null) {
        let replaceRmbItemNum = GoodsManager.instance.GetGoodsNumByTypeId(MiscConst.riche_act_shop_replace_rmb_item);
        if (replaceRmbItemNum >= price && price != 0) {
            DispatchManager.dispatchEvent("OPEN_REPLACE_BUY_DIALOG" /* OPEN_REPLACE_BUY_DIALOG */, { caller: caller, price: price, replaceCall: replaceCall, normalCall: normalCall });
        }
        else {
            let m_normalCall = new Handler(caller, normalCall);
            m_normalCall.run();
        }
    }
    static getErrorReason(code, reason, params = null) {
        if (reason != "" && reason != null) {
            return reason;
        }
        else {
            let vo = ConfigManager.cfg_errorCodeCache.get(code);
            if (vo) {
                let msg = vo.codeReason;
                if (!GlobalConfig.showRecharge && vo.codeId == 101) { //暂时只有一个要特殊处理，先不加配置，后面再有就修改配置处理。
                    msg = "钻石不足";
                }
                return this.dealTipsStr(msg, params);
            }
        }
        return null;
    }
    /**部分道具不足时需要特殊处理 */
    static specialHand(itemID) {
        let param = [];
        if (itemID == ItemConst.COST_SILVER) {
            param = MiscConstAuto.silver_special_link;
        }
        else if (itemID == ItemConst.COST_HERO_EXP) {
            param = MiscConstAuto.hero_exp_special_link;
        }
        else {
        }
        if (param.length > 0) {
            let openDay = Number(param[1]);
            let openEvent = param[2];
            if (DataCenter.earliestOpenedDay > openDay) {
                return false;
            }
            let item = ConfigManager.cfg_itemCache.get(itemID);
            if (item != null) {
                TipsUtil.showTips(item.name + "数量不足");
            }
            DispatchManager.dispatchEvent(openEvent);
            return true;
        }
        return false;
    }
    /**
     * 通用提示
     * @param code
     * @param reason
     * @param params
     */
    static showTipsCode(code, reason, params = null) {
        let tipMsg;
        if (reason != "" && reason != null) {
            tipMsg = reason;
        }
        else {
            let vo = ConfigManager.cfg_tipsCache.get(code);
            if (vo) {
                tipMsg = this.dealTipsStr(vo.msg, params);
            }
        }
        if (tipMsg != null && tipMsg != "") {
            TipsUtil.showTips(StringUtil.Format(tipMsg, params));
        }
    }
    static dealTipsStr(msg, params = null) {
        if (!msg || msg == "")
            return msg;
        let temArray = msg.split("|");
        if (temArray.length >= 2) {
            let strArray;
            let i = 0;
            let pNum = 0;
            msg = "";
            for (i; i < temArray.length; i++) {
                strArray = temArray[i].split("_");
                if (strArray[0] == "exp") {
                    if (params[pNum] > 0) {
                        msg += params[pNum] + "经验";
                    }
                    pNum++;
                }
                else if (strArray[0] == "gold") {
                    if (params[pNum] > 0) {
                        msg += params[pNum] + "钻石";
                    }
                    pNum++;
                }
                else if (strArray[0] == "string") {
                    msg += params[pNum];
                    pNum += 1;
                }
                else if (strArray[0] == "num") {
                    msg += "×" + params[pNum];
                    pNum += 1;
                }
                else if (strArray[0] == "time") {
                    msg += DateUtil.GetDhOrHMOrMS(params[pNum]);
                    pNum += 1;
                }
                else if (strArray[0] == "hero") {
                    let cfg = ConfigManager.cfg_hero_baseCache.get(Number(params[pNum]));
                    if (cfg) {
                        msg += cfg.name;
                    }
                }
                else if (strArray[0] == "item") {
                    let cfg = ConfigManager.cfg_itemCache.get(Number(params[pNum]));
                    if (cfg) {
                        msg += cfg.name;
                    }
                }
                else {
                    msg += strArray[0];
                }
            }
        }
        return msg;
    }
    /**
     * 飘字提醒(DEBUG模式)
     * @param text 飘字内容
     * @param isShowLog 是否打印控制台日志
     * @param logLevels 日志等级
     */
    static showDebugTips(text, isShowLog = true, logLevels = "Info") {
        if (GlobalConfig.IsDebug) {
            TipsUtil.showTips(text, "#ffffff", "common2_preload/tipsBg.png");
        }
        if (isShowLog) {
            if (logLevels == "Info") {
                console.info(text);
            }
            else if (logLevels == "Warnings") {
                console.warn(text);
            }
            else if (logLevels == "Errors") {
                console.error(text);
            }
        }
    }
    /**
     * 飘字提醒
     * @param text 飘字内容
     * @param color 飘字颜色
     * @param bgSkin 背景
     */
    static showTips(text, color = "#ffffff", bgSkin = "common2_preload/tipsBg.png") {
        // Toast.show(text, { color: color, showType: Toast.NORMAL_TYPE, bgSkin: bgSkin });
        TipsUtil.showTips3(text, { color: color, showType: Toast.NORMAL_TYPE, bgSkin: bgSkin });
    }
    /**
     * 飘字提醒 （有缩放效果）
     * @param text 飘字内容
     * @param color 飘字颜色
     * @param bgSkin 背景
     */
    static showTips2(text, color = "#ffffff", bgSkin = "common2_preload/tipsBg.png") {
        // Toast.show(text, { color: color, showType: Toast.SCALE_TYPE, bgSkin: bgSkin });
        TipsUtil.showTips3(text, { color: color, showType: Toast.SCALE_TYPE, bgSkin: bgSkin });
    }
    /**
     * 飘字提醒 （图文混排,有缩放效果）
     * @param text 飘字内容
     * @param image 图片
     * @param color 飘字颜色
     * @param imgUrl 背景
     */
    static showTipsImg(text, image = null, color = "#ffffff", bgSkin = "common2/tipsBg.png", pos = null) {
        let style = new HTMLStyle();
        style.wordWrap = false;
        Toast.show(text, { color: color, showType: Toast.SCALE_TYPE, bgSkin: bgSkin, image: image, pos: pos, style: style, showMultiline: false });
    }
    /**
    * 飘字提醒 （可调整文本样式）
    * @param text 飘字内容
    * @param param1
    */
    static showTips3(text, { color = "#ffffff", fontSize = 22, bold = false, wordWrap = true, stroke = 0, strokeColor = "#000000", showType = Toast.NORMAL_TYPE, bgSkin = "common2_preload/tipsBg.png", leading = 0, height = 40, showMultiline = true, consoleLevel = EConsoleLevel.none, } = {}) {
        let style = new HTMLStyle();
        style.color = color;
        style.fontSize = fontSize;
        style.bold = false;
        style.wordWrap = wordWrap;
        style.stroke = stroke;
        style.strokeColor = strokeColor;
        style.leading = leading;
        Toast.show(text, { color: color, showType: showType, bgSkin: bgSkin, style: style, height: height, showMultiline: showMultiline });
        if (consoleLevel) {
            ConsoleUtils.sendConsoleLog(consoleLevel, text);
        }
    }
    /**打开描述界面 */
    static showDescDialog(desc, { fontSize = 24, color = "#551511", leading = 6, width = 0, height = 0, zOrder = null, bgSkin = "common2/content_bg_2.png", } = {}) {
        if (desc) {
            DispatchManager.dispatchEvent("OPEN_COMMON_DESC_DIALOG" /* OPEN_COMMON_DESC_DIALOG */, {
                zOrder: zOrder,
                fontSize: fontSize,
                color: color,
                leading: leading,
                width: width,
                height: height,
                bgSkin: bgSkin,
                desc: desc,
            });
        }
    }
    /**道具获得飘字提醒 */
    static showGoodsGainTip(type_id, num) {
        TipsUtil.showGoodsGainTipByVo(GoodsVO.GetVoByTypeId(type_id, num));
    }
    /**道具获得飘字提醒 */
    static showGoodsGainTipByVo(vo) {
        if (vo && vo.item && vo.typeId) {
            // var img: Image = new Image(vo.skin);
            // img.scale(0.63, 0.63);
            TipsUtil.showTipsImg("获得" + HtmlUtil.GetHtmlImg(vo.skin, 45, 45) + vo.showUINum, null, ColorUtil.GetQualityToColor(vo.color));
        }
    }
    /**道具获得飘字提醒 */
    static showGoodsListTip(voList) {
        if (voList) {
            voList.forEach((vo) => {
                TipsUtil.showGoodsGainTipByVo(vo);
            });
        }
    }
    /**
         * @param goodsVO 显示物品详情
         * @param isShowBtn true 表示显示默认按钮
         * @param isShowEquipForge true 表示显示装备强化按钮
         * @param btns 自定义按钮点击事件
         * */
    static ShowGoodsVoToTips(goodsVo, isShowBtn = false, isShowForgeBtn = false, btns = null, isPathBtn = true, isBag = false, param = null) {
        let vo = {
            goods: goodsVo,
            isShowBtn: isShowBtn,
            isShowForgeBtn: isShowForgeBtn,
            zOrder: 0,
            btns: btns,
            isPathBtn: isPathBtn,
            isBag: isBag,
            param: param
        };
        if (goodsVo.kind == ItemMacro.ITEM_KIND_HERO_FASHION) {
            //时装
            DispatchManager.dispatchEvent("OPEN_HERO_SKIN_TIP_DIALOG" /* OPEN_HERO_SKIN_TIP_DIALOG */, vo);
        }
        else if (goodsVo.kind == ItemMacro.ITEM_KIND_SCENE_SKIN) {
            //场景特效
            DispatchManager.dispatchEvent("OPEN_ROLE_SCENE_EFFECT_TIP_DIALOG" /* OPEN_ROLE_SCENE_EFFECT_TIP_DIALOG */, vo);
        }
        else if (goodsVo.isGodEquip) {
            TipsUtil.showGodEquipTip(goodsVo, {
                isShowBtn: isShowBtn,
                isBag: isBag,
                otherHeroParam: param
            });
        }
        else if (goodsVo.isBox) {
            if (ConfigManager.cfg_main_battle_boxByItemIdCache.get(goodsVo.typeId) != undefined) {
                vo.isShowBtn = false;
                vo.isPathBtn = false;
            }
            DispatchManager.dispatchEvent("OPEN_GOOD_BOX_TIPS_DIALOG" /* OPEN_GOOD_BOX_TIPS_DIALOG */, vo);
        }
        else if (goodsVo.kind == ItemMacro.ITEM_KIND_LORD_TREASURE) {
            //如果是领主宝物，打开领主宝物详情页面
            DispatchManager.dispatchEvent("OPEN_LORD_TREASURE_TIP_DIALOG" /* OPEN_LORD_TREASURE_TIP_DIALOG */, { showGoodsVO: goodsVo });
        }
        else {
            DispatchManager.dispatchEvent("OPEN_GOOD_TIPS" /* OPEN_GOOD_TIPS */, vo);
        }
        //DispatchManager.dispatchEvent(ModuleCommand.OPEN_FORGE_STAR_TIPS_DIALOG, goodsVo);
    }
    /**
     * 显示物品详情
     * @param goodsVo
     * @param params 可选参数(常用的参数有 isShowBtn = 是否显示默认按钮；isShowEquipForge = 是否显示强化按钮 )
     */
    static showGoodsTips(goodsVo, params = {}) {
        if (!params)
            params = {};
        params["goods"] = goodsVo;
        params["zOrder"] = 0;
        if (goodsVo.kind == ItemMacro.ITEM_KIND_LORD_TREASURE) {
            //如果是领主宝物，打开领主宝物详情页面
            DispatchManager.dispatchEvent("OPEN_LORD_TREASURE_TIP_DIALOG" /* OPEN_LORD_TREASURE_TIP_DIALOG */, { showGoodsVO: goodsVo });
        }
        else {
            DispatchManager.dispatchEvent("OPEN_GOOD_TIPS" /* OPEN_GOOD_TIPS */, params);
        }
    }
    /**
     * 物品对比
     * @param params vo1装备物品 vo2选中物品
     */
    static showGoodsContrastTips(params) {
        DispatchManager.dispatchEvent("OPEN_GOODS_CONTRAST_DIALOG" /* OPEN_GOODS_CONTRAST_DIALOG */, params);
    }
    /**
     * 神装Tip
     */
    static showGodEquipTip(godEquip, { isShowDescTip = false, isShowAttr = true, isShowAttrTip = true, isShowBtn = false, isBag = false, isShowChange = false, otherHeroParam = null, isMockPVPGodEquip = false, mockPVPGodEquipList = null, } = {}) {
        if (isShowDescTip) {
            DispatchManager.dispatchEvent("OPEN_GOD_EQUIP_DESC_TIP_DIALOG" /* OPEN_GOD_EQUIP_DESC_TIP_DIALOG */, {
                godEquipVO: new GodEquipVO(godEquip),
            });
        }
        else {
            DispatchManager.dispatchEvent("OPEN_GOD_EQUIP_TIP_DIALOG" /* OPEN_GOD_EQUIP_TIP_DIALOG */, {
                godEquipVO: new GodEquipVO(godEquip),
                isShowAttr: isShowAttr,
                isShowAttrTip: isShowAttrTip,
                isShowBtn: isShowBtn,
                isBag: isBag,
                isShowChange: isShowChange,
                otherHeroParam: otherHeroParam,
                isMockPVPGodEquip: isMockPVPGodEquip,
                mockPVPGodEquipList: mockPVPGodEquipList,
            });
        }
    }
    /**
     * @param hero: 英雄数据
     * @param type_id: 英雄type_id
     * @param index: 标签ID
     * @param isPreview: 是否满级数据
     * @param isShowSingleNation: 如果是显示多个英雄得情况下，切换是否显示单一国家列表的英雄
     * @param isShowBackButton: 是否显示右下角返回按钮
     */
    static OpenHeroInfoDialog(hero, type_id, index = 0, isPreview = false, isShowSingleNation = false, isShowBackButton = false, navShow = null) {
        let obj = {
            hero: hero,
            type_id: type_id,
            index: index,
            isShowSingleNation: isShowSingleNation,
            navShow: navShow
        };
        // let vo = new HeroInfoDialogVo();
        // vo.hero = hero;
        // vo.type_id = type_id;
        // vo.index = index;
        // vo.isShowSingleNation = isShowSingleNation;
        // vo.isShowBackButton = isShowBackButton;
        if (isPreview == true) {
            DispatchManager.dispatchEvent("OPEN_HERO_TUJIAN_INFO_DIALOG" /* OPEN_HERO_TUJIAN_INFO_DIALOG */, { starIndex: 1, index: 0, nation: 0, typeId: type_id });
        }
        else {
            DispatchManager.dispatchEvent("OPEN_HERO_IFNO_DIALOG" /* OPEN_HERO_IFNO_DIALOG */, obj);
        }
    }
    /**
     * 未解锁的英雄基础信息
     */
    static OpenHeroSingleInfoDialog(type_id) {
        DispatchManager.dispatchEvent("OPEN_HERO_SINGLE_IFNO_DIALOG" /* OPEN_HERO_SINGLE_IFNO_DIALOG */, type_id);
    }
    /**
     * 打开布阵界面
     * @param lineup_type 阵容类型
     * @param lineUpVo 界面参数
     */
    static openHeroLineUpDialog(lineup_type, lineUpVo = null) {
        if (!lineUpVo) {
            lineUpVo = new LineUpVO();
        }
        if (lineUpVo.matchType == 0) {
            lineUpVo.matchType = lineup_type;
        }
        if (!lineUpVo.limitHelper) {
            lineUpVo.limitHelper = new BaseLineUpLimitHelper(lineup_type);
        }
        if (!lineUpVo.saveTips) {
            lineUpVo.saveTips = "保存成功";
        }
        DispatchManager.dispatchEvent("OPEN_LINE_UP_DIALOG" /* OPEN_LINE_UP_DIALOG */, [lineup_type, lineUpVo]);
    }
    /**
         * 显示物品获得路径
         * @param goodsVO 显示物品详情
         * @param isShowPath true 表示显示获得路径
         * @param isShowBtn true 表示显示默认按钮
         * */
    static ShowPathGoodsVoToTips(goodsVo) {
        //TipsUtil.ShowGoodsVoToTips(goodsVo, isShowBtn, isShowPath);
        DispatchManager.dispatchEvent("OPEN_GOODS_PATH_DIALOG" /* OPEN_GOODS_PATH_DIALOG */, goodsVo);
    }
    /**
     * 显示物品获得路径
     * @param typeId cfg_item.type_id
     */
    static ShowPathTypeIdToTips(typeId, isTips = false) {
        if (isTips == true) {
            let item = ConfigManager.cfg_itemCache.get(typeId);
            if (item != null) {
                TipsUtil.showTips(item.name + "数量不足");
            }
        }
        if (typeId > 0) {
            let count = GoodsManager.instance.GetGoodsNumByTypeId(typeId);
            TipsUtil.ShowPathGoodsVoToTips(GoodsVO.GetVoByTypeId(typeId, count));
        }
    }
    /**查看其他玩家信息 */
    static openRoleOtherInfo(roleId, { server_key = 0, way = 1, lineUpType = 1, teamId = 1, navShow = 3, } = {}) {
        if (!roleId)
            return;
        DispatchManager.dispatchEvent("OPEN_ROLE_OTHER_INFO_DIALOG" /* OPEN_ROLE_OTHER_INFO_DIALOG */, { hero_id: roleId, server_key: server_key, way: way, lineUpType: lineUpType, teamId: teamId, navShow: navShow });
    }
    /**
     * 通用奖励弹窗
     * @param goods 显示的道具列表
     */
    static showRewardDialog(goodsVo, getType = 0, privilegeId = 0, privilegeCost = null, show_hero = true) {
        if (goodsVo && goodsVo.length > 0) {
            // for (let i = 0; i < goodsVo.length; i++) {
            //     if (goodsVo[i].IsEquip && goodsVo[i].color >= 2) {
            //         DispatchManager.dispatchEvent(ModuleCommand.OPEN_EQUIP_COMPOSE_TIPS_DIALOG, { goods: goodsVo, get_type: getType, privilege_id: privilegeId, privilege_cost: privilegeCost });
            //         return;
            //     }
            // }
            //DispatchManager.dispatchEvent(ModuleCommand.OPEN_COMMON_REWARD_TIP_DIALOG, { goods: goodsVo, get_type: getType });
            //if (getType != GameConst.MATCH_TYPE_DAILY_FB)
            TipsUtil.setPopUpWindows("OPEN_COMMON_REWARD_TIP_DIALOG" /* OPEN_COMMON_REWARD_TIP_DIALOG */, { goods: goodsVo, get_type: getType, privilege_id: privilegeId, privilege_cost: privilegeCost, show_hero: show_hero });
            // DispatchManager.dispatchEvent(ModuleCommand.UPDATE_FIGHT_FINISH_MISSION_INFO, { goods: goodsVo, get_type: getType })
        }
    }
    /**
     * 带宝箱动画的奖励弹窗
     */
    static showRewardEffDialog(goodsVo, getType = 0, privilegeId = 0, privilegeCost = null) {
        TipsUtil.setPopUpWindows("OPEN_COMMON_REWARD_EFF_TIP_DIALOG" /* OPEN_COMMON_REWARD_EFF_TIP_DIALOG */, { goods: goodsVo, get_type: getType, privilege_id: privilegeId, privilege_cost: privilegeCost });
    }
    /**
     * 获得英雄的获得弹窗
     * @param type_id 显示的英雄type_id
     * @param show_type 展示类型  1 = 获得新的英雄； 2 = 英雄升星表现 3:英雄升品表现,4:英雄继承
     * @param agrs 其他参数
     */
    static showHeroGainDialog(type_id, show_type = 1, agrs = null) {
        if (ConfigManager.cfg_hero_baseCache.has(type_id)) {
            if (show_type == 4) {
                TipsUtil.setPopUpWindows("OPEN_HERO_INHERIT_SUCC_DIALOG" /* OPEN_HERO_INHERIT_SUCC_DIALOG */, { type_id: type_id, show_type: show_type, args: agrs });
            }
            else {
                TipsUtil.setPopUpWindows("OPEN_HERO_SHOW_DIALOG" /* OPEN_HERO_SHOW_DIALOG */, { type_id: type_id, show_type: show_type, args: agrs });
            }
            // DispatchManager.dispatchEvent(ModuleCommand.OPEN_HERO_SHOW_DIALOG, { type_id: type_id, show_type: show_type, args: agrs });
        }
        else {
            console.error("<<<<<<<<不存在的英雄id ", type_id);
        }
    }
    /**
     * 更换英雄，选择列表
     * @param postion 需要换将的位置索引（从0开始，发送给后端时需要 +1）
     * @param lineup_type 需要换将的阵容,(暂时有换将操作的 只有养成位和援军阵容)
     */
    static showChangeHeroDialog(position, lineup_type = HeroDataCenter.LINE_UP_BREED) {
        DispatchManager.dispatchEvent("OPEN_LINE_UP_HERO_LIST_DIALOG" /* OPEN_LINE_UP_HERO_LIST_DIALOG */, {
            type: lineup_type,
            position: position
        });
    }
    /**
     * 弹道具不足弹窗
     * @param type_id 道具id
     */
    static onShowItemNotHavePanel(type_id, isShowTip = false, checkPrivilege = true) {
        TipsUtil.ShowPathTypeIdToTips(type_id, isShowTip);
    }
    // /**获取快捷商店配置 */
    // public static getCfgQuickShop(type_id: number): cfg_shop_quick {
    //     return ConfigManager.cfg_shop_quickCache.get(type_id);
    // }
    /**
    * 确认按钮和取消按钮下面带有消耗道具的提示框
    * @param caller 执行的类
    * @param content 内容
    * @param title 标题
    * @param okCall 确认回调
    * @param cancelCall 取消回调
    * */
    static showPropDialog(caller, content, title = "提示", okCall = null, cancelCall = null, param = new CostAlertDialogParam()) {
        param.caller = caller;
        param.content = content;
        param.title = title;
        param.okCall = okCall;
        param.cancelCall = cancelCall;
        param = CostAlertDialogParam.cloneFrom(param);
        if (param.firstTimeKey.length > 0) {
            if (AlertDialog.alertDialogKey.has(param.firstTimeKey) == true) {
                let m_okCall;
                if (param.args instanceof Array) {
                    m_okCall = new Handler(caller, okCall, param.args);
                }
                else {
                    m_okCall = new Handler(caller, okCall, [param.args]);
                }
                if (m_okCall.method != null) {
                    m_okCall.run();
                }
                return null;
            }
        }
        else if (param.todayTipKey && param.todayTipKey.length > 0) {
            if (TodayTipSetting.checkTodayTip(param.todayTipKey) == false) {
                let m_okCall;
                if (param.args instanceof Array) {
                    m_okCall = new Handler(caller, okCall, param.args);
                }
                else {
                    m_okCall = new Handler(caller, okCall, [param.args]);
                }
                if (m_okCall.method != null) {
                    m_okCall.run();
                }
                return null;
            }
        }
        let dialog = new CostAlertDialog(param);
        dialog.open(false, param);
        return dialog;
    }
    /**
    * 确认按钮和取消按钮下面带有消耗道具的提示框
    * @param caller 执行的类
    * @param content 内容
    * @param title 标题
    * @param okCall 确认回调
    * @param cancelCall 取消回调
    * */
    static showWarPropDialog(caller, content, title = "提示", okCall = null, cancelCall = null, param = new WarAlertDialogParam()) {
        param.caller = caller;
        param.content = content;
        param.title = title;
        param.okCall = okCall;
        param.cancelCall = cancelCall;
        param = WarAlertDialogParam.cloneFrom(param);
        if (param.firstTimeKey.length > 0) {
            if (AlertDialog.alertDialogKey.has(param.firstTimeKey) == true) {
                let m_okCall;
                if (param.args instanceof Array) {
                    m_okCall = new Handler(caller, okCall, param.args);
                }
                else {
                    m_okCall = new Handler(caller, okCall, [param.args]);
                }
                if (m_okCall.method != null) {
                    m_okCall.run();
                }
                return null;
            }
        }
        else if (param.todayTipKey && param.todayTipKey.length > 0) {
            if (TodayTipSetting.checkTodayTip(param.todayTipKey) == false) {
                let m_okCall;
                if (param.args instanceof Array) {
                    m_okCall = new Handler(caller, okCall, param.args);
                }
                else {
                    m_okCall = new Handler(caller, okCall, [param.args]);
                }
                if (m_okCall.method != null) {
                    m_okCall.run();
                }
                return null;
            }
        }
        let dialog = new CountryWarAlertDialog(param);
        dialog.open(false, param);
        return dialog;
    }
    //----------------------------------w3废弃--------------
    /**
     * 显示技能描述
     * @param skill_id 技能id
     * @param hero_type_id 英雄type_id
     * @param isOtherPlayerHero 技能id
     */
    // public static showSkillDescDialog(skill_id: number, hero_type_id: number = 0, hero_star: number = 0, hero_color: number = 0, isOtherPlayerHero: boolean = false, { isRideSkill = false, specitySkillLevel = 0 }: { isRideSkill?: boolean, specitySkillLevel?: number } = {}): void {
    //     if (ConfigManager.cfg_skillCache.has(skill_id)) {
    //         let obj: any = { skill_id: skill_id, hero_type_id: hero_type_id, hero_star: hero_star, hero_color: hero_color, isOtherPlayerHero: isOtherPlayerHero, isRideSkill: isRideSkill, specitySkillLevel: specitySkillLevel};
    //         DispatchManager.dispatchEvent(ModuleCommand.OPEN_SKILL_INFO_DIALOG, obj);
    //     }
    // }
    /**w3 新版 技能信息框 */
    static showSkillDetailDialog(vo) {
        DispatchManager.dispatchEvent("OPEN_SKILL_DETAIL_DIALOG" /* OPEN_SKILL_DETAIL_DIALOG */, vo);
    }
    /**
     * 快捷购买弹窗
     * @param type_id 道具type_id
     */
    static showQuickShopDialog(type_id) {
        //判断是否有快捷商店 有就弹快捷购买商店
        let quickShop = ConfigManager.cfg_shop_shortcutCache.get(type_id);
        if (quickShop) {
            DispatchManager.dispatchEvent("REQUEST_QUICK_SHOP" /* REQUEST_QUICK_SHOP */, quickShop.shop_id);
        }
        else {
            TipsUtil.ShowPathTypeIdToTips(type_id);
        }
    }
    static showFlyAttr(data, delay = 300) {
        if (data.listData.length > 0) {
            for (let vo of data.listData) {
                if (vo.val < 0) {
                    return;
                }
            }
        }
        TipsUtil.isAutoFlyPower = false;
        DispatchManager.dispatchEvent("OPEN_FLY_ATTRIBUTE_CHANGE_DIALOG" /* OPEN_FLY_ATTRIBUTE_CHANGE_DIALOG */, { data: data, delay: delay });
    }
    static showSuitDialog(suitId = 0, breed_pos = 0) {
        //DispatchManager.dispatchEvent(ModuleCommand.OPEN_EQUIP_SUIT_ACTIVATE_DIALOG, { suitId: FlyAttributeChangeDialog.suitId, breed_pos: FlyAttributeChangeDialog.breed_pos });
        TipsUtil.setPopUpWindows("OPEN_EQUIP_SUIT_ACTIVATE_DIALOG" /* OPEN_EQUIP_SUIT_ACTIVATE_DIALOG */, { suitId: suitId, breed_pos: breed_pos });
        FlyAttributeChangeDialog.suitId = 0;
        FlyAttributeChangeDialog.suitNum = 0;
        FlyAttributeChangeDialog.breed_pos = 0;
        if (FlyAttributeChangeDialog.carveGoods != null) {
            TipsUtil.setPopUpWindows("OPEN_FORGE_CARVE_JI_HUO_DIALOG" /* OPEN_FORGE_CARVE_JI_HUO_DIALOG */, FlyAttributeChangeDialog.carveGoods);
            TipsUtil.isAttrAutoFlyPower = false;
        }
    }
    static showFlyAttr2(type, newAttr, oldAttr, multiple = []) {
        let vo = new FlyFontVo();
        vo.type = type;
        vo.multiple = multiple;
        for (const newItem of newAttr) {
            let val = newItem.val;
            for (const oldItem of oldAttr) {
                if (newItem.code == oldItem.code) {
                    val = newItem.val - oldItem.val;
                }
            }
            vo.setAttrVO(newItem.code, val);
        }
    }
    /**战斗力变化显示 */
    static showFlyPower(delay = 500, { oldPower = 0, newPower = 0, coverBefore = true, isMasterCardPower = false, } = {}) {
        var _a;
        TipsUtil.isAutoFlyPower = true;
        TipsUtil.isAttrAutoFlyPower = true;
        // if (GuaJiDataCenter.isInGuaJiDialog && !isMasterCardPower) {
        //     return
        // }
        if (((_a = BaseDialog.getTopShowDialog()) === null || _a === void 0 ? void 0 : _a.name) == "GuaJiDialog" && !isMasterCardPower) {
            return;
        }
        if (oldPower == 0 && !isMasterCardPower) {
            oldPower = RoleDataCenter._oldPower;
        }
        if (oldPower == 0 && !isMasterCardPower) {
            return;
        }
        if (newPower == 0 && !isMasterCardPower) {
            newPower = DataCenter.user.attr.power;
        }
        if (oldPower == newPower) {
            return;
        }
        if (oldPower > newPower) {
            return;
        }
        let event = "OPEN_FLY_FIGHTING_CHANGE_DIALOG" /* OPEN_FLY_FIGHTING_CHANGE_DIALOG */;
        ILaya.timer.once(delay, this, DispatchManager.dispatchEvent, [event, { old: oldPower, new: newPower, isMasterCardPower: isMasterCardPower }], coverBefore);
    }
    /**延迟打开界面 */
    static onceOpenUI(eventName, data = null, delay = 0) {
        if (delay == 0) {
            if (data == null) {
                DispatchManager.dispatchEvent(eventName);
            }
            else {
                DispatchManager.dispatchEvent(eventName, data);
            }
            return;
        }
        ILaya.timer.once(delay, this, function () {
            if (data == null) {
                DispatchManager.dispatchEvent(eventName);
            }
            else {
                DispatchManager.dispatchEvent(eventName, data);
            }
        }, null, false);
    }
    /**
    * 获取是否有该弹窗在列表中
    */
    static HasPopUpWindow(eventName) {
        for (const item of TipsUtil.popUpWindowList) {
            //奖励弹窗允许多个存在
            if (item.eventName == eventName) {
                return true;
            }
        }
        return false;
    }
    static showVipDialog(type, txtMax, caller = null, callFun = null) {
        if (!GlobalConfig.showRecharge) {
            TipsUtil.showTips("次数不足");
        }
        else if (VipTeQuanUtil.isVipLevelAddMax(type))
            TipsUtil.showTips(txtMax);
        else {
            if (caller && callFun) {
                let call = Handler.create(caller, callFun, null, true);
                call.run();
            }
            let obj = VipTeQuanUtil.getVipTipsObj(type);
            let txt = "提升至VIP{1}级，" + obj.txt;
            if (type == VipTeQuanConst.RIDE_STAR_RATE) {
                txt += HtmlUtil.br() + ColorUtil.GetColorHtml("每日00:00重置VIP加成概率", "#d31616");
            }
            TipsUtil.showDialog(this, StringUtil.Format(txt, obj.count, obj.lv), "提升VIP", () => {
                // DispatchManager.dispatchEvent(ModuleCommand.OPEN_PAYMENT_DIALOG);
                GameUtil.gotoSystemPanel(PanelEventConstants.PAYMENT + "|" + PaymentVO.KEY_SVIP_SHOP + "|" + obj.lv);
            });
        }
    }
    static removePopUpWindowVO(eventName) {
        let list = TipsUtil.popUpWindowList;
        for (let index = list.length - 1; index >= 0; index--) {
            let vo = list[index];
            if (vo.eventName == eventName) {
                list.splice(index, 1);
            }
        }
    }
    /**
     * 活动结束提示弹窗
     * @param tipKey 活动唯一标识
     * @param endTime 结束时间
     * @returns
     */
    static showActTimeTipDialog(tipKey, endTime, is_limiting, { tipDesc = "" } = {}) {
        let t = endTime - DataCenter.serverTimeSeconds;
        if (t <= 0 || t > 2 * 24 * 60 * 60) { //只提示剩余时间小于等于2天以内的活动
            return;
        }
        tipKey = "act_time_tip@" + tipKey;
        if (!TodayTipSetting.checkTodayTip(tipKey)) { //当天提示过的 不再提示
            return;
        }
        let param = {
            tip_key: tipKey,
            endTime: endTime,
            tipDesc: tipDesc,
            is_limiting: is_limiting
        };
        // let tipDialog = new ActTimeTipDialog(param);
        // tipDialog.open(false,param);
        DispatchManager.dispatchEvent("OPEN_ACT_TIME_TIP_DIALOG" /* OPEN_ACT_TIME_TIP_DIALOG */, param);
        TodayTipSetting.setTodayTip(tipKey, false);
    }
    /**
 * 多窗口提示数组,必须要都在列表中,才会有顺序弹出效果
 * 通用提升弹窗（包含通用奖励获得，装备套装， 装备大师激活，英雄进阶，快速挂机功能等）
 */
    static setPopUpWindows(eventName, data = null, zOrder = 0, multiple = false) {
        for (const item of TipsUtil.popUpWindowList) {
            if (item.eventName == eventName) {
                //奖励弹窗允许多个存在
                if (!multiple && eventName != "OPEN_COMMON_REWARD_TIP_DIALOG" /* OPEN_COMMON_REWARD_TIP_DIALOG */) {
                    item.data = data;
                    return;
                }
            }
        }
        /**充值界面特别处理 */
        if (eventName == "OPEN_FIRST_RECHARGE_DIALOG" /* OPEN_FIRST_RECHARGE_DIALOG */) {
            if (LayerManager.maxZOrder > -1 || FightDataCenter.isInFight == true) {
                TipsUtil.firstEechargeEvent = "OPEN_FIRST_RECHARGE_DIALOG" /* OPEN_FIRST_RECHARGE_DIALOG */;
                return;
            }
        }
        let vo = new PopUpWindowVo();
        vo.eventName = eventName;
        vo.data = data;
        vo.zOrder = zOrder;
        TipsUtil.popUpWindowList.push(vo);
        this.showPopUpWindows();
    }
    static oncePopUpWindows(eventName, data = null, delay = 0) {
        ILaya.timer.once(delay, this, function () {
            TipsUtil.setPopUpWindows(eventName, data);
        }, null, false);
    }
    static sortPopWindow() {
        // let isNeedConcat: boolean = false;
        // let arr: PopUpWindowVo[] = [];
        // for (let i: number = 0; i < TipsUtil._popTimeOutList.length; i++) {
        //     let vo: PopUpWindowVo = TipsUtil._popTimeOutList[i];
        //     if (vo.eventName == ModuleCommand.OPEN_GUAJI_REWARD_FULL_TIP_DIALOG && i != TipsUtil._popTimeOutList.length - 1)//手动把挂机收益放到最后
        //     {
        //         arr = TipsUtil._popTimeOutList.splice(i, 1);
        //         isNeedConcat = true;
        //         break;
        //     }
        // }
        // if (isNeedConcat) {
        //     TipsUtil.popUpWindowList = TipsUtil.popUpWindowList.concat(arr);
        // }
        TipsUtil._popTimeOutList = ArrayUtil.sortArrayByKey(TipsUtil._popTimeOutList, "index");
    }
    /**用于处理进游戏得密集弹窗 先保存 3秒后统一排序处理 */
    static setPopUpWindowsTimeOut(eventName, index = 999, data = null, zOrder = 0, multiple = false) {
        let vo = new PopUpWindowVo();
        vo.eventName = eventName;
        vo.data = data;
        vo.zOrder = zOrder;
        vo.index = index;
        vo.multiple = multiple;
        TipsUtil._popTimeOutList.push(vo);
    }
    static ShowTimeOutWindow() {
        TipsUtil.sortPopWindow();
        for (const item of TipsUtil._popTimeOutList) {
            TipsUtil.setPopUpWindows(item.eventName, item.data, item.zOrder, item.multiple);
        }
        TipsUtil._popTimeOutList = [];
    }
    static showCommonAttrTips(attrVos, txtName = "") {
        DispatchManager.dispatchEvent("OPEN_COMMON_ATTR_TIPS_DIALOG" /* OPEN_COMMON_ATTR_TIPS_DIALOG */, { attrVos: attrVos, txtName: txtName });
    }
    /***充值界面特别处理 */
    static showfirstEecharge() {
        if (TipsUtil.firstEechargeEvent.length > 0) {
            let event = TipsUtil.firstEechargeEvent;
            TipsUtil.firstEechargeEvent = "";
            if (RechargeDataCenter.instance.hasReward(FirstPayType.first_pay) || DataCenter.vipLevel < 7) {
                TipsUtil.setPopUpWindows(event);
            }
        }
    }
    /**新窗口数据通过 setPopUpWindows 方法设置 */
    static showPopUpWindows() {
        let list = TipsUtil.popUpWindowList;
        list.sort((a, b) => a.zOrder - b.zOrder);
        for (const vo of list) {
            if (vo.isShow == true) {
                return;
            }
        }
        if (list.length > 0) {
            let vo = list[0];
            vo.isShow = true;
            DispatchManager.dispatchEvent(vo.eventName, vo.data);
        }
    }
    /**清理当前显示窗口的数据,并弹下一个窗口 */
    static clearPopUpWindows(name = null) {
        let list = TipsUtil.popUpWindowList;
        for (let index = list.length - 1; index >= 0; index--) {
            let vo = list[index];
            if (vo.isShow == true && name == null) {
                list.splice(index, 1);
            }
            else if (vo.eventName == name) {
                list.splice(index, 1);
                name = null;
            }
        }
        if (list.length > 0) {
            TipsUtil.showPopUpWindows();
        }
        else {
            TipsUtil.showFlyPower();
        }
    }
    /**
     * 重启游戏
     */
    static reloadGame(str = "服务器正在维护，请稍后再试") {
        if (GlobalConfig.isMiniGameAll) {
            if (Browser.onMiniGame && window["wx"].restartMiniProgram) {
                window["wx"].restartMiniProgram();
            }
            else if (Browser.onTTMiniGame && window["tt"].restartMiniProgramSync) {
                window["tt"].restartMiniProgramSync();
            }
            else if (Browser.onQQMiniGame && window["qq"].exitMiniProgram) {
                window["qq"].exitMiniProgram();
            }
            else {
                TipsUtil.showDialog(this, str, "", TipsUtil.reloadGame, { btnType: AlertDialog.OK });
            }
        }
        else if (Browser.onVVMiniGame || Browser.onKGMiniGame) {
            if (window["qg"].exitApplication) {
                window["qg"].exitApplication();
            }
            else {
                TipsUtil.showDialog(this, str, "", TipsUtil.reloadGame, { btnType: AlertDialog.OK });
            }
        }
        else if (Browser.onHWMiniGame || Browser.onQGMiniGame) { //华为快游戏
            window["qg"].exitApplication({
                success: function () {
                    console.log("exitApplication success");
                },
                fail: function () {
                    console.log("exitApplication fail");
                },
                complete: function () {
                    console.log("exitApplication complete");
                }
            });
        }
        else if (GlobalConfig.isUcMiniGame) {
            Browser.window.uc.exit();
        }
        else {
            Browser.window.location.reload();
        }
    }
}
TipsUtil.isShowAlertDialog = false;
//一个按钮都没有;
TipsUtil.TYPE_NONE = "none";
//默认显示关闭和确认按钮 
TipsUtil.TYPE_DEFAULT = "default";
//只显示确认
TipsUtil.TYPE_OK = "ok";
//只显示取消
TipsUtil.TYPE_CANCEL = "cancel";
/**战斗力变化自动飘战斗力 */
TipsUtil.isAutoFlyPower = true;
/*属性变化自动飘战斗力 */
TipsUtil.isAttrAutoFlyPower = true;
TipsUtil.popUpWindowList = [];
TipsUtil.firstEechargeEvent = "";
TipsUtil._popTimeOutList = [];
/**弹窗数据 */
class PopUpWindowVo {
    constructor() {
        /**事件名字 */
        this.eventName = "";
        this.zOrder = 0;
        /**true 在显示中 */
        this.isShow = false;
        /**显示优先级 */
        this.index = 1;
        /**是否唯一 */
        this.multiple = false;
    }
}
window["bundleTipsUtil"] = TipsUtil;
