{"x": 0, "type": "BaseDialog", "selectedBox": 2, "selecteID": 5, "searchKey": "BaseDialog", "props": {"width": 720, "sceneColor": "#000000", "height": 1280}, "nodeParent": -1, "maxID": 13, "label": "BaseDialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 15, "type": "Image", "searchKey": "Image,bg,bg", "props": {"y": -240, "var": "bg", "skin": "csclan/bg2.png", "right": 0, "name": "bg", "left": 0}, "nodeParent": 2, "label": "bg", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "searchKey": "Box,boxTab", "props": {"y": 302, "x": 33, "width": 660, "var": "boxTab", "height": 63}, "nodeParent": 2, "label": "boxTab", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"type": "HScrollBar", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,bar", "props": {"visible": false, "skin": "common/hscroll.png", "right": 0, "name": "bar", "left": 0, "bottom": 0}, "nodeParent": 3, "label": "bar", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$HIDDEN": true}]}, {"x": 15, "type": "Box", "searchKey": "Box,boxList", "props": {"y": 416, "x": 14, "width": 690, "var": "boxList", "height": 518}, "nodeParent": 2, "label": "boxList", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"type": "VScrollBar", "searchKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,bar", "props": {"visible": false, "top": 0, "skin": "common/vscroll.png", "right": 0, "name": "bar", "bottom": 0}, "nodeParent": 4, "label": "bar", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$HIDDEN": true}]}, {"x": 15, "type": "UIView", "source": "res/common/CommonCostItem1.scene", "searchKey": "UIView,itemCost", "props": {"y": 1125, "x": 542, "width": 151, "var": "itemCost"}, "nodeParent": 2, "label": "itemCost", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "searchKey": "<PERSON>ton,btnBonus,btnBonus", "props": {"zOrder": 99, "y": 1109.5, "x": 448, "var": "btnBonus", "stateNum": "1", "skin": "countryWar/benifit_icon.png", "name": "btnBonus"}, "nodeParent": 2, "label": "btnBonus", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 15, "type": "Image", "searchKey": "Image,imgBg", "props": {"width": 720, "var": "imgBg", "skin": "v2_mainui/zc_rk_di_1.png", "bottom": 0}, "nodeParent": 2, "label": "imgBg", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"type": "<PERSON><PERSON>", "searchKey": "Button,close", "props": {"y": 36, "x": 24, "stateNum": "1", "skin": "v2_common/btn_back.png", "name": "close", "bottom": 18}, "nodeParent": 11, "label": "close", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}], "$LOCKED": false, "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}