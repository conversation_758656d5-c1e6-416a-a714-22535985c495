import { UrlConfig } from "../../../../game/UrlConfig";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { cfg_world_secret_treasure_boss } from "../../../cfg/vo/cfg_world_secret_treasure_boss";
import { cfg_world_secret_treasure_mission } from "../../../cfg/vo/cfg_world_secret_treasure_mission";
import { p_simp_mission } from "../../../proto/common/p_simp_mission";
import { com } from "../../../ui/layaMaxUI";
import { GameUtil } from "../../../util/GameUtil";
import { HtmlUtil } from "../../../util/HtmlUtil";
import { StringUtil } from "../../../util/StringUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { DialogNavShow } from "../../BaseDialog";
import { ModuleCommand } from "../../ModuleCommand";
import { UIList } from "../../baseModules/UIList";
import TopInsAuto from "../../common/TopInsAuto";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import MissionConst, { MissionStatus } from "../../mission/MissionConst";
import { WorldTreasureDataCenter, WorldTreasureOpType } from "../data/WorldTreasureDataCenter";

export default class WorldTreasureTaskDialog extends com.ui.res.worldTreasure.WorldTreasureTaskDialogUI {

    constructor(){
        super();
        this.navShow = DialogNavShow.NONE;
    }
    private goodsList:UIList;
    private topPanel:TopInsAuto;
    private taskInfo:p_simp_mission;
    private missCfg:cfg_world_secret_treasure_mission;
    public initUI(): void {
        this.goodsList = UIList.SetUIList(this,this.goodBox,GoodsItem);
        this.goodsList.SetRepeat(6,1);
        this.goodsList.SetSpace(20,10);
        this.goodsList.isBoxCenter = true;

        this.topPanel = this.topIns;
        this.topPanel.setTitle("秘宝任务");
    }
    public onOpen(param: any): void {
        this.updateView();
    }
    public addEvent(): void {
        //任务更新
        this.addEventListener(ModuleCommand.UPDATE_WORLD_TREASURE_TASK_INFO,this,this.updateView);
        this.addEventListener(ModuleCommand.UPDATE_TREASURE_TASK_REMAIN_TIMES,this,this.refreshTreasureRemainTimes);
    }
    public addClick(): void {
        this.addOnClick(this,this.refreshBtn,this.onClickrefreshTask);
        this.addOnClick(this,this.goToBtn,this.goToLink);
        this.addOnClick(this,this.completionBtn,this.completionTask);
    }
    /**刷新任务 */
    onClickrefreshTask(){
        const costItem:number[] = MiscConstAuto.secret_treasure_mission_refresh_cost;
        const item_id = costItem[0];
        const item_price = costItem[1];
        let goldSkin = HtmlUtil.GetHtmlImg(UrlConfig.getGoodsIconByTypeId(item_id),50,50);
        const desc = StringUtil.FormatArr("是否花费{0}{1}刷新任务",[goldSkin,GameUtil.gold(item_price,item_id)]);
        TipsUtil.showDialog(this,desc,window.iLang.L2_TI_SHI.il(),()=>{
            WorldTreasureDataCenter.instance.m_world_secret_treasure_op_tos(WorldTreasureOpType.REFRESH_TASK);
        });
        
    }
    /**前往 */
    goToLink(){
        if (this.taskInfo && this.missCfg) {
            if (this.taskInfo.status == MissionStatus.PRE_ACCEPT) {
                this.goToBoss(this.missCfg.targetNpcId);
            } else if (WorldTreasureDataCenter.instance.checkMissionComplete()) {
                this.goToBoss(this.missCfg.completeNpcId);
            } else {
                if (this.missCfg.event_type == MissionConst.MISSION_TYPE_STAGE_COPY_MONSTER || this.missCfg.event_type == MissionConst.MISSION_TYPE_COLLECT_GOOD) {
                    this.goToBoss(this.missCfg.condition);
                } else if (this.missCfg.event_type == MissionConst.MISSION_TYPE_STAGE_COPY_TALK) {
                    this.goToBoss(this.missCfg.completeNpcId);
                }else{

                }
            }
            this.close();
        }
    }

    private goToBoss(id: number) {
        let bosscfg: cfg_world_secret_treasure_boss = CfgCacheMapMgr.cfg_world_secret_treasure_bossCache.get(id);
        if (!bosscfg) {
            bosscfg = CfgCacheMapMgr.cfg_world_secret_treasure_boss_mapCache.get(id);
        }
        if (!bosscfg) {
            return;
        }
        let itemcfg = CfgCacheMapMgr.cfg_map_itemCache.get(bosscfg.map_item_id);
        if (itemcfg) {
            this.dispatchEvent(ModuleCommand.GO_TO_TREASURE_TASK_TARGET_POING,itemcfg.pos);
        }
    }
    /**打开一键完成界面 */
    completionTask(){
        this.dispatchEvent(ModuleCommand.OPEN_WORLD_TREASURE_TASK_COMPLETE_BUY_DIALOG);
    }
    updateView(){
        this.taskInfo = WorldTreasureDataCenter.instance.taskMissionInfo;
        if (!this.taskInfo) {
            return;
        }
        this.missCfg = CfgCacheMapMgr.cfg_world_secret_treasure_missionCache.get(this.taskInfo.id);
        if (this.missCfg) {
            if (this.missCfg.event_type === MissionConst.MISSION_TYPE_COLLECT_GOOD) {
                this.lbTarget.text = StringUtil.FormatArr("【任务目标】：{0}({1}/{2})",[this.missCfg.event_name,this.taskInfo.cur_progress,this.missCfg.max_progress]);
            }
            else{
                this.lbTarget.text = StringUtil.FormatArr("【任务目标】：{0}",[this.missCfg.event_name]);
            }
            this.lbDesc.text = StringUtil.FormatArr("【任务描述】：{0}",[this.missCfg.event_desc]);
        }
        this.goodsList.array = GoodsVO.GetPItemToVos( this.taskInfo.rewards);
        this.refreshTreasureRemainTimes();
    }
    refreshTreasureRemainTimes(){
        const remainTimes = WorldTreasureDataCenter.instance.remainTimes;
        this.lbTimes.text = StringUtil.FormatArr("今日剩余次数：{0}",[remainTimes]);
    }
    public onClose(): void {
        // WorldTreasureDataCenter.instance.m_world_secret_treasure_op_tos(WorldTreasureOpType.EXIT_MAP);
    }
}