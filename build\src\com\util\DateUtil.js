import { ClassUtils } from "laya/utils/ClassUtils";
import { DataCenter } from "../modules/DataCenter";
import { StringUtil } from "./StringUtil";
import { MiscConst } from "../modules/misc_config/MiscConst";
import { Laya } from "Laya";
export class DateUtil {
    /**
     * 获取当前时间
     * @param addTime 增加的秒数
     * @return
     *
     */
    static getNowDate(addSeconds = 0) {
        return new Date((DataCenter.serverTimeSeconds + addSeconds) * 1000);
    }
    static getNowDateLocal(addSeconds = 0) {
        var now = window["W7_LOCAL_TIME"] ? window["W7_LOCAL_TIME"] : (Laya.Browser.now() / 1000);
        return new Date((now + addSeconds) * 1000);
    }
    /**
     * 返回两个时间的差(秒)
     * @param minuend  被减数
     * @param subtrahend 减数
     * @return 秒数
     *
     */
    static getSubTimeToSeconds(minuend, subtrahend) {
        return Math.floor((minuend.getTime() - subtrahend.getTime()) / 1000);
    }
    /**
     * 自动选择显示内容
     * @param count
     * @returns
     */
    static AutoChooseShowDHMS(count) {
        if (count > 0) {
            if (count > 86400) {
                return DateUtil.GetDH(count);
            }
            else {
                return DateUtil.GetHMS(count);
            }
        }
    }
    /**
     * 如果大于一小时,返回 00：00：00(时分秒), 否则且返回00：00(分秒);
     * @param count  时间秒
     * **/
    static GetHMSOrMS(count) {
        let ret = "";
        if (count > 3600) {
            ret = this.GetHMS(count);
        }
        else {
            ret = this.GetMS(count);
        }
        return ret;
    }
    /**返回小时，分钟，秒;
     * @param count  时间秒
     * @param format 时间格式
     * **/
    static GetHMS(count, format = DateUtil.TYPE_HMS_DEFAULT) {
        if (count < 0) {
            count = 0;
        }
        let h = Math.floor(count / 3600);
        let m = Math.floor(count % 3600 / 60);
        let s = Math.floor(count % 60);
        return StringUtil.Format(format, DateUtil.Fill(h), DateUtil.Fill(m), DateUtil.Fill(s));
    }
    /**返回分钟，秒;
    * @param count  时间秒
    * @param format 时间格式
    * **/
    static GetMS(count, format = DateUtil.TYPE_MS_DEFAULT) {
        if (count < 0) {
            count = 0;
        }
        let m = Math.floor(count % 3600 / 60);
        let s = Math.floor(count % 60);
        return StringUtil.Format(format, DateUtil.Fill(m), DateUtil.Fill(s));
    }
    /**返回分钟，秒，毫秒;
       * @param count  时间秒
       * @param format 时间格式
       * **/
    static GetMSS(count, format = DateUtil.TYPE_MSS_DEFAULT) {
        if (count < 0) {
            count = 0;
        }
        let m = Math.floor(count / 60000);
        let s = Math.floor((count % 60000) / 1000);
        let ms = Math.floor((count % 1000) / 10); // 计算毫秒部分，保留两位小数
        return StringUtil.Format(format, DateUtil.Fill(m), DateUtil.Fill(s), DateUtil.Fill(ms));
    }
    /**返回分钟，秒;
    * @param count  时间秒
    * @param format 时间格式
    * **/
    static GetMSCeil(count, format = DateUtil.TYPE_MS_DEFAULT) {
        if (count < 0) {
            count = 0;
        }
        let m = Math.floor(count % 3600 / 60);
        let s = Math.ceil(count % 60);
        return StringUtil.Format(format, DateUtil.Fill(m), DateUtil.Fill(s));
    }
    /**
     * 如果大于一天,返回 x天x时, 否则返回x时x分;
     * @param count  时间秒
     * @param format 时间格式
     * **/
    static GetDhOrHM(count, format1 = "{0}时{1}分", format2 = "{0}分{1}秒") {
        let ret = "";
        if (count > 86400) {
            ret = this.GetDH(count);
        }
        else if (3600 < count && count <= 86400) {
            ret = this.GetHM(count, format1);
        }
        else {
            ret = this.GetMS(count, format2);
        }
        return ret;
    }
    /**
     * 如果大于一天,返回 x天x时, 否则返回x时x分;
     * @param targetTime 时间秒
     * **/
    static getTimeRemaining(targetTime, format = DateUtil.TYPE_YMD_DEFAULT_2) {
        const now = DataCenter.serverTimeSeconds;
        const diffS = now - targetTime; // 时间差（秒）
        // 处理过去时间或无效值
        if (diffS <= 0)
            return "已结束";
        // 转换为分钟和小时
        const minutes = Math.floor(diffS / 60);
        const hours = Math.floor(minutes / 60);
        // 根据时间差返回不同格式
        if (minutes < 60) {
            return `${minutes}分钟前`; // 不足1小时
        }
        else if (hours < 24) {
            return `${hours}小时前`; // 不足24小时
        }
        else {
            // 超过24小时显示完整日期
            return this.GetYMD(targetTime * 1000, format);
        }
    }
    /**
     * 如果大于一天,返回 x天x时, 否则且大于一个小时返回x时x分,小于小时返回x分x秒;
     * @param count  时间秒
     * @param format 时间格式
     * **/
    static GetDhOrHMOrMS(count) {
        let ret = "";
        if (count > 86400) {
            ret = this.GetDH(count);
        }
        else if (count < 86400 && count > 3600) {
            ret = this.GetHM(count, "{0}时{1}分");
        }
        else {
            ret = this.GetMS(count, "{0}分{1}秒");
        }
        return ret;
    }
    /**返回小时，分钟;
     * @param count  时间秒
     * @param format 时间格式
     * **/
    static GetHM(count, format = DateUtil.TYPE_HM_DEFAULT) {
        if (count < 0) {
            count = 0;
        }
        let h = Math.floor(count / 3600);
        let m = Math.floor(count % 3600 / 60);
        return StringUtil.Format(format, DateUtil.Fill(h), DateUtil.Fill(m));
    }
    /**返回天时;
     * @param count  时间秒
     * @param format 时间格式
     * **/
    static GetDH(count, format = DateUtil.TYPE_DH_TYPE_1) {
        if (count < 0) {
            count = 0;
        }
        let d = Math.floor(count / 86400);
        let h = Math.floor((count % 86400) / 3600);
        return StringUtil.Format(format, d, h);
    }
    /**返回天,小时，分钟,秒;
     * @param count  时间秒
     * @param format 时间格式
     * **/
    static GetDHMS(count, format = "{0}时{1}分{2}秒") {
        if (count < 0) {
            count = 0;
        }
        let d = Math.floor(count / 86400);
        let h = Math.floor((count % 86400) / 3600);
        let m = Math.floor((count % 3600) / 60);
        let s = count % 60;
        let showTxt = "";
        if (d > 0) {
            showTxt += d + "天";
        }
        //if(h>0){
        //showTxt += DateUtil.Fill(h) + "时";
        //}
        //if(m>0){
        //showTxt += DateUtil.Fill(m) + "分";
        //}
        //if(s>0){
        //showTxt += DateUtil.Fill(s) + "秒";
        //}
        return showTxt + StringUtil.Format(format, DateUtil.Fill(h), DateUtil.Fill(m), DateUtil.Fill(s));
    }
    /**返回天时分或时分秒;
     * @param count  时间秒
     * **/
    static GetDHMorHMS(count, dhm = "{0}天{1}时{2}分", hms = "{0}时{1}分{2}秒") {
        if (count < 0) {
            count = 0;
        }
        let d = Math.floor(count / 86400);
        let h = Math.floor((count % 86400) / 3600);
        let m = Math.floor((count % 3600) / 60);
        let s = count % 60;
        if (count >= 86400) {
            return StringUtil.Format(dhm, DateUtil.Fill(d), DateUtil.Fill(h), DateUtil.Fill(m));
        }
        else {
            return StringUtil.Format(hms, DateUtil.Fill(h), DateUtil.Fill(m), DateUtil.Fill(s));
        }
    }
    /**返回天,小时，分钟,秒;
       * @param count  时间秒
       * @param format 时间格式
       * **/
    static GetDHM(count) {
        if (count < 0) {
            count = 0;
        }
        let d = Math.floor(count / 86400);
        let h = Math.floor((count % 86400) / 3600);
        let m = Math.floor((count % 3600) / 60);
        let s = count % 60;
        let showTxt = "";
        //if(d>0){
        showTxt += d + "天";
        //}
        //if(h>0){
        showTxt += h + "时";
        //}
        //if(m>0){
        showTxt += m + "分";
        return showTxt;
    }
    /**返回超短时间,如果大于一天,就返回天,如果小于一天就返回小时,如果小于小时就返回分,如果小于分,就返回秒
     * @param count  时间秒
     * @param format 时间格式
     * **/
    static GetSmallTime(count) {
        if (count < 0) {
            count = 0;
        }
        let d = Math.floor(count / 86400);
        let h = Math.floor((count % 86400) / 3600);
        let m = Math.floor((count % 3600) / 60);
        let s = count % 60;
        let showTxt = "";
        if (d > 0) {
            showTxt += d + "天";
        }
        else {
            if (h > 0) {
                showTxt = h + "时";
            }
            else {
                if (m > 0) {
                    showTxt = m + "分";
                }
                else {
                    if (s > 0) {
                        showTxt = s + "秒";
                    }
                    else {
                    }
                }
            }
        }
        return showTxt;
    }
    /**根据时间戳返回年,月,日
     * @param count 时间戳 毫秒(小于等于0 为当前时间)
     * @param format 时间格式
     * ***/
    static GetYMD(count = 0, format = DateUtil.TYPE_YMD_DEFAULT) {
        let date = DateUtil.GetDate(count);
        return StringUtil.Format(format, date.getFullYear().toString(), DateUtil.Fill(date.getMonth() + 1), DateUtil.Fill(date.getDate()));
    }
    /**根据时间戳返回月,日
     * @param count 时间戳 毫秒(小于等于0 为当前时间)
     * @param mdformat 时间格式
     * ***/
    static GetMD(count = 0, mdformat = DateUtil.TYPE_MD_DEFAULT) {
        mdformat = DateUtil.TYPE_MD_DEFAULT;
        let date = DateUtil.GetDate(count);
        return StringUtil.Format(mdformat, DateUtil.Fill(date.getMonth() + 1), DateUtil.Fill(date.getDate()));
    }
    /**根据时间戳返回 xx月xx日xx时xx分
    * @param count 时间戳 毫秒(小于等于0 为当前时间)
    * @param format 时间格式
    * ***/
    static GetMDHM(count = 0, format = DateUtil.TYPE_MDHM_DEFAULT) {
        let date = DateUtil.GetDate(count);
        return StringUtil.Format(format, [DateUtil.Fill(date.getMonth() + 1), DateUtil.Fill(date.getDate()), DateUtil.Fill(date.getHours()), DateUtil.Fill(date.getMinutes())]);
    }
    /** 根据时间戳返回 年,月,日,时 分 秒
     * @param count 时间戳 毫秒(小于等于0 为当前时间)
     * @param ymdFormat 年月日格式
     * @param hmsFormat 时分秒格式
     * **/
    static GetYMDHNS(count = 0, ymdFormat = DateUtil.TYPE_YMD_DEFAULT, hmsFormat = DateUtil.TYPE_HMS_DEFAULT) {
        let date = DateUtil.GetDate(count);
        //年月日
        let ymdStr = StringUtil.Format(ymdFormat, date.getFullYear().toString(), DateUtil.Fill(date.getMonth() + 1), DateUtil.Fill(date.getDate()));
        //时分秒
        let hms = StringUtil.Format(hmsFormat, DateUtil.Fill(date.getHours()), DateUtil.Fill(date.getMinutes()), DateUtil.Fill(date.getSeconds()));
        return ymdStr + " " + hms;
    }
    /** 根据时间戳返回 时 分
     * @param count 时间戳 毫秒(小于等于0 为当前时间)
     * @param hmsFormat 时分秒格式
     * **/
    static GetHN(count = 0, hmFormat = DateUtil.TYPE_MS_DEFAULT) {
        let date = DateUtil.GetDate(count);
        //时分
        let hm = StringUtil.Format(hmFormat, DateUtil.Fill(date.getHours()), DateUtil.Fill(date.getMinutes()));
        return hm;
    }
    /** 根据时间戳返回 年,月,日,时 分 秒
 * @param count 时间戳 毫秒(小于等于0 为当前时间)
 * @param ymdFormat 年月日格式
 * @param hmsFormat 时分秒格式
 * **/
    static GetHNS(count = 0, hmsFormat = DateUtil.TYPE_HMS_DEFAULT) {
        let date = DateUtil.GetDate(count);
        ;
        //时分秒
        let hms = StringUtil.Format(hmsFormat, DateUtil.Fill(date.getHours()), DateUtil.Fill(date.getMinutes()), DateUtil.Fill(date.getSeconds()));
        return hms;
    }
    /**
     * 根据开始和结束时间，返回时间段，年月日 时分秒 - 时分秒
     * @param s_time 开始时间戳
     * @param e_time 结束时间戳
     * @param timeFormat 时间格式
     * @returns 返回时间段
     */
    static GetTimeFrame(s_time = 0, e_time, timeFormat = DateUtil.TYPE_YMD_HMS_TYPE) {
        let s_date = DateUtil.GetDate(s_time);
        let e_date = DateUtil.GetDate(e_time);
        let arr = [
            //开始
            s_date.getFullYear().toString(),
            DateUtil.Fill(s_date.getMonth() + 1),
            DateUtil.Fill(s_date.getDate()),
            DateUtil.Fill(s_date.getHours()),
            DateUtil.Fill(s_date.getMinutes()),
            DateUtil.Fill(s_date.getSeconds()),
            //结束
            e_date.getFullYear().toString(),
            DateUtil.Fill(e_date.getMonth() + 1),
            DateUtil.Fill(e_date.getDate()),
            DateUtil.Fill(e_date.getHours()),
            DateUtil.Fill(e_date.getMinutes()),
            DateUtil.Fill(e_date.getSeconds()),
        ];
        return StringUtil.Format(timeFormat, arr);
    }
    /** 根据时间戳返回 月,日,时 分  eg:7-10 19:00
     * @param count 时间戳 毫秒(小于等于0 为当前时间)
     * @param ymdFormat 年月日格式
     * @param hmsFormat 时分秒格式
     * **/
    /*public static GetByRankMatch(count: number = 0): string {
        let date: Date = DateUtil.GetDate(count);
        //年月日
        let ymdStr: string = StringUtil.Format2("{0}-{1}", DateUtil.Fill(date.getMonth() + 1), DateUtil.Fill(date.getDate()));
        //时分秒
        let hms: string = StringUtil.Format2("{0}:{1}", DateUtil.Fill(date.getHours()), DateUtil.Fill(date.getMinutes()));

        return ymdStr + " " + hms;
    }*/
    /** 根据时间戳返回时间*/
    static GetDate(second) {
        let date;
        if (second <= 0) {
            date = new Date(DataCenter.serverTime);
        }
        else {
            date = new Date(second);
        }
        return date;
    }
    /**
     * 时间戳格式化
     * @param time 时间戳（秒）
     * @param format 格式   y=年；M=月；d=日；H=时；m=分；s=秒
     */
    // public static formatDate(time: number = 0, format: string = "yyyy-MM-dd HH:mm:ss"): string {
    //     if (time == 0) {
    //         time = DataCenter.serverTimeSeconds;
    //     }
    //     let date: Date = DateUtil.GetDate(time * 1000);
    //     let result: string = format;
    //     for (let i: number = 0, len: number = DateUtil.dateStrList.length; i < len; i++) {
    //         result = result.replace(DateUtil.dateStrList[i], DateUtil.getDateReplaceString(DateUtil.dateStrList[i], date));
    //     }
    //     return result;
    // }
    // private static dateStrList: string[] = ["yyyy", "MM", "dd", "HH", "mm", "ss", "yy", "M", "d", "H", "m", "s"];
    // private static getDateReplaceString(replaceText, date: Date): string {
    //     switch (replaceText) {
    //         case 'yyyy':
    //             return DateUtil.Fill(date.getFullYear());
    //         case 'MM':
    //             return DateUtil.Fill(date.getMonth() + 1);
    //         case 'mm':
    //             return DateUtil.Fill(date.getMinutes());
    //         case 'dd':
    //             return DateUtil.Fill(date.getDate());
    //         case 'HH':
    //             return DateUtil.Fill(date.getHours());
    //         case 'ss':
    //             return DateUtil.Fill(date.getSeconds());
    //         case 'yy':
    //             let yearStr: string = date.getFullYear().toString();
    //             return yearStr.substr(yearStr.length - 2, 2);
    //         case 'M':
    //             return date.getMonth() + 1 + "";
    //         case 'm':
    //             return date.getMinutes().toString();
    //         case 'd':
    //             return date.getDate().toString();
    //         case 'H':
    //             return date.getHours().toString();
    //         case 's':
    //             return date.getSeconds().toString();
    //     }
    //     return replaceText;
    // }
    /**获取当天开始的时间截 多少秒*/
    static getDateStartTime(totalSeconds = 0) {
        totalSeconds = totalSeconds > 0 ? totalSeconds : DataCenter.serverTimeSeconds;
        let nowDate = new Date(totalSeconds * 1000 + 1);
        nowDate.setHours(0, 0, 0, 0);
        return Math.ceil(nowDate.getTime() / 1000);
    }
    /**
     * 获取今天指定时间点的时间
     * timeStr 格式：18:30:00;
     * addTime 增加的秒数
     **/
    static GetTodayTime(timeStr, addTime = 0) {
        let arr = timeStr.split(":");
        let seconds = parseInt(arr[0]) * 3600 + parseInt(arr[1]) * 60 + parseInt(arr[2]) + addTime;
        let startCount = DateUtil.getDateStartTime(DataCenter.serverTimeSeconds);
        let time = startCount + seconds;
        ////console.lhtlog(GlobalManager.serverTimeSeconds);
        ////console.lhtlog(startCount);
        return DateUtil.GetDate(time * 1000);
    }
    static checkSummerMonth(date = null, summer_month = null) {
        if (!date) {
            date = DateUtil.getNowDate();
        }
        if (!summer_month) {
            summer_month = MiscConst.summer_month;
        }
        let month = parseInt(DateUtil.Fill(date.getMonth() + 1));
        if (month >= summer_month[0] && month <= summer_month[1]) {
            return true;
        }
        return false;
    }
    /**
     * 获取下一个时间戳
     * 如果今天没到这个时间,就返回 这个时间的时间戳
     * 否则,就取明天的 这个时间的时间戳
     *
     * timeStr格式：18:30:00;
     **/
    static GetNextTimestamp(timeStr) {
        let nextTimestamp = this.GetTodaySecondsTime(timeStr);
        //今天已经过了,取明天的
        if (nextTimestamp < DataCenter.serverTimeSeconds) {
            nextTimestamp += 86400;
        }
        return nextTimestamp;
    }
    /**
     * 获取指定时间点的秒
     * timeStr格式：18:30:00;
     **/
    static GetTodaySecondsTime(timeStr) {
        let arr = timeStr.split(":");
        let seconds = parseInt(arr[0]) * 3600 + parseInt(arr[1]) * 60 + parseInt(arr[2]);
        let startCount = DateUtil.getDateStartTime(DataCenter.serverTimeSeconds);
        let time = startCount + seconds;
        return time;
    }
    /**时间不足两位,补足两位**/
    static Fill(value) {
        return value < 10 ? "0" + value : value.toString();
    }
    /**当前月有多少天**/
    static GetMonthDay(year, month) {
        switch (month) {
            case 4:
            case 6:
            case 9:
            case 11: {
                return 30;
            }
            case 2: {
                let yes = DateUtil.isLeapYear(year);
                return yes == true ? 29 : 28;
            }
        }
        return 31;
    }
    /**是否闰年*/
    static isLeapYear(year) {
        if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
            return true;
        }
        return false;
    }
    /**day 1|2|3|4|5|6|7
     * **/
    static GetDayText(day) {
        let dayText = ["", "一", "二", "三", "四", "五", "六", "日"];
        let weeks = day.split('|');
        let str = "";
        let temp = [];
        weeks.forEach(value => {
            temp.push(dayText[value]);
        });
        return temp;
    }
    /**
     * 根据开服天数，获取结束时间，如 day = 8 返回第八天 结束的时间戳
     * @param day 开服第几天
     * return 返回结束时间戳（秒）
     */
    static getEndTimeByOpenServerDay(day) {
        let openServerDate = new Date(DataCenter.roleChoseInfo.opened_time * 1000);
        let openTime = (new Date(openServerDate.getFullYear(), openServerDate.getMonth(), openServerDate.getDate(), 0, 0, 0)).getTime();
        return openTime / 1000 + day * 24 * 60 * 60;
    }
    static getTargetTime(count) {
        let d = Math.floor(count / 86400);
        let h = Math.floor((count % 86400) / 3600);
        let m = Math.floor((count % 3600) / 60);
        // let s: number = count % 60;
        if (d >= 1)
            // return StringUtil.Format(d > 3 ? "离线3天以上" : "离线{0}天", d);
            return StringUtil.Format("{0}天", d);
        else if (h >= 1)
            return StringUtil.Format("{0}小时", h);
        else if (m >= 1)
            return StringUtil.Format("{0}分钟", m);
        else
            return "";
    }
    /** 获取今天星期几 */
    static getWeek() {
        let date = DateUtil.getNowDate();
        let week = date.getDay();
        week = week == 0 ? 7 : week;
        return week;
    }
    /**周几字符串 */
    static getWeekStr(week) {
        return week == 7 ? "周日" : "周" + StringUtil.numberToCN(week);
    }
    /**返回星期x 的开始时间戳（秒） */
    static getWeekToTime(week = 0, hour = 0, minutes = 0) {
        let curWeek = this.getWeek();
        /**还差多少天 */
        let manyDay = 0;
        if (week > curWeek) {
            manyDay = week - curWeek;
        }
        else {
            manyDay = 7 - curWeek + week;
        }
        let tarTime = DateUtil.getDateStartTime(DataCenter.serverTimeSeconds + (manyDay * 86400) + (hour * 3600) + (minutes * 60));
        return tarTime;
    }
    /**
     * 将“时分秒”转换成秒
     * timeStr格式：时:分:秒
     */
    static timeStrToSeconds(timeStr) {
        let toSeconds = DateUtil.ToSeconds;
        let totalSec = 0;
        let arr = timeStr.split(":").map(Number);
        for (let i = 0; i < arr.length; ++i) {
            let time = arr[i] || 0;
            let sec = toSeconds[i] || 0;
            totalSec += time * sec;
        }
        return totalSec;
    }
    /**
     * 将天数转换成秒
     */
    static dayToSeconds(day) {
        return day * 86400; //24 * 60 * 60;
    }
    /**
     * 将小时转换成秒
     */
    static hourToSeconds(hour) {
        return hour * 3600; //60 * 60;
    }
    /**
     * 通用活动倒计时显示
     * @param time 时间差
     * @returns
     */
    static getCommonActEndTime(time, format = null) {
        if (time <= 0)
            return "已结束";
        let str;
        if (time > 86400) {
            str = DateUtil.GetDH(time, format ? format : DateUtil.TYPE_DH_TYPE_1);
        }
        else {
            str = DateUtil.GetHMS(time, format ? format : DateUtil.TYPE_HMS_DEFAULT);
        }
        return str;
    }
    /**
     * 通用离线时间显示
     * @param offLineTick 离线时间
     * @returns
     */
    static _getTickStr(offLineTick) {
        let tickStr = "";
        let tick = DataCenter.serverTimeSeconds - offLineTick;
        if (tick >= 86400) {
            tickStr = DateUtil.GetDH(tick, "{0}天");
        }
        else if (tick >= 3600) {
            tickStr = DateUtil.GetDH(tick, "{1}小时");
        }
        else if (tick >= 45 * 60) {
            tickStr = "45分钟";
        }
        else if (tick >= 30 * 60) {
            tickStr = "30分钟";
        }
        else {
            tickStr = "刚离开";
        }
        return tickStr;
    }
    /**
    * 通用活动倒计时显示1
    * @param time 时间差
    * @returns
    */
    static getCommon1ActEndTime(time, format = null) {
        if (time <= 0)
            return "已结束";
        let str;
        if (time > 86400) {
            str = DateUtil.GetDH(time, format ? format : DateUtil.TYPE_DH_TYPE_1);
        }
        else if (time <= 86400 && time >= 3600) {
            str = DateUtil.GetHM(time, format ? format : DateUtil.TYPE_HM_TYPE_1);
        }
        else {
            str = DateUtil.GetMS(time, format ? format : DateUtil.TYPE_MS_TYPE_1);
        }
        return str;
    }
    /**获取设备与北京时区的时间差 */
    static getUTC8DeltaSec() {
        // 获取当前设备本地时间
        var localDate = new Date();
        // 获取设备当前时区相对于UTC时间的偏移量
        var localOffset = localDate.getTimezoneOffset();
        // 获取北京时间的时区偏移量
        var beijingOffset = -480; //分钟
        // 计算当前设备时区与北京时间之间的时间差 秒
        var offsetDiff = -(localOffset - beijingOffset) * 60;
        return offsetDiff;
    }
    /**
     * 将年、月、日转换为时间戳
     * @param year 年份
     * @param month 月份（1-12）
     * @param day 日期（1-31）
     * @returns 时间戳（毫秒）
     */
    static convertToTimestamp(year, month, day) {
        const date = new Date(year, month - 1, day);
        return Math.floor(date.getTime() / 1000);
    }
}
/**时分秒 00:00:00 */
DateUtil.TYPE_HMS_DEFAULT = "{0}:{1}:{2}";
/**分秒 00:00 */
DateUtil.TYPE_MS_DEFAULT = "{0}:{1}";
/**分秒毫秒 00:00.00 */
DateUtil.TYPE_MSS_DEFAULT = "{0}:{1}.{2}";
/**时分 00:00* */
DateUtil.TYPE_HM_DEFAULT = "{0}:{1}";
/** 年月日 0000年00月00日*/
DateUtil.TYPE_YMD_DEFAULT = "{0}年{1}月{2}日";
/** 年月日 0000·00·00*/
DateUtil.TYPE_YMD_DEFAULT_2 = "{0}·{1}·{2}";
/** 月日 00月00日*/
DateUtil.TYPE_MD_DEFAULT = "{0}月{1}日";
/** 月日时分 00月00日00时00分*/
DateUtil.TYPE_MDHM_DEFAULT = "{0}月{1}日{2}时{3}分";
/** 年月日 0000-00-00*/
DateUtil.TYPE_YMD_TYPE_1 = "{0}-{1}-{2}";
/**天时 */
DateUtil.TYPE_DH_TYPE_1 = "{0}天{1}时";
/**时分 */
DateUtil.TYPE_HM_TYPE_1 = "{0}时{1}分";
/**分秒 */
DateUtil.TYPE_MS_TYPE_1 = "{0}分{1}秒";
/** 年月日 时分秒 */
DateUtil.TYPE_YMD_HMS_TYPE = "{0}年{1}月{2}日 {3}时{4}分{5}秒 - {6}年{7}月{8}日 {9}时{10}分{11}秒";
/**
 * buff时间格式  xx小时xx分xx秒
 */
DateUtil.TYPE_HMS_BUFF = "{0}小时{1}分{2}秒";
//时分秒转秒
DateUtil.ToSeconds = [3600, 60, 1];
ClassUtils.regClass("com.util.DateUtil", DateUtil);
