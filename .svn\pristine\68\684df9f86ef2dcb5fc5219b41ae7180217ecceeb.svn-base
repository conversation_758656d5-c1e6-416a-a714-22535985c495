window.cclayadefine=function(){var _curweight,_utf8len,_scriptlist,_nextscriptlist,_absurl,_isDebug=!1,_isUserMode=!0,_enableDrawCell=!1,_updateUrl="//w6d.kkyou.cn/",_platName="",_cdnURL="//bsycdn.kkyou.cn/w6d/versions/trunk/bin/",_logURL="",_userInfo=null,_configurl=null,_extParam=null,_newest=!1,_gamever="53393",_libver="trunk",_publishtime=1753259233,_applibver="",_appchildver="-1",_sdkInstance=null,_noSleep=null,_timeId=null,_startPercent=0,_totalPercent=0,_jslen=2,_pctype=0,_jsWeights=[],_minModel=!1,_isVertical=!1,_isForceNoSdk=!1,_jsonphpparam="",_jsonjsparam="",_xmlparam="",_sceneid="",_cnameid="",_propertyobj="",_gameInfo=null,_appcall=!1,_globalobj={},str_arr_ok=!0,str_unit8_ok=!0,_enableguide=!0,_matrixy=0,_dicVals={key1:!1,key2:""},_device_cpid="",_statUrl="",_openStat="";return{getDeviceCPID:function(){return window.DEVICE_CPID||_device_cpid},setDeviceCPID:function(value){_device_cpid=value},getOpenStat:function(){return window.SERVER_CNF&&window.SERVER_CNF.OPEN_STAT?window.SERVER_CNF.OPEN_STAT:_openStat},setOpenStat:function(value){_openStat=value},getStatUrl:function(){return window.SERVER_CNF&&window.SERVER_CNF.STAT_URL?window.SERVER_CNF.STAT_URL:_statUrl},setStatUrl:function(value){_statUrl=value},setVal:function(key,val){_dicVals[key]=val},getVal:function(key){return _dicVals[key]},isUserMode:function(){return _isUserMode},isDebug:function(){return _isDebug},isEnableDrawCell:function(){return _enableDrawCell},getCdnUrl:function(){return _newest?_cdnURL.substring(0,_cdnURL.lastIndexOf("/bin"))+"/newest/bin/":_cdnURL},getUpdateUrl:function(){return _updateUrl},getpctype:function(){return _pctype},getPlatName:function(){return window.SERVER_CNF&&window.SERVER_CNF.PLAT_NAME?window.SERVER_CNF.PLAT_NAME:_platName},getUserInfo:function(){return _userInfo},getLogUrl:function(){return _logURL},getExtParam:function(){return _extParam},getGameVersion:function(){return _gamever},getLibVersion:function(){return _libver},getPublishTime:function(){return _publishtime},getenableguide:function(){return _enableguide},getAppLibVersion:function(){return _applibver},getAppChildVersion:function(){return _appchildver},getSdkInstance:function(){return _sdkInstance},getMinModel:function(){return _minModel},getIsVertical:function(){return _isVertical},isForceNoSdk:function(){return _isForceNoSdk},getJsonPhpParam:function(){return _jsonphpparam},getJsonJsParam:function(){return _jsonjsparam},getmatrixy:function(){return _matrixy},startPercent:function(){return _startPercent},getXmlParam:function(){return _xmlparam},getSceneId:function(){return _sceneid},getCnameId:function(){return _cnameid},getconfigurlTest:function(){return _configurl},getPropertyObj:function(){return _propertyobj},getSdkGameInfo:function(){return _gameInfo},getScriptlist:function(){return _scriptlist},getNextScriptlist:function(){return _nextscriptlist},appcall:function(){return _appcall},getglobalobj:function(key){return _globalobj[key]},getAbsUrl:function(){return _newest?_absurl.substring(0,_absurl.lastIndexOf("/bin"))+"/newest/bin/":_absurl},setSdkGameInfo:function(v){_gameInfo=v},setglobalobj:function(key,v){_globalobj[key]=v},setCnameId:function(v){_cnameid=v},setmatrixy:function(v){_matrixy=v},setSceneId:function(v){_sceneid=v},setDebug:function(v){_isDebug=v},setMode:function(v){_isUserMode=v},setenableguide:function(v){_enableguide=v},setEnableDrawCell:function(v){_enableDrawCell=v},setNewEst:function(v){_newest=v},setCdnUrl:function(v){var index=v.indexOf("/appres");0<index&&(v=v.substring(0,index+1)),_cdnURL=v},setUpdateUrl:function(v){_updateUrl=v},setpctype:function(v){_pctype=v},setappcall:function(v){_appcall=v},setUserInfo:function(v){_userInfo=v},setPlatName:function(v){_platName=v},setLogUrl:function(v){_logURL=v},setExtParam:function(v){_extParam=v},setGameVersion:function(v){_gamever=v},setLibVersion:function(v){_libver=v},setPublishTime:function(v){_publishtime=v},setAppLibVersion:function(v){_applibver=v},setAppChildVersion:function(v){_appchildver=v},setSdkInstance:function(v){_sdkInstance=v},setMinModel:function(v){_minModel=v},setIsVertical:function(v){_isVertical=v},setIsForceNoSdk:function(v){_isForceNoSdk=v},setJsonPhpParam:function(v){_jsonphpparam=v},setJsonJsParam:function(v){_jsonjsparam=v},setXmlParam:function(v){_xmlparam=v},setScriptlist:function(v){_scriptlist=v},setNextScriptlist:function(value){_nextscriptlist=value},setconfigurlTest:function(v){_configurl=v},setAbsUrl:function(v){_absurl=v},clearsomething:function(){_propertyobj=null},xxxooo:function(){window.Laya.DialogManager.prototype._closeOnSide=function(){for(var i=this.numChildren-1;-1<i;i--){var dialog=this.getChildAt(i);if(dialog instanceof window.Laya.Dialog)return void dialog.close(window.Laya.Dialog.CLOSE)}}},wxrequest:function(applibversion,platname,dataparam,cdnurl){for(var plain=new Zlib.Inflate(new Uint8Array(wx.getFileSystemManager().readFileSync("config.bin"))).decompress(),dataString="",i=0;i<7;i++)dataString+=String.fromCharCode(plain[i]);for(var offset=parseInt(dataString),dataString="",i=7;i<offset;i++)dataString+=String.fromCharCode(plain[i]);this.setIncludeObj(JSON.parse(dataString)),dataString="";for(i=offset;i<plain.length;i++)dataString+=String.fromCharCode(plain[i]);_propertyobj=JSON.parse(dataString),this.setAppLibVersion(applibversion);dataparam={v:window.cclayadefine.getAppLibVersion(),w:dataparam};cdnurl&&(dataparam.cdnurl=cdnurl),wx.request({url:"https://w6d.kkyou.cn/"+platname+"/android/version.php",data:dataparam,header:{"content-type":"text/plain",charset:"utf-8"},success:function(res){window.cclayadefine.setXmlParam(res.data),wx.loadSubpackage({name:"core",success:function(res){},fail:function(res){}})},fail:function(e){console.log("加载平台信息失败",e)}}),wx.onShow(function(option){var cname=decodeURIComponent(option.query.scene);window.cclayadefine.setSceneId(cname);cname=decodeURIComponent(option.query.cname);window.cclayadefine.setCnameId(cname),console.log("option",option)})},enableNoSleep:function(){_noSleep||(_noSleep=new NoSleep,document.addEventListener("click",function enableNoSleep(){_noSleep.enable(),document.removeEventListener("click",enableNoSleep,!1)},!1))},enableTimer:function(evalue){_timeId=evalue?_timeId||window.setInterval(window.cclayadefine.updateTimer,20):(_timeId&&window.clearInterval(_timeId),null)},nextJs:function(leftlen){_curweight=_jsWeights[_jslen-leftlen-1],_jslen-leftlen-1==0?_totalPercent=_curweight:_totalPercent+=_curweight},updatePercent:function(per){},updateTimer:function(){window.loadingView&&_startPercent<=99&&(_startPercent+=5)<_totalPercent&&window.loadingView.loading(_startPercent)},init:function(){try{String.fromCharCode.apply(null,[0])}catch(__){str_arr_ok=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(__){str_unit8_ok=!1}_utf8len=new("undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array?Uint8Array:Array)(256);for(var q=0;q<256;q++)_utf8len[q]=252<=q?6:248<=q?5:240<=q?4:224<=q?3:192<=q?2:1;_utf8len[254]=_utf8len[254]=1;var total=20,single=total/(_jslen=_scriptlist?_scriptlist.length:2);_jsWeights.push(80);for(var i=0;i<_jslen-1;i++)i==_jslen-2?_jsWeights.push(total):_jsWeights.push(single),total-=single;_startPercent=10,_totalPercent=_jsWeights[0],window.loadingView&&window.loadingView.loading(_startPercent)},developerMode:function(urls,callback,param,force){var url=urls.shift();window.cclayadefine.nextJs(urls.length),url=window.cclayadefine.replaceurl(url);var url=window.cclayadefine.conchurl(url),xmlHttp=window.cclayadefine.getXmlHttp();xmlHttp.open("GET",url,!0),window.cclayadefine.reportStat(url,"start"),xmlHttp.send(),xmlHttp.onreadystatechange=function(){var script;4==xmlHttp.readyState&&((script=document.createElement("script")).text=xmlHttp.responseText,document.getElementsByTagName("head")[0].appendChild(script),window.cclayadefine.reportStat(url,"finish"),0<urls.length?window.cclayadefine.developerMode(urls,callback,param,force):callback&&callback.apply(null,param))}},shrinkBuf:function(buf,size){return buf.length===size?buf:buf.subarray?buf.subarray(0,size):(buf.length=size,buf)},buf2binstring:function(buf,len){if(len<65534&&(buf.subarray&&str_unit8_ok||!buf.subarray&&str_arr_ok))return String.fromCharCode.apply(null,window.cclayadefine.shrinkBuf(buf,len));for(var result="",i=0;i<len;i++)result+=String.fromCharCode(buf[i]);return result},buf2string:function(buf,max){for(var c,c_len,len=max||buf.length,utf16buf=new Array(2*len),out=0,i=0;i<len;)if((c=buf[i++])<128)utf16buf[out++]=c;else if(4<(c_len=_utf8len[c]))utf16buf[out++]=65533,i+=c_len-1;else{for(c&=2===c_len?31:3===c_len?15:7;1<c_len&&i<len;)c=c<<6|63&buf[i++],c_len--;1<c_len?utf16buf[out++]=65533:c<65536?utf16buf[out++]=c:(c-=65536,utf16buf[out++]=55296|c>>10&1023,utf16buf[out++]=56320|1023&c)}return window.cclayadefine.buf2binstring(utf16buf,out)},parseXMLFromString:function(rst){if(rst=rst.replace(/>\s+</g,"><"),-1<(rst=(new DOMParser).parseFromString(rst,"text/xml")).firstChild.textContent.indexOf("This page contains the following errors"))throw new Error(rst.firstChild.firstChild.textContent);return rst},getXmlHttp:function(){return window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP")},replaceurl:function(url){var xmldoc;return window.conch&&(window.cclayadefine.appcall()||(window.cclayadefine.setappcall(!0),"Conch-android"==window.conch.config.getOS()?(xmldoc=window.PlatformClass.createClass("layaair.autoupdateversion.AutoUpdateAPK"),window.cclayadefine.setCdnUrl(xmldoc.call("getProperty","cdnurl"))):("iPhone10,3"!==(xmldoc=JSON.parse(window.conch.config.getDeviceInfo())).devicename&&"iPhone10,6"!==xmldoc.devicename&&"iPhone11,2"!==xmldoc.devicename&&"iPhone11,4"!==xmldoc.devicename&&"iPhone11,6"!==xmldoc.devicename&&"iPhone11,8"!==xmldoc.devicename&&"iPhone12,1"!==xmldoc.devicename&&"iPhone12,3"!==xmldoc.devicename&&"iPhone12,5"!==xmldoc.devicename||window.cclayadefine.setmatrixy(100),"string"==typeof(xmldoc=window.conch.ccversionxml)&&(xmldoc=window.cclayadefine.parseXMLFromString(xmldoc)),window.cclayadefine.setCdnUrl(xmldoc.getElementsByTagName("cdnurl")[0].firstChild.nodeValue)))),window.cclayadefine.getCdnUrl()?window.cclayadefine.getCdnUrl()+url:window.cclayadefine.getAbsUrl()?window.cclayadefine.getAbsUrl()+url:url},conchurl:function(url){if(window.conch){var endindex=url.indexOf("?v=");if(0<endindex)return url.substring(0,endindex)}return url},userMode:function(urls,callback,param,force){var url=urls.shift();window.cclayadefine.nextJs(urls.length),url=window.cclayadefine.replaceurl(url);var url=window.cclayadefine.conchurl(url),xmlHttp=window.cclayadefine.getXmlHttp();xmlHttp.open("GET",url,!0),window.cclayadefine.reportStat(url,"start"),xmlHttp.send(),xmlHttp.onreadystatechange=function(){var script;4==xmlHttp.readyState&&((script=document.createElement("script")).text=xmlHttp.responseText,document.getElementsByTagName("head")[0].appendChild(script),window.cclayadefine.reportStat(url,"finish"),0<urls.length?window.cclayadefine.userMode(urls,callback,param,force):callback&&callback.apply(null,param))}},reportStat:function(jsFileName,statType){var androidCall,iosCall,php_url,params,xhr_post,state;"false"!=window.cclayadefine.getOpenStat()&&((androidCall=window.localStorage.getItem("PhpStatUtil_finishStat"))&&"true"==androidCall||(window.conch&&("Conch-android"==window.conch.config.getOS()?(androidCall=window.PlatformClass.createClass("layaair.autoupdateversion.AutoUpdateAPK"))&&((iosCall=androidCall.call("getProperty","devicecpid"))&&window.cclayadefine.setDeviceCPID(iosCall),(iosCall=androidCall.call("getProperty","staturl"))&&window.cclayadefine.setStatUrl(iosCall),(iosCall=androidCall.call("getProperty","gameplatform"))&&window.cclayadefine.setPlatName(iosCall)):(iosCall=window.PlatformClass.createClass("S3LuaObjectCBridg"))&&(iosCall.callWithBack(function(msg){window.cclayadefine.setDeviceCPID(msg)},"getDeviceCPID"),iosCall.callWithBack(function(msg){window.cclayadefine.setStatUrl(msg)},"getStatURL"),"string"==typeof(state=window.conch.ccversionxml)&&(state=window.cclayadefine.parseXMLFromString(state)),window.cclayadefine.setPlatName(state.getElementsByTagName("gameplatform")[0].firstChild.nodeValue))),state="",state="start"==statType?"start_":"finish_",console.log("jsFileName="+jsFileName),-1!=jsFileName.indexOf("laya.")&&(state+="layajs"),-1!=jsFileName.indexOf("js/bundle.")&&(state+="bundlejs"),""!=state&&(php_url=window.cclayadefine.getStatUrl()+"stat/beforeReg",params="device_cpid="+window.cclayadefine.getDeviceCPID()+"&platform_id="+window.cclayadefine.getPlatName()+"&channel_name="+window.cclayadefine.getPlatName()+"&state="+state,console.log("h5_act2Reg--\x3e"+php_url+"--\x3e>"+params),(xhr_post=new XMLHttpRequest).open("POST",php_url,!0),xhr_post.setRequestHeader("Content-type","application/x-www-form-urlencoded"),xhr_post.addEventListener("load",function(){console.log("h5_act2Reg response"+JSON.stringify(xhr_post.response)+"php_url:"+php_url+"params:"+params)}),xhr_post.send(params))))},openapk:function(androidDownUrl){setTimeout(function(){window.location=androidDownUrl},500)},openApp:function(iphoneSchema,iphoneDownUrl,androidSchema,androidDownUrl){if(this.isWeixin())$(".weixin-tip").css("height",$(window).height()),$(".weixin-tip").show(),$(".weixin-tip").on("touchstart",function(){$(".weixin-tip").hide()});else if(navigator.userAgent.match(/(iPhone|iPod|iPad);?/i)){var loadDateTime=new Date;window.setTimeout(function(){new Date-loadDateTime<5e3?window.location=iphoneDownUrl:window.close()},25),window.location=iphoneSchema}else if(navigator.userAgent.match(/android/i))try{window.location=androidSchema,setTimeout(function(){window.location=androidDownUrl},500)}catch(e){}},isWeixin:function(){return"micromessenger"==navigator.userAgent.toLowerCase().match(/MicroMessenger/i)}}}(),window.cclayadefine.setScriptlist(["libs/spine-core-3.8.min.js?v=143052397af2fc1be3f8179b8d297243","laya.ss.js?v=eb56942e3a91e74ec18656fdbba98ec1","js/preload.ss.js?v=fad173e04b976075d95ab112baf01876"]),window.cclayadefine.setNextScriptlist(["js/bundle.ss.js?v=816a023ebcc10b1d727fb48d16d6d7f4"]),window.cclayadefine.init(),window.cclayadefine.setAbsUrl("//bsycdn.kkyou.cn/w6d/versions/trunk/bin/");