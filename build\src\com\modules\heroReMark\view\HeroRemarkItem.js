import { com } from "../../../ui/layaMaxUI";
import { HeroRemarkDataCenter } from "../data/HeroRemarkDataCenter";
import { heroCommentOpType } from "../data/HeroRemarkConst";
import { DataCenter } from "../../DataCenter";
import { StringUtil } from "../../../util/StringUtil";
export default class HeroRemarkItem extends com.ui.res.heroRemark.HeroRemarkItemUI {
    constructor() {
        super();
    }
    UpdateItem(itemData, select) {
        this.info = itemData.data;
        this.hero_id = itemData.parameter;
        let isMyComment = this.info.role_id == DataCenter.myRoleID;
        this.playerName.color = isMyComment ? "#e25b18" : "#855033";
        let player = StringUtil.Format("s{0}.{1}", this.info.server_id, this.info.role_name);
        let playerNameDesc = isMyComment ? player + " 【我的】" : player;
        this.playerName.text = playerNameDesc;
        this.descTxt.text = this.info.content;
        switch (this.info.state) {
            case 0:
                //未操作
                this.likeBtn.skin = "heroRemark/reyi_8a.png";
                this.noLikeBtn.skin = "heroRemark/reyi_7a.png";
                break;
            case 1:
                //点赞
                this.likeBtn.skin = "heroRemark/reyi_8.png";
                this.noLikeBtn.skin = "heroRemark/reyi_7a.png";
                break;
            case 2:
                //点踩
                this.likeBtn.skin = "heroRemark/reyi_8a.png";
                this.noLikeBtn.skin = "heroRemark/reyi_7.png";
                break;
            default:
                break;
        }
        this.lbLike.text = this.info.like_num.toString();
        this.lbNoLike.text = this.info.dislike_num.toString();
    }
    /**添加事件**/
    addClick() {
        this.addOnClick(this, this.noLikeBtn, this.OnSelectNoLikeBtn);
        this.addOnClick(this, this.likeBtn, this.OnSelectLikeBtn);
    }
    OnSelectLikeBtn() {
        if (this.info.state == 1) {
            //取消点赞
            HeroRemarkDataCenter.instance.m_hero_comment_op_tos(heroCommentOpType.CANCEL_LIKE, this.hero_id, this.info.id);
        }
        else {
            //点赞
            HeroRemarkDataCenter.instance.m_hero_comment_op_tos(heroCommentOpType.LIKE, this.hero_id, this.info.id, true);
        }
    }
    OnSelectNoLikeBtn() {
        if (this.info.state == 2) {
            //取消点踩
            HeroRemarkDataCenter.instance.m_hero_comment_op_tos(heroCommentOpType.CANCEL_LIKE, this.hero_id, this.info.id);
        }
        else {
            //点踩
            HeroRemarkDataCenter.instance.m_hero_comment_op_tos(heroCommentOpType.LIKE, this.hero_id, this.info.id, false);
        }
    }
    Clean() {
    }
    set isSelect(value) {
    }
}
