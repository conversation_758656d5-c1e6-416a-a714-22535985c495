/**
 * 基础使用示例
 * 展示如何使用这个战斗框架
 */

import { Vector3, Damage, DamageInfoTag } from "../types/GameTypes";
import { ChaProperty, ChaControlState, ChaResource } from "../character/ChaProperty";
import { BuffModel, BuffObj, AddBuffInfo } from "../character/Buff";
import { ChaState } from "../gameobjects/Character";
import { BulletModel, BulletLauncher } from "../gameobjects/Bullet";
import { DamageInfo } from "../battle/DamageInfo";
import { BuffManager } from "../managers/BuffManager";
import { DamageManager } from "../managers/DamageManager";

/**
 * 基础使用示例类
 */
export class BasicUsageExample {

    /**
     * 创建一个基础角色
     */
    public static createBasicCharacter(name: string): any {
        // 创建角色属性
        const property = new ChaProperty(
            100,  // moveSpeed
            1000, // hp
            30,   // ammo
            50,   // attack
            100,  // actionSpeed
            0.5,  // bodyRadius
            0.6,  // hitRadius
        );

        // 创建角色控制状态
        const controlState = new ChaControlState(true, true, true);

        // 创建角色资源
        const resource = new ChaResource(1000, 30, 100);

        // 创建游戏对象 (简化版本)
        const gameObject: any = {
            id: `character_${Date.now()}_${Math.random()}`,
            name: name,
            active: true,
            transform: {
                position: Vector3.zero,
                rotation: 0,
                scale: Vector3.one,
                translate: function(delta: Vector3) {
                    this.position = this.position.add(delta);
                },
                rotate: function(angle: number) {
                    this.rotation += angle;
                }
            },
            components: new Map(),
            getComponent: function<T>(type: new() => T): T | null {
                const typeName = type.name;
                return this.components.get(typeName) as T || null;
            },
            addComponent: function<T>(component: T): T {
                const typeName = (component as any).constructor.name;
                (component as any).gameObject = this;
                this.components.set(typeName, component);
                return component;
            },
            removeComponent: function<T>(type: new() => T): boolean {
                const typeName = type.name;
                return this.components.delete(typeName);
            },
            destroy: function() {
                this.active = false;
            }
        };

        // 添加角色状态组件
        const chaState = new ChaState(property, controlState, resource);
        gameObject.addComponent(chaState);

        return gameObject;
    }

    /**
     * 创建一个基础Buff - 攻击力提升
     */
    public static createAttackBoostBuff(): BuffModel {
        const buffModel = new BuffModel(
            "attack_boost",
            "攻击力提升",
            ["buff", "attack"],
            100, // priority
            5,   // maxStack
            0,   // tickTime (不需要周期执行)
            new ChaControlState(true, true, true)
        );

        // 设置属性修正 - 攻击力+20 (加法)
        buffModel.propMod[0] = new ChaProperty(0, 0, 0, 20, 0, 0, 0);

        // 设置回调函数
        buffModel.setCallback("onOccur", (buff: BuffObj, modifyStack: number) => {
            console.log(`${buff.carrier.name} 获得了攻击力提升Buff，层数：${buff.stack}`);
        });

        buffModel.setCallback("onRemoved", (buff: BuffObj) => {
            console.log(`${buff.carrier.name} 失去了攻击力提升Buff`);
        });

        return buffModel;
    }

    /**
     * 创建一个基础子弹
     */
    public static createBasicBullet(): BulletModel {
        const bulletModel = new BulletModel(
            "basic_bullet",
            "基础子弹",
            new Damage(100, 0, 0), // 100点子弹伤害
            500, // 速度
            3.0, // 生存时间3秒
            0.1, // 10%暴击率
            0,   // 不贯穿
            0.1, // 碰撞半径
            [DamageInfoTag.DirectDamage]
        );

        // 设置回调函数
        bulletModel.onCreate = (bullet) => {
            console.log(`子弹 ${bullet.name} 被创建`);
        };

        bulletModel.onHit = (bullet, target) => {
            console.log(`子弹 ${bullet.name} 命中了 ${target.name}`);
        };

        bulletModel.onRemoved = (bullet) => {
            console.log(`子弹 ${bullet.name} 被销毁`);
        };

        return bulletModel;
    }

    /**
     * 运行基础示例
     */
    public static runBasicExample(): void {
        console.log("=== 开始基础战斗框架示例 ===");

        // 初始化管理器
        BuffManager.instance.initialize();

        // 创建两个角色
        const player = this.createBasicCharacter("玩家");
        const enemy = this.createBasicCharacter("敌人");

        // 设置位置
        player.transform.position = new Vector3(0, 0, 0);
        enemy.transform.position = new Vector3(10, 0, 0);

        console.log("创建了两个角色：", player.name, enemy.name);

        // 给玩家添加攻击力提升Buff
        const attackBoostBuff = this.createAttackBoostBuff();
        const addBuffInfo = new AddBuffInfo(
            attackBoostBuff,
            null, // 没有施法者
            player,
            3,    // 3层
            10.0  // 持续10秒
        );

        BuffManager.instance.addBuff(addBuffInfo);

        // 检查玩家的最终属性
        const playerState = player.getComponent(ChaState);
        const finalProperty = playerState.getFinalProperty();
        console.log(`玩家最终攻击力: ${finalProperty.attack}`); // 应该是 50 + 20*3 = 110

        // 创建子弹并发射
        const bulletModel = this.createBasicBullet();
        const direction = new Vector3(1, 0, 0); // 向右发射

        const bullet = BulletLauncher.launch(
            bulletModel,
            player,
            player.transform.position,
            direction,
            enemy
        );

        console.log("玩家发射了一颗子弹");

        // 模拟直接命中 (因为碰撞检测还没实现)
        const damageInfo = new DamageInfo(
            player,
            enemy,
            bulletModel.damage,
            0, // 角度
            bulletModel.criticalRate,
            bulletModel.damageTags
        );

        console.log("模拟子弹命中敌人");
        DamageManager.instance.addDamage(damageInfo);

        // 检查敌人的血量
        const enemyState = enemy.getComponent(ChaState);
        console.log(`敌人剩余血量: ${enemyState.resource.hp}`);

        console.log("=== 基础示例结束 ===");
    }
}

// 如果直接运行此文件，执行示例
if (typeof window === 'undefined') {
    // Node.js环境
    BasicUsageExample.runBasicExample();
}