/**
 * 游戏核心类型定义
 * 基于Unity战斗框架的TypeScript重写版本
 */

// 基础游戏对象接口 (对应Unity的GameObject)
export interface GameObject {
    id: string;
    name: string;
    active: boolean;
    transform: Transform;
    components: Map<string, Component>;

    getComponent<T extends Component>(type: new() => T): T | null;
    addComponent<T extends Component>(component: T): T;
    removeComponent<T extends Component>(type: new() => T): boolean;
    destroy(): void;
}

// 变换组件 (对应Unity的Transform)
export interface Transform {
    position: Vector3;
    rotation: number; // 2D游戏只需要Y轴旋转
    scale: Vector3;

    translate(delta: Vector3): void;
    rotate(angle: number): void;
}

// 基础组件接口 (对应Unity的Component)
export class Component {
    gameObject: GameObject;
    enabled: boolean;

    awake?(): void;
    start?(): void;
    update?(deltaTime: number): void;
    fixedUpdate?(fixedDeltaTime: number): void;
    destroy?(): void;
}

// 3D向量
export class Vector3 {
    public x: number;
    public y: number;
    public z: number;

    constructor(x: number = 0, y: number = 0, z: number = 0) {
        this.x = x;
        this.y = y;
        this.z = z;
    }

    static get zero(): Vector3 { return new Vector3(0, 0, 0); }
    static get one(): Vector3 { return new Vector3(1, 1, 1); }
    static get forward(): Vector3 { return new Vector3(0, 0, 1); }

    add(other: Vector3): Vector3 {
        return new Vector3(this.x + other.x, this.y + other.y, this.z + other.z);
    }

    multiply(scalar: number): Vector3 {
        return new Vector3(this.x * scalar, this.y * scalar, this.z * scalar);
    }

    magnitude(): number {
        return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
    }

    normalized(): Vector3 {
        const mag = this.magnitude();
        if (mag === 0) return Vector3.zero;
        return new Vector3(this.x / mag, this.y / mag, this.z / mag);
    }
}

// 移动类型枚举
export enum MoveType {
    Ground = 0,  // 地面移动
    Fly = 1      // 飞行移动
}

// 伤害信息标签枚举
export enum DamageInfoTag {
    DirectDamage = 0,    // 直接伤害
    PeriodDamage = 1,    // 间歇性伤害
    ReflectDamage = 2,   // 反弹伤害
    DirectHeal = 10,     // 直接治疗
    PeriodHeal = 11,     // 间歇性治疗
    MonkeyDamage = 9999  // 扩展用伤害类型
}

// 多属性伤害结构
export class Damage {
    public bullet: number;    // 子弹伤害
    public explosion: number; // 爆破伤害
    public mental: number;    // 精神伤害

    constructor(bullet: number = 0, explosion: number = 0, mental: number = 0) {
        this.bullet = bullet;
        this.explosion = explosion;
        this.mental = mental;
    }

    // 计算总伤害值
    overall(asHeal: boolean = false): number {
        if (!asHeal) {
            return Math.max(0, this.bullet) + Math.max(0, this.explosion) + Math.max(0, this.mental);
        } else {
            return Math.min(0, this.bullet) + Math.min(0, this.explosion) + Math.min(0, this.mental);
        }
    }

    // 伤害相加
    add(other: Damage): Damage {
        return new Damage(
            this.bullet + other.bullet,
            this.explosion + other.explosion,
            this.mental + other.mental
        );
    }

    // 伤害乘法
    multiply(scalar: number): Damage {
        return new Damage(
            Math.round(this.bullet * scalar),
            Math.round(this.explosion * scalar),
            Math.round(this.mental * scalar)
        );
    }
}

// 回调函数类型定义
export type BuffOnOccur = (buff: BuffObj, modifyStack: number) => void;
export type BuffOnRemoved = (buff: BuffObj) => void;
export type BuffOnTick = (buff: BuffObj) => void;
export type BuffOnHit = (buff: BuffObj, damageInfo: DamageInfo, target: GameObject) => void;
export type BuffOnBeHurt = (buff: BuffObj, damageInfo: DamageInfo, attacker: GameObject) => void;
export type BuffOnKill = (buff: BuffObj, damageInfo: DamageInfo, target: GameObject) => void;
export type BuffOnBeKilled = (buff: BuffObj, damageInfo: DamageInfo, attacker: GameObject) => void;
export type BuffOnCast = (buff: BuffObj, skill: SkillObj, timeline: TimelineObj) => TimelineObj;

// 子弹回调函数类型
export type BulletOnCreate = (bullet: GameObject) => void;
export type BulletOnHit = (bullet: GameObject, target: GameObject) => void;
export type BulletOnRemoved = (bullet: GameObject) => void;
export type BulletTween = (t: number, bullet: GameObject, target: GameObject | null) => Vector3;

// AoE回调函数类型
export type AoeOnCreate = (aoe: GameObject) => void;
export type AoeOnRemoved = (aoe: GameObject) => void;
export type AoeOnTick = (aoe: GameObject) => void;
export type AoeOnCharacterEnter = (aoe: GameObject, characters: GameObject[]) => void;
export type AoeOnCharacterLeave = (aoe: GameObject, characters: GameObject[]) => void;
export type AoeOnBulletEnter = (aoe: GameObject, bullets: GameObject[]) => void;
export type AoeOnBulletLeave = (aoe: GameObject, bullets: GameObject[]) => void;

// 时间轴回调函数类型
export type TimelineEvent = (timeline: TimelineObj, ...args: any[]) => void;

// 前置声明，具体实现在各自的文件中
export interface DamageInfo {}
export interface BuffObj {}
export interface SkillObj {}
export interface TimelineObj {}
export interface AddBuffInfo {}
export interface ChaProperty {}
export interface ChaControlState {}
export interface ChaResource {}