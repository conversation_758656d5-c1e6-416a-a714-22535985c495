//数据不要用静态类型的
//可以在本模块引用，不可跨模块引用
//本模块引用的时候不要缓存instance，每次通过instance获取即可
import { MatchConst } from "../../../auto/ConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { Connection } from "../../../net/Connection";
import { m_lord_treasure_op_tos } from "../../../proto/line/m_lord_treasure_op_tos";
import { GameUtil } from "../../../util/GameUtil";
import { LordDataCenter } from "../../Lord/data/LordDataCenter";
import { MockPVPDataCenter } from "../../mockPVP/data/MockPVPDataCenter";
/**领主装备菜单 */
export var ELordTreasureMenuType;
(function (ELordTreasureMenuType) {
    /**宝物背包 */
    ELordTreasureMenuType[ELordTreasureMenuType["MENU_BAG"] = 1] = "MENU_BAG";
    /**宝物打造 */
    ELordTreasureMenuType[ELordTreasureMenuType["MENU_MAKE"] = 2] = "MENU_MAKE";
})(ELordTreasureMenuType || (ELordTreasureMenuType = {}));
export var ELordTreasureOpType;
(function (ELordTreasureOpType) {
    /**打造宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_MAKE"] = 1] = "OP_MAKE";
    /**强化宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_STREANGTH"] = 2] = "OP_STREANGTH";
    /**锻造宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_FORGE"] = 3] = "OP_FORGE";
    /**重置宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_REMAKE"] = 4] = "OP_REMAKE";
    /**装备宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_LOAD"] = 5] = "OP_LOAD";
    /**卸下宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_UNLOAD"] = 6] = "OP_UNLOAD";
    /**分享宝物 */
    ELordTreasureOpType[ELordTreasureOpType["OP_SHARE"] = 7] = "OP_SHARE";
})(ELordTreasureOpType || (ELordTreasureOpType = {}));
export class LordTreasureDataCenter {
    static get instance() {
        if (LordTreasureDataCenter._instance == null) {
            LordTreasureDataCenter._instance = new LordTreasureDataCenter();
        }
        return LordTreasureDataCenter._instance;
    }
    reset() {
        LordTreasureDataCenter._instance = null;
    }
    get allLordTreasureList() {
        return LordDataCenter.instance.info.lord_treasure_list;
    }
    getLordTreasureById(id) {
        return this.allLordTreasureList.find(v => v.id == id);
    }
    /**
     * 宝物排列顺序：已穿戴>等级（从高到低）>可锻造次数（从少到多）>宝物ID（从大到小）
     * @param a
     * @param b
     * @param groupType
     * @param matchType
     * @returns
     */
    sortLordTreasure(a, b, matchType = MatchConst.MATCH_TYPE_MAIN_BATTLE) {
        var _a, _b;
        let indexA = (_a = this.getGroupTypeInfoBy(a.id, matchType)) === null || _a === void 0 ? void 0 : _a.index;
        let indexB = (_b = this.getGroupTypeInfoBy(b.id, matchType)) === null || _b === void 0 ? void 0 : _b.index;
        // 先判断是否在队伍中
        if (!indexA && indexB)
            return 1;
        if (!indexB && indexA)
            return -1;
        // 如果都在队伍中，比较 index（从小到大）
        if (indexA && indexB) {
            if (indexA !== indexB) {
                return indexA - indexB; // 小的排前面
            }
        }
        if (a.level != b.level) {
            return b.level - a.level;
        }
        if (a.forge_times != b.forge_times) {
            return a.forge_times - b.forge_times;
        }
        return b.id - a.id;
    }
    /**最大宝物强化等级 */
    getMaxWorkLevel(typeId) {
        let maxLevel = 0;
        CfgCacheMapMgr.cfg_lord_treasure_levelBytreasure_idCache.get(typeId).forEach(v => {
            if (v.level > maxLevel) {
                maxLevel = v.level;
            }
        });
        return maxLevel;
    }
    /**是否达到最大宝物强化等级 */
    getIsMaxWorkLevel(level, typeId) {
        const maxLevel = this.getMaxWorkLevel(typeId);
        return level >= maxLevel;
    }
    /**宝物的最大锻造等级 */
    getMaxForgeLevel(entryId) {
        let maxLevel = 0;
        CfgCacheMapMgr.cfg_lord_treasure_entryByEntry_idCache.get(entryId).forEach(v => {
            if (v.entry_level > maxLevel) {
                maxLevel = v.entry_level;
            }
        });
        return maxLevel;
    }
    /**是否达到最大宝物锻造次数
     * @param times 宝物当前锻造次数
     * @param typeId 宝物类型ID
     * @returns
    */
    isMaxForgeTimes(times, typeId) {
        const maxTimes = CfgCacheMapMgr.cfg_lord_treasureCache.get(typeId).forge_max_times;
        return times >= maxTimes;
    }
    /**最大宝物锻造次数
         * @param times 宝物当前锻造次数
         * @param typeId 宝物类型ID
         * @returns
        */
    getMaxForgeTimes(typeId) {
        const maxTimes = CfgCacheMapMgr.cfg_lord_treasureCache.get(typeId).forge_max_times;
        return maxTimes;
    }
    /**通过treasureID获取主副队信息 */
    getGroupTypeInfoBy(treasureID, matchType) {
        if (LordDataCenter.instance.lordLineUpMapList.has(matchType)) {
            for (const [index, item] of LordDataCenter.instance.lordLineUpMapList.get(matchType).entries()) {
                if (item.lord_treasure == treasureID) {
                    return item;
                }
            }
        }
        else {
            return null;
        }
    }
    /** 获得所有的未穿戴宝物（除了模拟对战 */
    getAllIdleTreasure() {
        let loadList = [];
        LordDataCenter.instance.lordLineUpMapList.forEach((v1, k1) => {
            v1.forEach((v2, k2) => {
                if ((v2.lord_treasure > 0 && loadList.indexOf(v2.lord_treasure) == -1) ||
                    (v2.lord_treasure > 0 && k1 == MatchConst.MATCH_TYPE_MOCK_BATTLE)) { //选出穿戴的或模拟对战的
                    loadList.push(v2.lord_treasure);
                }
            });
        });
        let idleList = [];
        this.allLordTreasureList.forEach((v, k) => {
            if (loadList.indexOf(v.id) == -1) {
                idleList.push(v);
            }
        });
        return idleList;
    }
    /** 获得所有的宝物（除了模拟对战 */
    getAllTreasure() {
        let loadList = [];
        LordDataCenter.instance.lordLineUpMapList.forEach((v1, k1) => {
            v1.forEach((v2, k2) => {
                if (v2.lord_treasure > 0 && k1 == MatchConst.MATCH_TYPE_MOCK_BATTLE) { //选出穿戴的或模拟对战的
                    loadList.push(v2.lord_treasure);
                }
            });
        });
        let idleList = [];
        this.allLordTreasureList.forEach((v, k) => {
            if (loadList.indexOf(v.id) == -1) {
                idleList.push(v);
            }
        });
        console.log("idleList", idleList);
        return idleList;
    }
    /**通过matchType、index获取主副队的宝物信息 */
    getGroupTreasureInfo(matchType, index) {
        let info = LordDataCenter.instance.getGroupTypeInfoByIndex(index, matchType);
        if (info) {
            let groupTreasureInfo;
            if (matchType == MatchConst.MATCH_TYPE_MOCK_BATTLE) {
                //模拟对战读取模拟对战的领主宝物库
                groupTreasureInfo = MockPVPDataCenter.instance.getMockPVPLordTreasureById(info.lord_treasure);
            }
            else {
                groupTreasureInfo = this.getLordTreasureById(info.lord_treasure);
            }
            return groupTreasureInfo;
        }
        return null;
    }
    /**获取领主宝物总强化效果 */
    getAllTreasureEntry(type_id, treasureLevel = 1) {
        let treasureEntryList = [];
        let cfgTreasure = CfgCacheMapMgr.cfg_lord_treasureCache.get(type_id);
        let treasureEntry = cfgTreasure.treasure_entry.split("|");
        let cfgLevel = CfgCacheMapMgr.cfg_lord_treasure_levelCache.m_get(type_id, treasureLevel);
        let treasureEntryLevelList = GameUtil.parseCfgByField(cfgLevel, "treasure_entry_", { ingoreEmpty: true });
        treasureEntry.forEach(v => {
            let entry_id = Number(v);
            let _level = 1;
            let unlockLevel = this.getUnlockLevel(type_id, entry_id);
            let _treasureEntry = treasureEntryLevelList.find(v => { var _a; return Number((_a = v[0]) === null || _a === void 0 ? void 0 : _a.split("|")[0]) == entry_id; });
            if (_treasureEntry) {
                _level = Number(_treasureEntry[0].split("|")[1]);
            }
            let confEntry = CfgCacheMapMgr.cfg_lord_treasure_entryCache.m_get(entry_id, _level);
            let tmp = { confLevel: cfgLevel, confEntry: confEntry, unlockLevel: unlockLevel };
            treasureEntryList.push(tmp);
        });
        // console.log("all_treasureEntryList", treasureEntryList);
        return treasureEntryList;
    }
    getUnlockLevel(type_id, entryId) {
        let unlockLevel = 0;
        let unlockLevelMap = new Map();
        let newTreasureEntry = 0;
        CfgCacheMapMgr.cfg_lord_treasure_levelBytreasure_idCache.get(type_id).forEach(v => {
            let _treasure_entry = GameUtil.parseCfgByField(v, "treasure_entry_", { ingoreEmpty: true });
            if (_treasure_entry.length > newTreasureEntry) { //有新词条时
                let entryInfo = _treasure_entry[newTreasureEntry][0];
                let entry_id = entryInfo.split("|")[0];
                let entry_level = entryInfo.split("|")[1];
                unlockLevelMap.set(Number(entry_id), Number(v.level));
                newTreasureEntry++;
            }
        });
        if (unlockLevelMap.has(entryId)) {
            unlockLevel = unlockLevelMap.get(entryId);
        }
        return unlockLevel;
    }
    /**获取对应等级的领主宝物强化效果
     * @param type_id 宝物ID
     * @param treasureLevel 宝物等级
     * @param isShowNew 是否显示新词条
     * @param isShowChange 是否显示词条变化
     * @returns 对应等级的领主宝物强化效果
    */
    getTreasureEntry(type_id, treasureLevel, isShowNew = false, isShowChange = false) {
        let treasureEntryList = [];
        let cfgLevel = CfgCacheMapMgr.cfg_lord_treasure_levelCache.m_get(type_id, treasureLevel);
        if (!cfgLevel) {
            console.error("cfgLevel is null", { type_id: type_id, level: treasureLevel });
            return;
        }
        let _treasureEntry = GameUtil.parseCfgByField(cfgLevel, "treasure_entry_", { ingoreEmpty: true });
        let lastIndexes = [];
        if (isShowNew) {
            let oriCfgLevel = CfgCacheMapMgr.cfg_lord_treasure_levelCache.m_get(type_id, treasureLevel - 1);
            if (!oriCfgLevel) {
                return;
            }
            let oriTreasureEntry = GameUtil.parseCfgByField(oriCfgLevel, "treasure_entry_", { ingoreEmpty: true });
            if (_treasureEntry.length > oriTreasureEntry.length) { //有新词条时，新词条加上去
                let newNum = _treasureEntry.length - oriTreasureEntry.length;
                lastIndexes = Array.from({ length: newNum }, (_, i) => _treasureEntry.length - newNum + i);
            }
        }
        // console.log("_treasureEntry", _treasureEntry)
        for (let i = 0; i < _treasureEntry.length; i++) {
            let isNew = false;
            let isChange = false;
            let entryInfo = _treasureEntry[i][0];
            let entry_id = entryInfo.split("|")[0];
            let entry_level = entryInfo.split("|")[1];
            let confEntry = CfgCacheMapMgr.cfg_lord_treasure_entryCache.m_get(entry_id, entry_level);
            if (isShowNew) {
                isNew = lastIndexes.indexOf(i) > -1;
            }
            if (isShowChange) {
                let oriCfgLevel = CfgCacheMapMgr.cfg_lord_treasure_levelCache.m_get(type_id, treasureLevel - 1);
                if (oriCfgLevel) {
                    let oriTreasureEntry = oriCfgLevel["treasure_entry_" + (i + 1)];
                    if (oriTreasureEntry) {
                        isChange = oriTreasureEntry != entryInfo;
                    }
                    else {
                        isChange = true;
                    }
                }
            }
            let tmp = { confLevel: cfgLevel, confEntry: confEntry, isNew: isNew, isChange: isChange };
            treasureEntryList.push(tmp);
        }
        // console.log("treasureEntryList", treasureEntryList);
        return treasureEntryList;
    }
    //--------------协议发送------------------------
    /**
     * 发送领主宝物操作协议
     * @param op_type - 宝物操作类型，参考 ELordTreasureOpType 枚举
     * @param type_id - 宝物类型ID，默认为 0
     * @param treasure_id - 宝物ID，默认为 0
     * @param match_type - 战斗类型，默认为主战斗类型（MatchConst.MATCH_TYPE_MAIN_BATTLE）
     * @param index - 领主分组类型，默认为主分组（ELordGroupType.TYPE_MAIN）
     * @param lord_id - 领主ID，默认为 0
     */
    m_lord_treasure_op_tos(op_type, type_id = 0, treasure_id = 0, match_type = 0, index = 0, lord_id = 0, param_list = []) {
        let tos = new m_lord_treasure_op_tos();
        tos.op_type = op_type;
        tos.type_id = type_id;
        tos.treasure_id = treasure_id;
        tos.match_type = match_type;
        tos.index = index;
        tos.lord_id = lord_id;
        tos.param_list = param_list;
        Connection.instance.sendMessage(tos);
    }
}
LordTreasureDataCenter._instance = null;
;
