; FBX 6.1.0 project file
; Created by Blender FBX Exporter
; for support mail: <EMAIL>
; ----------------------------------------------------

FBXHeaderExtension:  {
    FBXHeaderVersion: 1003
    FBXVersion: 6100
    CreationTimeStamp:  {
        Version: 1000
        Year: 2011
        Month: 03
        Day: 16
        Hour: 12
        Minute: 48
        Second: 14
        Millisecond: 0
    }
    Creator: "FBX SDK/FBX Plugins build 20070228"
    OtherFlags:  {
        FlagPLE: 0
    }
}
CreationTime: "2011-03-16 12:48:14:000"
Creator: "Blender version 2.56 (sub 0)"

; Object definitions
;------------------------------------------------------------------

Definitions:  {
    Version: 100
    Count: 14
    ObjectType: "Model" {
        Count: 10
    }
    ObjectType: "Geometry" {
        Count: 1
    }
    ObjectType: "Material" {
        Count: 1
    }
    ObjectType: "Texture" {
        Count: 1
    }
    ObjectType: "Video" {
        Count: 1
    }
    ObjectType: "Pose" {
        Count: 1
    }
    ObjectType: "GlobalSettings" {
        Count: 1
    }
}

; Object properties
;------------------------------------------------------------------

Objects:  {
    Model: "Model::Camera Switcher", "CameraSwitcher" {
        Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
            Property: "Color", "Color", "A",0.8,0.8,0.8
            Property: "Camera Index", "Integer", "A+",100
        }
        MultiLayer: 0
        MultiTake: 1
        Hidden: "True"
        Shading: W
        Culling: "CullingOff"
        Version: 101
        Name: "Model::Camera Switcher"
        CameraId: 0
        CameraName: 100
        CameraIndexName:
    }
	Model: "Model::blend_root", "Null" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
        }
        MultiLayer: 0
        MultiTake: 1
        Shading: Y
        Culling: "CullingOff"
        TypeFlags: "Null"
    }
	Model: "Model::SpikeBall", "Mesh" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",-90.000009334538021,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
			Property: "Color", "Color", "A",0.8,0.8,0.8
			Property: "Size", "double", "",100
			Property: "Look", "enum", "",1
		}
		MultiLayer: 0
		MultiTake: 1
		Shading: Y
		Culling: "CullingOff"
		Vertices: 0.000000,0.000000,0.573238,0.000000,0.000000,-0.573238,0.000000,0.219369,0.529603,0.000000,0.219369,-0.529603,0.000000,-0.219369,0.529603,0.000000,-0.219369,-0.529603,0.000000,0.405341,0.405341
		,0.000000,0.405341,-0.405341,0.000000,-0.405341,0.405341,0.000000,-0.405341,-0.405341,0.000000,0.529603,0.219369,0.000000,0.529603,-0.219369,0.000000,-0.529603,0.219369,0.000000,-0.529603,-0.219369
		,0.000000,0.573238,0.000000,0.000000,-0.573238,0.000000,-0.219369,0.529603,0.000000,-0.219369,-0.529603,0.000000,0.219369,0.529603,0.000000,0.219369,-0.529603,0.000000,-0.202670,0.489290,0.219369
		,-0.202670,0.489290,-0.219369,-0.202670,-0.489290,0.219369,-0.202670,-0.489290,-0.219369,0.202670,0.489290,0.219369,0.202670,0.489290,-0.219369,0.202670,-0.489290,0.219369,0.202670,-0.489290,-0.219369
		,-0.155117,0.374486,0.405341,-0.155117,0.374486,-0.405341,-0.155117,-0.374486,0.405341,-0.155117,-0.374486,-0.405341,0.155117,0.374486,0.405341,0.155117,0.374486,-0.405341,0.155117,-0.374486,0.405341
		,0.155117,-0.374486,-0.405341,-0.083949,0.202670,0.529603,-0.083949,0.202670,-0.529603,-0.083949,-0.202670,0.529603,-0.083949,-0.202670,-0.529603,0.083949,0.202670,0.529603,0.083949,0.202670,-0.529603
		,0.083949,-0.202670,0.529603,0.083949,-0.202670,-0.529603,-0.155117,0.155117,0.529603,-0.155117,0.155117,-0.529603,-0.155117,-0.155117,0.529603,-0.155117,-0.155117,-0.529603,0.155117,0.155117,0.529603
		,0.155117,0.155117,-0.529603,0.155117,-0.155117,0.529603,0.155117,-0.155117,-0.529603,-0.286619,0.286619,0.405341,-0.286619,0.286619,-0.405341,-0.286619,-0.286619,0.405341,-0.286619,-0.286619,-0.405341
		,0.286619,0.286619,0.405341,0.286619,0.286619,-0.405341,0.286619,-0.286619,0.405341,0.286619,-0.286619,-0.405341,-0.374486,0.374486,0.219369,-0.374486,0.374486,-0.219369,-0.374486,-0.374486,0.219369
		,-0.374486,-0.374486,-0.219369,0.374486,0.374486,0.219369,0.374486,0.374486,-0.219369,0.374486,-0.374486,0.219369,0.374486,-0.374486,-0.219369,-0.405341,0.405341,0.000000,-0.405341,-0.405341,0.000000
		,0.405341,0.405341,0.000000,0.405341,-0.405341,0.000000,-0.529603,0.219369,0.000000,-0.529603,-0.219369,0.000000,0.529603,0.219369,0.000000,0.529603,-0.219369,0.000000,-0.489290,0.202670,0.219369
		,-0.489290,0.202670,-0.219369,-0.489290,-0.202670,0.219369,-0.489290,-0.202670,-0.219369,0.489290,0.202670,0.219369,0.489290,0.202670,-0.219369,0.489290,-0.202670,0.219369,0.489290,-0.202670,-0.219369
		,-0.374486,0.155117,0.405341,-0.374486,0.155117,-0.405341,-0.374486,-0.155117,0.405341,-0.374486,-0.155117,-0.405341,0.374486,0.155117,0.405341,0.374486,0.155117,-0.405341,0.374486,-0.155117,0.405341
		,0.374486,-0.155117,-0.405341,-0.202670,0.083949,0.529603,-0.202670,0.083949,-0.529603,-0.202670,-0.083949,0.529603,-0.202670,-0.083949,-0.529603,0.202670,0.083949,0.529603,0.202670,0.083949,-0.529603
		,0.202670,-0.083949,0.529603,0.202670,-0.083949,-0.529603,-0.219369,0.000000,0.529603,-0.219369,0.000000,-0.529603,0.219369,0.000000,0.529603,0.219369,0.000000,-0.529603,-0.405341,0.000000,0.405341
		,-0.405341,0.000000,-0.405341,0.405341,0.000000,0.405341,0.405341,0.000000,-0.405341,-0.529603,0.000000,0.219369,-0.529603,0.000000,-0.219369,0.529603,0.000000,0.219369,0.529603,0.000000,-0.219369
		,-0.573238,0.000000,0.000000,0.573238,0.000000,0.000000,0.000000,0.184799,0.502268,0.000000,0.184799,-0.502268,0.000000,-0.184799,0.502268,0.000000,-0.184799,-0.502268,-0.160041,0.092400,0.502268
		,-0.160041,-0.092400,-0.502268,0.160041,-0.092400,0.502268,0.160041,-0.092400,-0.502268,0.000000,0.502268,0.184799,0.000000,0.502268,-0.184799,0.000000,-0.502268,0.184799,0.000000,-0.502268,-0.184799
		,-0.160041,0.502268,-0.092399,-0.160041,-0.502268,-0.092399,0.160041,0.502268,-0.092399,0.160041,-0.502268,-0.092399,0.000000,0.708711,0.708710,0.000000,0.708711,-0.708710,0.000000,-0.708711,0.708710
		,0.000000,-0.708711,-0.708710,-0.160041,0.420494,0.289821,-0.160041,0.420494,-0.289821,-0.160041,-0.420494,0.289821,-0.160041,-0.420494,-0.289821,0.160041,0.420494,0.289821,0.160041,0.420494,-0.289821
		,0.160041,-0.420494,0.289821,0.160041,-0.420494,-0.289821,-0.160041,0.289821,0.420494,-0.160041,0.289821,-0.420494,-0.160041,-0.289821,0.420494,-0.160041,-0.289821,-0.420494,0.160041,0.289821,0.420494
		,0.160041,0.289821,-0.420494,0.160041,-0.289821,0.420494,0.160041,-0.289821,-0.420494,0.000000,0.224485,0.485830,0.000000,0.224485,-0.485830,0.000000,-0.224485,0.485830,0.000000,-0.224485,-0.485830
		,0.000000,0.485830,0.224484,0.000000,0.485830,-0.224484,0.000000,-0.485830,0.224484,0.000000,-0.485830,-0.224484,-0.502268,0.184799,0.000000,-0.502268,-0.184799,0.000000,0.502268,0.184799,0.000000
		,0.502268,-0.184799,0.000000,-0.502268,0.092400,0.160041,-0.502268,0.092400,-0.160041,0.502268,0.092400,0.160041,0.502268,0.092400,-0.160041,-0.246140,0.418513,0.225135,-0.246140,0.418513,-0.225135
		,-0.246140,-0.418513,0.225135,-0.246140,-0.418513,-0.225135,0.246140,0.418513,0.225135,0.246140,0.418513,-0.225135,0.246140,-0.418513,0.225135,0.246140,-0.418513,-0.225135,-0.449403,0.112796,0.267839
		,-0.449403,0.112796,-0.267839,-0.449403,-0.112796,0.267839,-0.449403,-0.112796,-0.267839,0.449403,0.112796,0.267839,0.449403,0.112796,-0.267839,0.449403,-0.112796,0.267839,0.449403,-0.112796,-0.267839
		,-0.473333,0.219615,0.118951,-0.473333,0.219615,-0.118951,-0.473333,-0.219615,0.118951,-0.473333,-0.219615,-0.118951,0.473333,0.219615,0.118951,0.473333,0.219615,-0.118951,0.473333,-0.219615,0.118951
		,0.473333,-0.219615,-0.118951,-0.371701,0.372474,0.097599,-0.371701,0.372474,-0.097599,-0.371701,-0.372474,0.097599,-0.371701,-0.372474,-0.097599,0.371701,0.372474,0.097599,0.371701,0.372474,-0.097599
		,0.371701,-0.372474,0.097599,0.371701,-0.372474,-0.097599,-0.693973,0.530109,0.491862,-0.693973,0.530109,-0.491862,-0.693973,-0.530109,0.491862,-0.693973,-0.530109,-0.491862,0.693973,0.530109,0.491862
		,0.693973,0.530109,-0.491862,0.693973,-0.530109,0.491862,0.693973,-0.530109,-0.491862,-0.323842,0.158835,0.395376,-0.323842,0.158835,-0.395376,-0.323842,-0.158835,0.395376,-0.323842,-0.158835,-0.395376
		,0.323842,0.158835,0.395376,0.323842,0.158835,-0.395376,0.323842,-0.158835,0.395376,0.323842,-0.158835,-0.395376,-0.222210,0.311694,0.374024,-0.222210,0.311694,-0.374024,-0.222210,-0.311694,0.374024
		,-0.222210,-0.311694,-0.374024,0.222210,0.311694,0.374024,0.222210,0.311694,-0.374024,0.222210,-0.311694,0.374024,0.222210,-0.311694,-0.374024,-0.160041,-0.092400,0.502268,-0.160041,0.092400,-0.502268
		,0.160041,0.092400,0.502268,0.160041,0.092400,-0.502268,0.000000,0.000000,1.002268,0.000000,0.000000,-1.002268,-0.160041,0.502268,0.092399,-0.160041,-0.502268,0.092399,0.160041,0.502268,0.092399
		,0.160041,-0.502268,0.092399,0.000000,1.002268,0.000000,0.000000,-1.002268,0.000000,-0.502268,-0.092400,0.160041,-0.502268,-0.092400,-0.160041,0.502268,-0.092400,0.160041,0.502268,-0.092400,-0.160041
		,-1.002268,0.000000,0.000000,1.002268,0.000000,0.000000
		PolygonVertexIndex: 14,16,20,-11,21,16,14,-12,22,17,15,-13,15,17,23,-14,24,18,14,-11,14,18,25,-12,15,19,26,-13,27,19,15,-14,10,20,28,-7,29,21,11,-8,30,22,12,-9,13,23,31,-10,32,24,10,-7
		,11,25,33,-8,12,26,34,-9,35,27,13,-10,6,28,36,-3,37,29,7,-4,38,30,8,-5,9,31,39,-6,40,32,6,-3,7,33,41,-4,8,34,42,-5,43,35,9,-6,36,0,-3,3,1,-38
		,4,0,-39,39,1,-6,2,0,-41,41,1,-4,42,0,-5,5,1,-44,44,0,-37,37,1,-46,38,0,-47,47,1,-40,40,0,-49,49,1,-42,50,0,-43
		,43,1,-52,28,52,44,-37,45,53,29,-38,46,54,30,-39,31,55,47,-40,48,56,32,-41,33,57,49,-42,34,58,50,-43,51,59,35,-44,20,60,52,-29,53,61,21,-30,54,62,22,-31,23,63,55,-32
		,56,64,24,-33,25,65,57,-34,26,66,58,-35,59,67,27,-36,16,68,60,-21,61,68,16,-22,62,69,17,-23,17,69,63,-24,64,70,18,-25,18,70,65,-26,19,71,66,-27,67,71,19,-28,68,72,76,-61
		,77,72,68,-62,78,73,69,-63,69,73,79,-64,80,74,70,-65,70,74,81,-66,71,75,82,-67,83,75,71,-68,60,76,84,-53,85,77,61,-54,86,78,62,-55,63,79,87,-56,88,80,64,-57,65,81,89,-58
		,66,82,90,-59,91,83,67,-60,52,84,92,-45,93,85,53,-46,94,86,54,-47,55,87,95,-48,96,88,56,-49,57,89,97,-50,58,90,98,-51,99,91,59,-52,92,0,-45,45,1,-94,46,0,-95
		,95,1,-48,48,0,-97,97,1,-50,98,0,-51,51,1,-100,100,0,-93,93,1,-102,94,0,-101,101,1,-96,96,0,-103,103,1,-98,102,0,-99,99,1,-104
		,84,104,100,-93,101,105,85,-94,100,104,86,-95,87,105,101,-96,102,106,88,-97,89,107,103,-98,90,106,102,-99,103,107,91,-100,76,108,104,-85,105,109,77,-86,104,108,78,-87,79,109,105,-88,106,110,80,-89
		,81,111,107,-90,82,110,106,-91,107,111,83,-92,72,112,108,-77,109,112,72,-78,108,112,73,-79,73,112,109,-80,110,113,74,-81,74,113,111,-82,75,113,110,-83,111,113,75,-84,130,154,-135,135,155,-132
		,136,156,-133,133,157,-138,138,154,-131,131,155,-140,132,156,-141,141,157,-134,130,134,-143,143,135,-132,144,136,-133,133,137,-146,146,138,-131,131,139,-148,132,140,-149
		,149,141,-134,130,142,-151,151,143,-132,152,144,-133,133,145,-154,150,146,-131,131,147,-152,132,148,-153,153,149,-134,198,182,-175,175,183,-200,176,184,-201,201,185,-178
		,178,186,-203,203,187,-180,204,188,-181,181,189,-206,198,190,-183,183,191,-200,184,192,-201,201,193,-186,186,194,-203,203,195,-188,204,196,-189,189,197,-206,198,166,-191
		,191,167,-200,192,168,-201,201,169,-194,194,170,-203,203,171,-196,204,172,-197,197,173,-206,198,174,-207,207,175,-200,208,176,-201,201,177,-210,210,178,-203,203,179,-212
		,204,180,-213,213,181,-206,198,206,-215,215,207,-200,216,208,-201,201,209,-218,218,210,-203,203,211,-220,204,212,-221,221,213,-206,198,214,-167,167,215,-200,168,216,-201
		,201,217,-170,170,218,-203,203,219,-172,204,220,-173,173,221,-206,222,226,-119,223,227,-120,224,226,-121,121,227,-226,118,226,-115,115,227,-224,116,226,-223,119,227,-118
		,114,226,-225,225,227,-116,120,226,-117,117,227,-122,232,228,-123,123,126,-233,124,229,-234,233,127,-126,122,230,-233,232,128,-124,233,231,-125,125,129,-234,228,232,-127
		,127,233,-230,128,232,-231,231,233,-130,162,238,-235,235,238,-164,236,239,-165,165,239,-238,238,162,-159,158,163,-239,159,234,-239,238,235,-160,160,164,-240,239,165,-161
		,239,236,-162,161,237,-240
		Edges: 2,6,3,7,4,8,5,9,6,10,7,11,8,12,9,13,10,14,11,14,12,15,13,15,14,16
		,15,17,14,18,15,19,10,20,11,21,12,22,13,23,10,24,11,25,12,26,13,27,16,20,16,21
		,17,22,17,23,18,24,18,25,19,26,19,27,6,28,7,29,8,30,9,31,6,32,7,33,8,34
		,9,35,20,28,21,29,22,30,23,31,24,32,25,33,26,34,27,35,2,36,3,37,4,38,5,39
		,2,40,3,41,4,42,5,43,28,36,29,37,30,38,31,39,32,40,33,41,34,42,35,43,36,44
		,37,45,38,46,39,47,40,48,41,49,42,50,43,51,44,52,45,53,46,54,47,55,48,56,49,57
		,50,58,51,59,28,52,29,53,30,54,31,55,32,56,33,57,34,58,35,59,52,60,53,61,54,62
		,55,63,56,64,57,65,58,66,59,67,20,60,21,61,22,62,23,63,24,64,25,65,26,66,27,67
		,60,68,61,68,62,69,63,69,64,70,65,70,66,71,67,71,16,68,17,69,18,70,19,71,68,72
		,69,73,70,74,71,75,60,76,61,77,62,78,63,79,64,80,65,81,66,82,67,83,72,76,72,77
		,73,78,73,79,74,80,74,81,75,82,75,83,52,84,53,85,54,86,55,87,56,88,57,89,58,90
		,59,91,76,84,77,85,78,86,79,87,80,88,81,89,82,90,83,91,44,92,45,93,46,94,47,95
		,48,96,49,97,50,98,51,99,84,92,85,93,86,94,87,95,88,96,89,97,90,98,91,99,92,100
		,93,101,94,100,95,101,96,102,97,103,98,102,99,103,100,104,101,105,102,106,103,107,84,104,85,105
		,86,104,87,105,88,106,89,107,90,106,91,107,104,108,105,109,106,110,107,111,76,108,77,109,78,108
		,79,109,80,110,81,111,82,110,83,111,108,112,109,112,110,113,111,113,72,112,73,112,74,113,75,113
		,0,100,1,101,0,102,1,103,0,92,1,93,0,94,1,95,0,96,1,97,0,98,1,99,0,44
		,1,45,0,46,1,47,0,48,1,49,0,50,1,51,0,36,1,37,0,38,1,39,0,40,1,41
		,0,42,1,43,0,2,1,3,0,4,1,5,114,118,117,119,116,120,117,121,123,126,125,127,123,128
		,125,129,134,154,135,155,136,156,137,157,138,154,139,155,140,156,141,157,130,134,131,135,132,136,133,137
		,130,138,131,139,132,140,133,141,134,142,135,143,136,144,137,145,138,146,139,147,140,148,141,149,130,142
		,131,143,132,144,133,145,130,146,131,147,132,148,133,149,142,150,143,151,144,152,145,153,146,150,147,151
		,148,152,149,153,130,150,131,151,132,152,133,153,130,154,131,155,132,156,133,157,158,162,158,163,160,164
		,160,165,174,182,175,183,176,184,177,185,178,186,179,187,180,188,181,189,182,190,183,191,184,192,185,193
		,186,194,187,195,188,196,189,197,166,190,167,191,168,192,169,193,170,194,171,195,172,196,173,197,198,206
		,199,207,200,208,201,209,202,210,203,211,204,212,205,213,206,214,207,215,208,216,209,217,210,218,211,219
		,212,220,213,221,198,214,199,215,200,216,201,217,202,218,203,219,204,220,205,221,174,198,175,199,176,200
		,177,201,178,202,179,203,180,204,181,205,166,198,167,199,168,200,169,201,170,202,171,203,172,204,173,205
		,166,214,167,215,168,216,169,217,170,218,171,219,172,220,173,221,174,206,175,207,176,208,177,209,178,210
		,179,211,180,212,181,213,190,198,191,199,192,200,193,201,194,202,195,203,196,204,197,205,182,198,183,199
		,184,200,185,201,186,202,187,203,188,204,189,205,118,222,119,223,120,224,121,225,118,226,119,227,120,226
		,121,227,126,228,127,229,128,230,129,231,162,234,163,235,164,236,165,237,162,238,163,238,164,239,165,239
		,222,226,223,227,224,226,225,227,114,226,115,227,116,226,117,227,228,232,229,233,230,232,231,233,234,238
		,235,238,236,239,237,239,158,238,159,238,160,239,161,239,122,232,123,232,124,233,125,233,126,232,127,233
		,128,232,129,233,116,222,114,224,122,228,122,230,159,234,161,236,124,229,124,231,115,223,115,225,161,237
		,159,235
		GeometryVersion: 124
        LayerElementNormal: 0 {
            Version: 101
            Name: ""
            MappingInformationType: "ByVertice"
            ReferenceInformationType: "Direct"
            Normals: 0.000000000000000,0.000000000000000,1.000000000000000,0.000000000000000,0.000000000000000,-1.000000000000000
			 ,0.000000000000000,0.382183283567429,0.924069941043854,0.000000000000000,0.382183283567429,-0.924069941043854
			 ,0.000000000000000,-0.382183283567429,0.924069941043854,0.000000000000000,-0.382183283567429,-0.924069941043854
			 ,0.000000000000000,0.706564545631409,0.707602143287659,0.000000000000000,0.706564545631409,-0.707602143287659
			 ,0.000000000000000,-0.706564545631409,0.707602143287659,0.000000000000000,-0.706564545631409,-0.707602143287659
			 ,0.000000000000000,0.923673212528229,0.383129358291626,0.000000000000000,0.923673212528229,-0.383129358291626
			 ,0.000000000000000,-0.923673212528229,0.383129358291626,0.000000000000000,-0.923673212528229,-0.383129358291626
			 ,0.000000000000000,0.999969482421875,0.000000000000000,0.000000000000000,-0.999969482421875,0.000000000000000
			 ,-0.382671594619751,0.923856317996979,0.000000000000000,-0.382671594619751,-0.923856317996979,0.000000000000000
			 ,0.382671594619751,0.923856317996979,0.000000000000000,0.382671594619751,-0.923856317996979,0.000000000000000
			 ,-0.353465378284454,0.853358566761017,0.383129358291626,-0.353465378284454,0.853358566761017,-0.383129358291626
			 ,-0.353465378284454,-0.853358566761017,0.383129358291626,-0.353465378284454,-0.853358566761017,-0.383129358291626
			 ,0.353465378284454,0.853358566761017,0.383129358291626,0.353465378284454,0.853358566761017,-0.383129358291626
			 ,0.353465378284454,-0.853358566761017,0.383129358291626,0.353465378284454,-0.853358566761017,-0.383129358291626
			 ,-0.270393997430801,0.652790904045105,0.707602143287659,-0.270393997430801,0.652790904045105,-0.707602143287659
			 ,-0.270393997430801,-0.652790904045105,0.707602143287659,-0.270393997430801,-0.652790904045105,-0.707602143287659
			 ,0.270393997430801,0.652790904045105,0.707602143287659,0.270393997430801,0.652790904045105,-0.707602143287659
			 ,0.270393997430801,-0.652790904045105,0.707602143287659,0.270393997430801,-0.652790904045105,-0.707602143287659
			 ,-0.146244704723358,0.353099167346954,0.924069941043854,-0.146244704723358,0.353099167346954,-0.924069941043854
			 ,-0.146244704723358,-0.353099167346954,0.924069941043854,-0.146244704723358,-0.353099167346954,-0.924069941043854
			 ,0.146244704723358,0.353099167346954,0.924069941043854,0.146244704723358,0.353099167346954,-0.924069941043854
			 ,0.146244704723358,-0.353099167346954,0.924069941043854,0.146244704723358,-0.353099167346954,-0.924069941043854
			 ,-0.270241409540176,0.270241409540176,0.924069941043854,-0.270241409540176,0.270241409540176,-0.924069941043854
			 ,-0.270241409540176,-0.270241409540176,0.924069941043854,-0.270241409540176,-0.270241409540176,-0.924069941043854
			 ,0.270241409540176,0.270241409540176,0.924069941043854,0.270241409540176,0.270241409540176,-0.924069941043854
			 ,0.270241409540176,-0.270241409540176,0.924069941043854,0.270241409540176,-0.270241409540176,-0.924069941043854
			 ,-0.499618530273438,0.499618530273438,0.707602143287659,-0.499618530273438,0.499618530273438,-0.707602143287659
			 ,-0.499618530273438,-0.499618530273438,0.707602143287659,-0.499618530273438,-0.499618530273438,-0.707602143287659
			 ,0.499618530273438,0.499618530273438,0.707602143287659,0.499618530273438,0.499618530273438,-0.707602143287659
			 ,0.499618530273438,-0.499618530273438,0.707602143287659,0.499618530273438,-0.499618530273438,-0.707602143287659
			 ,-0.653126597404480,0.653126597404480,0.383129358291626,-0.653126597404480,0.653126597404480,-0.383129358291626
			 ,-0.653126597404480,-0.653126597404480,0.383129358291626,-0.653126597404480,-0.653126597404480,-0.383129358291626
			 ,0.653126597404480,0.653126597404480,0.383129358291626,0.653126597404480,0.653126597404480,-0.383129358291626
			 ,0.653126597404480,-0.653126597404480,0.383129358291626,0.653126597404480,-0.653126597404480,-0.383129358291626
			 ,-0.707083344459534,0.707083344459534,0.000000000000000,-0.707083344459534,-0.707083344459534,0.000000000000000
			 ,0.707083344459534,0.707083344459534,0.000000000000000,0.707083344459534,-0.707083344459534,0.000000000000000
			 ,-0.923856317996979,0.382671594619751,0.000000000000000,-0.923856317996979,-0.382671594619751,0.000000000000000
			 ,0.923856317996979,0.382671594619751,0.000000000000000,0.923856317996979,-0.382671594619751,0.000000000000000
			 ,-0.853358566761017,0.353465378284454,0.383129358291626,-0.853358566761017,0.353465378284454,-0.383129358291626
			 ,-0.853358566761017,-0.353465378284454,0.383129358291626,-0.853358566761017,-0.353465378284454,-0.383129358291626
			 ,0.853358566761017,0.353465378284454,0.383129358291626,0.853358566761017,0.353465378284454,-0.383129358291626
			 ,0.853358566761017,-0.353465378284454,0.383129358291626,0.853358566761017,-0.353465378284454,-0.383129358291626
			 ,-0.652790904045105,0.270393997430801,0.707602143287659,-0.652790904045105,0.270393997430801,-0.707602143287659
			 ,-0.652790904045105,-0.270393997430801,0.707602143287659,-0.652790904045105,-0.270393997430801,-0.707602143287659
			 ,0.652790904045105,0.270393997430801,0.707602143287659,0.652790904045105,0.270393997430801,-0.707602143287659
			 ,0.652790904045105,-0.270393997430801,0.707602143287659,0.652790904045105,-0.270393997430801,-0.707602143287659
			 ,-0.353099167346954,0.146244704723358,0.924069941043854,-0.353099167346954,0.146244704723358,-0.924069941043854
			 ,-0.353099167346954,-0.146244704723358,0.924069941043854,-0.353099167346954,-0.146244704723358,-0.924069941043854
			 ,0.353099167346954,0.146244704723358,0.924069941043854,0.353099167346954,0.146244704723358,-0.924069941043854
			 ,0.353099167346954,-0.146244704723358,0.924069941043854,0.353099167346954,-0.146244704723358,-0.924069941043854
			 ,-0.382183283567429,0.000000000000000,0.924069941043854,-0.382183283567429,0.000000000000000,-0.924069941043854
			 ,0.382183283567429,0.000000000000000,0.924069941043854,0.382183283567429,0.000000000000000,-0.924069941043854
			 ,-0.706564545631409,0.000000000000000,0.707602143287659,-0.706564545631409,0.000000000000000,-0.707602143287659
			 ,0.706564545631409,0.000000000000000,0.707602143287659,0.706564545631409,0.000000000000000,-0.707602143287659
			 ,-0.923673212528229,0.000000000000000,0.383129358291626,-0.923673212528229,0.000000000000000,-0.383129358291626
			 ,0.923673212528229,0.000000000000000,0.383129358291626,0.923673212528229,0.000000000000000,-0.383129358291626
			 ,-1.000000000000000,0.000000000000000,0.000000000000000,1.000000000000000,0.000000000000000,0.000000000000000
			 ,0.000000000000000,0.937955856323242,0.346659749746323,0.000000000000000,0.937955856323242,-0.346659749746323
			 ,0.000000000000000,-0.937955856323242,0.346659749746323,0.000000000000000,-0.937955856323242,-0.346659749746323
			 ,-0.812311172485352,0.468977928161621,0.346659749746323,-0.812311172485352,-0.468977928161621,-0.346659749746323
			 ,0.812311172485352,-0.468977928161621,0.346659749746323,0.812311172485352,-0.468977928161621,-0.346659749746323
			 ,0.000000000000000,0.346659749746323,0.937955856323242,0.000000000000000,0.346659749746323,-0.937955856323242
			 ,0.000000000000000,-0.346659749746323,0.937955856323242,0.000000000000000,-0.346659749746323,-0.937955856323242
			 ,-0.812311172485352,0.346659749746323,-0.468977928161621,-0.812311172485352,-0.346659749746323,-0.468977928161621
			 ,0.812311172485352,0.346659749746323,-0.468977928161621,0.812311172485352,-0.346659749746323,-0.468977928161621
			 ,0.000000000000000,0.707083344459534,0.707083344459534,0.000000000000000,0.707083344459534,-0.707083344459534
			 ,0.000000000000000,-0.707083344459534,0.707083344459534,0.000000000000000,-0.707083344459534,-0.707083344459534
			 ,-0.812311172485352,0.576738774776459,-0.086489453911781,-0.812311172485352,0.576738774776459,0.086489453911781
			 ,-0.812311172485352,-0.576738774776459,-0.086489453911781,-0.812311172485352,-0.576738774776459,0.086489453911781
			 ,0.812311172485352,0.576738774776459,-0.086489453911781,0.812311172485352,0.576738774776459,0.086489453911781
			 ,0.812311172485352,-0.576738774776459,-0.086489453911781,0.812311172485352,-0.576738774776459,0.086489453911781
			 ,-0.812311172485352,-0.086458936333656,0.576738774776459,-0.812311172485352,-0.086458936333656,-0.576738774776459
			 ,-0.812311172485352,0.086458936333656,0.576738774776459,-0.812311172485352,0.086458936333656,-0.576738774776459
			 ,0.812311172485352,-0.086458936333656,0.576738774776459,0.812311172485352,-0.086458936333656,-0.576738774776459
			 ,0.812311172485352,0.086458936333656,0.576738774776459,0.812311172485352,0.086458936333656,-0.576738774776459
			 ,0.000000000000000,-0.418103575706482,0.908383429050446,0.000000000000000,-0.418103575706482,-0.908383429050446
			 ,0.000000000000000,0.418103575706482,0.908383429050446,0.000000000000000,0.418103575706482,-0.908383429050446
			 ,0.000000000000000,0.908383429050446,-0.418103575706482,0.000000000000000,0.908383429050446,0.418103575706482
			 ,0.000000000000000,-0.908383429050446,-0.418103575706482,0.000000000000000,-0.908383429050446,0.418103575706482
			 ,-0.346659749746323,0.937955856323242,0.000000000000000,-0.346659749746323,-0.937955856323242,0.000000000000000
			 ,0.346659749746323,0.937955856323242,0.000000000000000,0.346659749746323,-0.937955856323242,0.000000000000000
			 ,-0.346659749746323,0.468977928161621,0.812311172485352,-0.346659749746323,0.468977928161621,-0.812311172485352
			 ,0.346659749746323,0.468977928161621,0.812311172485352,0.346659749746323,0.468977928161621,-0.812311172485352
			 ,0.275795757770538,0.959196746349335,0.061738945543766,0.275795757770538,0.959196746349335,-0.061738945543766
			 ,0.275795757770538,-0.959196746349335,0.061738945543766,0.275795757770538,-0.959196746349335,-0.061738945543766
			 ,-0.275795757770538,0.959196746349335,0.061738945543766,-0.275795757770538,0.959196746349335,-0.061738945543766
			 ,-0.275795757770538,-0.959196746349335,0.061738945543766,-0.275795757770538,-0.959196746349335,-0.061738945543766
			 ,-0.755882441997528,-0.592486321926117,0.278481394052505,-0.755882441997528,-0.592486321926117,-0.278481394052505
			 ,-0.755882441997528,0.592486321926117,0.278481394052505,-0.755882441997528,0.592486321926117,-0.278481394052505
			 ,0.755882441997528,-0.592486321926117,0.278481394052505,0.755882441997528,-0.592486321926117,-0.278481394052505
			 ,0.755882441997528,0.592486321926117,0.278481394052505,0.755882441997528,0.592486321926117,-0.278481394052505
			 ,-0.877346098423004,-0.050294503569603,-0.477187424898148,-0.877346098423004,-0.050294503569603,0.477187424898148
			 ,-0.877346098423004,0.050294503569603,-0.477187424898148,-0.877346098423004,0.050294503569603,0.477187424898148
			 ,0.877346098423004,-0.050294503569603,-0.477187424898148,0.877346098423004,-0.050294503569603,0.477187424898148
			 ,0.877346098423004,0.050294503569603,-0.477187424898148,0.877346098423004,0.050294503569603,0.477187424898148
			 ,-0.361491739749908,0.725516498088837,-0.585558652877808,-0.361491739749908,0.725516498088837,0.585558652877808
			 ,-0.361491739749908,-0.725516498088837,-0.585558652877808,-0.361491739749908,-0.725516498088837,0.585558652877808
			 ,0.361491739749908,0.725516498088837,-0.585558652877808,0.361491739749908,0.725516498088837,0.585558652877808
			 ,0.361491739749908,-0.725516498088837,-0.585558652877808,0.361491739749908,-0.725516498088837,0.585558652877808
			 ,-0.692373394966125,0.528885781764984,0.490737617015839,-0.692373394966125,0.528885781764984,-0.490737617015839
			 ,-0.692373394966125,-0.528885781764984,0.490737617015839,-0.692373394966125,-0.528885781764984,-0.490737617015839
			 ,0.692373394966125,0.528885781764984,0.490737617015839,0.692373394966125,0.528885781764984,-0.490737617015839
			 ,0.692373394966125,-0.528885781764984,0.490737617015839,0.692373394966125,-0.528885781764984,-0.490737617015839
			 ,-0.118564411997795,-0.358806103467941,0.925840020179749,-0.118564411997795,-0.358806103467941,-0.925840020179749
			 ,-0.118564411997795,0.358806103467941,0.925840020179749,-0.118564411997795,0.358806103467941,-0.925840020179749
			 ,0.118564411997795,-0.358806103467941,0.925840020179749,0.118564411997795,-0.358806103467941,-0.925840020179749
			 ,0.118564411997795,0.358806103467941,0.925840020179749,0.118564411997795,0.358806103467941,-0.925840020179749
			 ,0.397259443998337,0.417035430669785,0.817438304424286,0.397259443998337,0.417035430669785,-0.817438304424286
			 ,0.397259443998337,-0.417035430669785,0.817438304424286,0.397259443998337,-0.417035430669785,-0.817438304424286
			 ,-0.397259443998337,0.417035430669785,0.817438304424286,-0.397259443998337,0.417035430669785,-0.817438304424286
			 ,-0.397259443998337,-0.417035430669785,0.817438304424286,-0.397259443998337,-0.417035430669785,-0.817438304424286
			 ,-0.812311172485352,-0.468977928161621,0.346659749746323,-0.812311172485352,0.468977928161621,-0.346659749746323
			 ,0.812311172485352,0.468977928161621,0.346659749746323,0.812311172485352,0.468977928161621,-0.346659749746323
			 ,0.000000000000000,0.000000000000000,0.999969482421875,0.000000000000000,0.000000000000000,-0.999969482421875
			 ,-0.812311172485352,0.346659749746323,0.468977928161621,-0.812311172485352,-0.346659749746323,0.468977928161621
			 ,0.812311172485352,0.346659749746323,0.468977928161621,0.812311172485352,-0.346659749746323,0.468977928161621
			 ,0.000000000000000,1.000000000000000,0.000000000000000,0.000000000000000,-1.000000000000000,0.000000000000000
			 ,-0.346659749746323,-0.468977928161621,0.812311172485352,-0.346659749746323,-0.468977928161621,-0.812311172485352
			 ,0.346659749746323,-0.468977928161621,0.812311172485352,0.346659749746323,-0.468977928161621,-0.812311172485352
			 ,-0.999969482421875,0.000000000000000,0.000000000000000,0.999969482421875,0.000000000000000,0.000000000000000
		}
        LayerElementSmoothing: 0 {
            Version: 102
            Name: ""
            MappingInformationType: "ByPolygon"
            ReferenceInformationType: "Direct"
            Smoothing: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			 ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			 ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
		}
        LayerElementSmoothing: 0 {
            Version: 101
            Name: ""
            MappingInformationType: "ByEdge"
            ReferenceInformationType: "Direct"
            Smoothing: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
			 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVTex"
            MappingInformationType: "ByPolygonVertex"
            ReferenceInformationType: "IndexToDirect"
            UV: 0.015680,0.527895,0.263295,0.499222,0.299363,0.617942,0.086941,0.649520,0.299363,0.617942,0.263295,0.499222,0.015680,0.527895
			 ,0.086941,0.649520,0.299363,0.617942,0.263295,0.499222,0.015680,0.527895,0.086941,0.649520,0.015680,0.527895,0.263295,0.499222
			 ,0.299363,0.617942,0.086941,0.649520,0.299363,0.617942,0.263295,0.499222,0.015680,0.527895,0.086941,0.649520,0.015680,0.527895
			 ,0.263295,0.499222,0.299363,0.617942,0.086941,0.649520,0.015680,0.527895,0.263295,0.499222,0.299363,0.617942,0.086941,0.649520
			 ,0.299363,0.617942,0.263295,0.499222,0.015680,0.527895,0.086941,0.649520,0.086941,0.649520,0.299363,0.617942,0.347692,0.727381
			 ,0.195115,0.759854,0.347692,0.727381,0.299363,0.617942,0.086941,0.649520,0.195115,0.759854,0.347692,0.727381,0.299363,0.617942
			 ,0.086941,0.649520,0.195115,0.759854,0.086941,0.649520,0.299363,0.617942,0.347692,0.727381,0.195115,0.759854,0.347692,0.727381
			 ,0.299363,0.617942,0.086941,0.649520,0.195115,0.759854,0.086941,0.649520,0.299363,0.617942,0.347692,0.727381,0.195115,0.759854
			 ,0.086941,0.649520,0.299363,0.617942,0.347692,0.727381,0.195115,0.759854,0.347692,0.727381,0.299363,0.617942,0.086941,0.649520
			 ,0.195115,0.759854,0.195115,0.759854,0.347692,0.727381,0.415272,0.835697,0.336584,0.857689,0.415272,0.835697,0.347692,0.727381
			 ,0.195115,0.759854,0.336584,0.857689,0.415272,0.835697,0.347692,0.727381,0.195115,0.759854,0.336584,0.857689,0.195115,0.759854
			 ,0.347692,0.727381,0.415272,0.835697,0.336584,0.857689,0.415272,0.835697,0.347692,0.727381,0.195115,0.759854,0.336584,0.857689
			 ,0.195115,0.759854,0.347692,0.727381,0.415272,0.835697,0.336584,0.857689,0.195115,0.759854,0.347692,0.727381,0.415272,0.835697
			 ,0.336584,0.857689,0.415272,0.835697,0.347692,0.727381,0.195115,0.759854,0.336584,0.857689,0.415272,0.835697,0.501292,0.945896
			 ,0.336584,0.857689,0.336584,0.857689,0.501292,0.945896,0.415272,0.835697,0.336584,0.857689,0.501292,0.945896,0.415272,0.835697
			 ,0.415272,0.835697,0.501292,0.945896,0.336584,0.857689,0.336584,0.857689,0.501292,0.945896,0.415272,0.835697,0.415272,0.835697
			 ,0.501292,0.945896,0.336584,0.857689,0.415272,0.835697,0.501292,0.945896,0.336584,0.857689,0.336584,0.857689,0.501292,0.945896
			 ,0.415272,0.835697,0.502069,0.828387,0.501292,0.945896,0.415272,0.835697,0.415272,0.835697,0.501292,0.945896,0.502069,0.828387
			 ,0.415272,0.835697,0.501292,0.945896,0.502069,0.828387,0.502069,0.828387,0.501292,0.945896,0.415272,0.835697,0.415272,0.835697
			 ,0.501292,0.945896,0.502069,0.828387,0.502069,0.828387,0.501292,0.945896,0.415272,0.835697,0.502069,0.828387,0.501292,0.945896
			 ,0.415272,0.835697,0.415272,0.835697,0.501292,0.945896,0.502069,0.828387,0.347692,0.727381,0.502904,0.717087,0.502069,0.828387
			 ,0.415272,0.835697,0.502069,0.828387,0.502904,0.717087,0.347692,0.727381,0.415272,0.835697,0.502069,0.828387,0.502904,0.717087
			 ,0.347692,0.727381,0.415272,0.835697,0.347692,0.727381,0.502904,0.717087,0.502069,0.828387,0.415272,0.835697,0.502069,0.828387
			 ,0.502904,0.717087,0.347692,0.727381,0.415272,0.835697,0.347692,0.727381,0.502904,0.717087,0.502069,0.828387,0.415272,0.835697
			 ,0.347692,0.727381,0.502904,0.717087,0.502069,0.828387,0.415272,0.835697,0.502069,0.828387,0.502904,0.717087,0.347692,0.727381
			 ,0.415272,0.835697,0.299363,0.617942,0.504741,0.607850,0.502904,0.717087,0.347692,0.727381,0.502904,0.717087,0.504741,0.607850
			 ,0.299363,0.617942,0.347692,0.727381,0.502904,0.717087,0.504741,0.607850,0.299363,0.617942,0.347692,0.727381,0.299363,0.617942
			 ,0.504741,0.607850,0.502904,0.717087,0.347692,0.727381,0.502904,0.717087,0.504741,0.607850,0.299363,0.617942,0.347692,0.727381
			 ,0.299363,0.617942,0.504741,0.607850,0.502904,0.717087,0.347692,0.727381,0.299363,0.617942,0.504741,0.607850,0.502904,0.717087
			 ,0.347692,0.727381,0.502904,0.717087,0.504741,0.607850,0.299363,0.617942,0.347692,0.727381,0.263295,0.499222,0.510904,0.491802
			 ,0.504741,0.607850,0.299363,0.617942,0.504741,0.607850,0.510904,0.491802,0.263295,0.499222,0.299363,0.617942,0.504741,0.607850
			 ,0.510904,0.491802,0.263295,0.499222,0.299363,0.617942,0.263295,0.499222,0.510904,0.491802,0.504741,0.607850,0.299363,0.617942
			 ,0.504741,0.607850,0.510904,0.491802,0.263295,0.499222,0.299363,0.617942,0.263295,0.499222,0.510904,0.491802,0.504741,0.607850
			 ,0.299363,0.617942,0.263295,0.499222,0.510904,0.491802,0.504741,0.607850,0.299363,0.617942,0.504741,0.607850,0.510904,0.491802
			 ,0.263295,0.499222,0.299363,0.617942,0.510904,0.491802,0.751301,0.505663,0.709298,0.619287,0.504741,0.607850,0.709298,0.619287
			 ,0.751301,0.505663,0.510904,0.491802,0.504741,0.607850,0.709298,0.619287,0.751301,0.505663,0.510904,0.491802,0.504741,0.607850
			 ,0.510904,0.491802,0.751301,0.505663,0.709298,0.619287,0.504741,0.607850,0.709298,0.619287,0.751301,0.505663,0.510904,0.491802
			 ,0.504741,0.607850,0.510904,0.491802,0.751301,0.505663,0.709298,0.619287,0.504741,0.607850,0.510904,0.491802,0.751301,0.505663
			 ,0.709298,0.619287,0.504741,0.607850,0.709298,0.619287,0.751301,0.505663,0.510904,0.491802,0.504741,0.607850,0.504741,0.607850
			 ,0.709298,0.619287,0.657495,0.727766,0.502904,0.717087,0.657495,0.727766,0.709298,0.619287,0.504741,0.607850,0.502904,0.717087
			 ,0.657495,0.727766,0.709298,0.619287,0.504741,0.607850,0.502904,0.717087,0.504741,0.607850,0.709298,0.619287,0.657495,0.727766
			 ,0.502904,0.717087,0.657495,0.727766,0.709298,0.619287,0.504741,0.607850,0.502904,0.717087,0.504741,0.607850,0.709298,0.619287
			 ,0.657495,0.727766,0.502904,0.717087,0.504741,0.607850,0.709298,0.619287,0.657495,0.727766,0.502904,0.717087,0.657495,0.727766
			 ,0.709298,0.619287,0.504741,0.607850,0.502904,0.717087,0.502904,0.717087,0.657495,0.727766,0.588721,0.835923,0.502069,0.828387
			 ,0.588721,0.835923,0.657495,0.727766,0.502904,0.717087,0.502069,0.828387,0.588721,0.835923,0.657495,0.727766,0.502904,0.717087
			 ,0.502069,0.828387,0.502904,0.717087,0.657495,0.727766,0.588721,0.835923,0.502069,0.828387,0.588721,0.835923,0.657495,0.727766
			 ,0.502904,0.717087,0.502069,0.828387,0.502904,0.717087,0.657495,0.727766,0.588721,0.835923,0.502069,0.828387,0.502904,0.717087
			 ,0.657495,0.727766,0.588721,0.835923,0.502069,0.828387,0.588721,0.835923,0.657495,0.727766,0.502904,0.717087,0.502069,0.828387
			 ,0.588721,0.835923,0.501292,0.945896,0.502069,0.828387,0.502069,0.828387,0.501292,0.945896,0.588721,0.835923,0.502069,0.828387
			 ,0.501292,0.945896,0.588721,0.835923,0.588721,0.835923,0.501292,0.945896,0.502069,0.828387,0.502069,0.828387,0.501292,0.945896
			 ,0.588721,0.835923,0.588721,0.835923,0.501292,0.945896,0.502069,0.828387,0.588721,0.835923,0.501292,0.945896,0.502069,0.828387
			 ,0.502069,0.828387,0.501292,0.945896,0.588721,0.835923,0.668354,0.858328,0.501292,0.945896,0.588721,0.835923,0.588721,0.835923
			 ,0.501292,0.945896,0.668354,0.858328,0.588721,0.835923,0.501292,0.945896,0.668354,0.858328,0.668354,0.858328,0.501292,0.945896
			 ,0.588721,0.835923,0.588721,0.835923,0.501292,0.945896,0.668354,0.858328,0.668354,0.858328,0.501292,0.945896,0.588721,0.835923
			 ,0.668354,0.858328,0.501292,0.945896,0.588721,0.835923,0.588721,0.835923,0.501292,0.945896,0.668354,0.858328,0.657495,0.727766
			 ,0.809485,0.759159,0.668354,0.858328,0.588721,0.835923,0.668354,0.858328,0.809485,0.759159,0.657495,0.727766,0.588721,0.835923
			 ,0.668354,0.858328,0.809485,0.759159,0.657495,0.727766,0.588721,0.835923,0.657495,0.727766,0.809485,0.759159,0.668354,0.858328
			 ,0.588721,0.835923,0.668354,0.858328,0.809485,0.759159,0.657495,0.727766,0.588721,0.835923,0.657495,0.727766,0.809485,0.759159
			 ,0.668354,0.858328,0.588721,0.835923,0.657495,0.727766,0.809485,0.759159,0.668354,0.858328,0.588721,0.835923,0.668354,0.858328
			 ,0.809485,0.759159,0.657495,0.727766,0.588721,0.835923,0.709298,0.619287,0.912420,0.650687,0.809485,0.759159,0.657495,0.727766
			 ,0.809485,0.759159,0.912420,0.650687,0.709298,0.619287,0.657495,0.727766,0.809485,0.759159,0.912420,0.650687,0.709298,0.619287
			 ,0.657495,0.727766,0.709298,0.619287,0.912420,0.650687,0.809485,0.759159,0.657495,0.727766,0.809485,0.759159,0.912420,0.650687
			 ,0.709298,0.619287,0.657495,0.727766,0.709298,0.619287,0.912420,0.650687,0.809485,0.759159,0.657495,0.727766,0.709298,0.619287
			 ,0.912420,0.650687,0.809485,0.759159,0.657495,0.727766,0.809485,0.759159,0.912420,0.650687,0.709298,0.619287,0.657495,0.727766
			 ,0.751301,0.505663,0.984957,0.535822,0.912420,0.650687,0.709298,0.619287,0.912420,0.650687,0.984957,0.535822,0.751301,0.505663
			 ,0.709298,0.619287,0.912420,0.650687,0.984957,0.535822,0.751301,0.505663,0.709298,0.619287,0.751301,0.505663,0.984957,0.535822
			 ,0.912420,0.650687,0.709298,0.619287,0.912420,0.650687,0.984957,0.535822,0.751301,0.505663,0.709298,0.619287,0.751301,0.505663
			 ,0.984957,0.535822,0.912420,0.650687,0.709298,0.619287,0.751301,0.505663,0.984957,0.535822,0.912420,0.650687,0.709298,0.619287
			 ,0.912420,0.650687,0.984957,0.535822,0.751301,0.505663,0.709298,0.619287,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881
			 ,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809
			 ,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809,0.741246,0.039881
			 ,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809
			 ,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881
			 ,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881
			 ,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881
			 ,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881
			 ,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809,0.741246,0.039881
			 ,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881
			 ,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809
			 ,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881
			 ,0.507460,0.444809,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809
			 ,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881
			 ,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881
			 ,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881
			 ,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809
			 ,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.741246,0.039881
			 ,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809
			 ,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809
			 ,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881
			 ,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809
			 ,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881
			 ,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881
			 ,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881
			 ,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809
			 ,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.507460,0.444809,0.741246,0.039881
			 ,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809
			 ,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881
			 ,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881
			 ,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881
			 ,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881
			 ,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.741246,0.039881,0.507460,0.444809
			 ,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881
			 ,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.273675,0.039881
			 ,0.507460,0.444809,0.741246,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809
			 ,0.273675,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881
			 ,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809
			 ,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881
			 ,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809
			 ,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881
			 ,0.507460,0.444809,0.741246,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.273675,0.039881,0.507460,0.444809
			 ,0.741246,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881
			 ,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881
			 ,0.273675,0.039881,0.507460,0.444809,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881
			 ,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881
			 ,0.507460,0.444809,0.273675,0.039881,0.741246,0.039881,0.741246,0.039881,0.273675,0.039881,0.507460,0.444809
			UVIndex: 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54
				,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109
				,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164
				,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219
				,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274
				,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329
				,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384
				,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439
				,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494
				,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549
				,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604
				,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659
				,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714
				,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769
				,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803
		}
		LayerElementTexture: 0 {
			Version: 101
			Name: "UVTex"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			BlendMode: "Translucent"
			TextureAlpha: 1
			TextureId: 0
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: 0
		}
        Layer: 0 {
            Version: 100
            LayerElement:  {
                Type: "LayerElementNormal"
                TypedIndex: 0
            }
            LayerElement:  {
                Type: "LayerElementMaterial"
                TypedIndex: 0
            }
            LayerElement:  {
                Type: "LayerElementTexture"
                TypedIndex: 0
            }
            LayerElement:  {
                Type: "LayerElementUV"
                TypedIndex: 0
            }
		}
	}
	Model: "Model::Producer Perspective", "Camera" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",0.000000000000000,71.299999999999997,287.500000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
			Property: "Color", "Color", "A",0.8,0.8,0.8
			Property: "Roll", "Roll", "A+",0
			Property: "FieldOfView", "FieldOfView", "A+",40
			Property: "FieldOfViewX", "FieldOfView", "A+",1
			Property: "FieldOfViewY", "FieldOfView", "A+",1
			Property: "OpticalCenterX", "Real", "A+",0
			Property: "OpticalCenterY", "Real", "A+",0
			Property: "BackgroundColor", "Color", "A+",0.63,0.63,0.63
			Property: "TurnTable", "Real", "A+",0
			Property: "DisplayTurnTableIcon", "bool", "",1
			Property: "Motion Blur Intensity", "Real", "A+",1
			Property: "UseMotionBlur", "bool", "",0
			Property: "UseRealTimeMotionBlur", "bool", "",1
			Property: "ResolutionMode", "enum", "",0
			Property: "ApertureMode", "enum", "",2
			Property: "GateFit", "enum", "",0
			Property: "FocalLength", "Real", "A+",21.3544940948486
			Property: "CameraFormat", "enum", "",0
			Property: "AspectW", "double", "",320
			Property: "AspectH", "double", "",200
			Property: "PixelAspectRatio", "double", "",1
			Property: "UseFrameColor", "bool", "",0
			Property: "FrameColor", "ColorRGB", "",0.3,0.3,0.3
			Property: "ShowName", "bool", "",1
			Property: "ShowGrid", "bool", "",1
			Property: "ShowOpticalCenter", "bool", "",0
			Property: "ShowAzimut", "bool", "",1
			Property: "ShowTimeCode", "bool", "",0
			Property: "NearPlane", "double", "",10.000000
			Property: "FarPlane", "double", "",4000.000000
			Property: "FilmWidth", "double", "",0.816
			Property: "FilmHeight", "double", "",0.612
			Property: "FilmAspectRatio", "double", "",1.33333333333333
			Property: "FilmSqueezeRatio", "double", "",1
			Property: "FilmFormatIndex", "enum", "",4
			Property: "ViewFrustum", "bool", "",1
			Property: "ViewFrustumNearFarPlane", "bool", "",0
			Property: "ViewFrustumBackPlaneMode", "enum", "",2
			Property: "BackPlaneDistance", "double", "",100
			Property: "BackPlaneDistanceMode", "enum", "",0
			Property: "ViewCameraToLookAt", "bool", "",1
			Property: "LockMode", "bool", "",0
			Property: "LockInterestNavigation", "bool", "",0
			Property: "FitImage", "bool", "",0
			Property: "Crop", "bool", "",0
			Property: "Center", "bool", "",1
			Property: "KeepRatio", "bool", "",1
			Property: "BackgroundMode", "enum", "",0
			Property: "BackgroundAlphaTreshold", "double", "",0.5
			Property: "ForegroundTransparent", "bool", "",1
			Property: "DisplaySafeArea", "bool", "",0
			Property: "SafeAreaDisplayStyle", "enum", "",1
			Property: "SafeAreaAspectRatio", "double", "",1.33333333333333
			Property: "Use2DMagnifierZoom", "bool", "",0
			Property: "2D Magnifier Zoom", "Real", "A+",100
			Property: "2D Magnifier X", "Real", "A+",50
			Property: "2D Magnifier Y", "Real", "A+",50
			Property: "CameraProjectionType", "enum", "",0
			Property: "UseRealTimeDOFAndAA", "bool", "",0
			Property: "UseDepthOfField", "bool", "",0
			Property: "FocusSource", "enum", "",0
			Property: "FocusAngle", "double", "",3.5
			Property: "FocusDistance", "double", "",200
			Property: "UseAntialiasing", "bool", "",0
			Property: "AntialiasingIntensity", "double", "",0.77777
			Property: "UseAccumulationBuffer", "bool", "",0
			Property: "FrameSamplingCount", "int", "",7
		}
		MultiLayer: 0
		MultiTake: 0
		Hidden: "True"
		Shading: Y
		Culling: "CullingOff"
		TypeFlags: "Camera"
		GeometryVersion: 124
		Position: 0.000000,71.300000,287.500000
		Up: 0,1,0
		LookAt: 0,0,0
		ShowInfoOnMoving: 1
		ShowAudio: 0
		AudioColor: 0,1,0
		CameraOrthoZoom: 1
	}
	Model: "Model::Producer Top", "Camera" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",0.000000000000000,4000.000000000000000,0.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
			Property: "Color", "Color", "A",0.8,0.8,0.8
			Property: "Roll", "Roll", "A+",0
			Property: "FieldOfView", "FieldOfView", "A+",40
			Property: "FieldOfViewX", "FieldOfView", "A+",1
			Property: "FieldOfViewY", "FieldOfView", "A+",1
			Property: "OpticalCenterX", "Real", "A+",0
			Property: "OpticalCenterY", "Real", "A+",0
			Property: "BackgroundColor", "Color", "A+",0.63,0.63,0.63
			Property: "TurnTable", "Real", "A+",0
			Property: "DisplayTurnTableIcon", "bool", "",1
			Property: "Motion Blur Intensity", "Real", "A+",1
			Property: "UseMotionBlur", "bool", "",0
			Property: "UseRealTimeMotionBlur", "bool", "",1
			Property: "ResolutionMode", "enum", "",0
			Property: "ApertureMode", "enum", "",2
			Property: "GateFit", "enum", "",0
			Property: "FocalLength", "Real", "A+",21.3544940948486
			Property: "CameraFormat", "enum", "",0
			Property: "AspectW", "double", "",320
			Property: "AspectH", "double", "",200
			Property: "PixelAspectRatio", "double", "",1
			Property: "UseFrameColor", "bool", "",0
			Property: "FrameColor", "ColorRGB", "",0.3,0.3,0.3
			Property: "ShowName", "bool", "",1
			Property: "ShowGrid", "bool", "",1
			Property: "ShowOpticalCenter", "bool", "",0
			Property: "ShowAzimut", "bool", "",1
			Property: "ShowTimeCode", "bool", "",0
			Property: "NearPlane", "double", "",1.000000
			Property: "FarPlane", "double", "",30000.000000
			Property: "FilmWidth", "double", "",0.816
			Property: "FilmHeight", "double", "",0.612
			Property: "FilmAspectRatio", "double", "",1.33333333333333
			Property: "FilmSqueezeRatio", "double", "",1
			Property: "FilmFormatIndex", "enum", "",4
			Property: "ViewFrustum", "bool", "",1
			Property: "ViewFrustumNearFarPlane", "bool", "",0
			Property: "ViewFrustumBackPlaneMode", "enum", "",2
			Property: "BackPlaneDistance", "double", "",100
			Property: "BackPlaneDistanceMode", "enum", "",0
			Property: "ViewCameraToLookAt", "bool", "",1
			Property: "LockMode", "bool", "",0
			Property: "LockInterestNavigation", "bool", "",0
			Property: "FitImage", "bool", "",0
			Property: "Crop", "bool", "",0
			Property: "Center", "bool", "",1
			Property: "KeepRatio", "bool", "",1
			Property: "BackgroundMode", "enum", "",0
			Property: "BackgroundAlphaTreshold", "double", "",0.5
			Property: "ForegroundTransparent", "bool", "",1
			Property: "DisplaySafeArea", "bool", "",0
			Property: "SafeAreaDisplayStyle", "enum", "",1
			Property: "SafeAreaAspectRatio", "double", "",1.33333333333333
			Property: "Use2DMagnifierZoom", "bool", "",0
			Property: "2D Magnifier Zoom", "Real", "A+",100
			Property: "2D Magnifier X", "Real", "A+",50
			Property: "2D Magnifier Y", "Real", "A+",50
			Property: "CameraProjectionType", "enum", "",1
			Property: "UseRealTimeDOFAndAA", "bool", "",0
			Property: "UseDepthOfField", "bool", "",0
			Property: "FocusSource", "enum", "",0
			Property: "FocusAngle", "double", "",3.5
			Property: "FocusDistance", "double", "",200
			Property: "UseAntialiasing", "bool", "",0
			Property: "AntialiasingIntensity", "double", "",0.77777
			Property: "UseAccumulationBuffer", "bool", "",0
			Property: "FrameSamplingCount", "int", "",7
		}
		MultiLayer: 0
		MultiTake: 0
		Hidden: "True"
		Shading: Y
		Culling: "CullingOff"
		TypeFlags: "Camera"
		GeometryVersion: 124
		Position: 0.000000,4000.000000,0.000000
		Up: 0,0,-1
		LookAt: 0,0,0
		ShowInfoOnMoving: 1
		ShowAudio: 0
		AudioColor: 0,1,0
		CameraOrthoZoom: 1
	}
	Model: "Model::Producer Bottom", "Camera" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",0.000000000000000,-4000.000000000000000,0.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
			Property: "Color", "Color", "A",0.8,0.8,0.8
			Property: "Roll", "Roll", "A+",0
			Property: "FieldOfView", "FieldOfView", "A+",40
			Property: "FieldOfViewX", "FieldOfView", "A+",1
			Property: "FieldOfViewY", "FieldOfView", "A+",1
			Property: "OpticalCenterX", "Real", "A+",0
			Property: "OpticalCenterY", "Real", "A+",0
			Property: "BackgroundColor", "Color", "A+",0.63,0.63,0.63
			Property: "TurnTable", "Real", "A+",0
			Property: "DisplayTurnTableIcon", "bool", "",1
			Property: "Motion Blur Intensity", "Real", "A+",1
			Property: "UseMotionBlur", "bool", "",0
			Property: "UseRealTimeMotionBlur", "bool", "",1
			Property: "ResolutionMode", "enum", "",0
			Property: "ApertureMode", "enum", "",2
			Property: "GateFit", "enum", "",0
			Property: "FocalLength", "Real", "A+",21.3544940948486
			Property: "CameraFormat", "enum", "",0
			Property: "AspectW", "double", "",320
			Property: "AspectH", "double", "",200
			Property: "PixelAspectRatio", "double", "",1
			Property: "UseFrameColor", "bool", "",0
			Property: "FrameColor", "ColorRGB", "",0.3,0.3,0.3
			Property: "ShowName", "bool", "",1
			Property: "ShowGrid", "bool", "",1
			Property: "ShowOpticalCenter", "bool", "",0
			Property: "ShowAzimut", "bool", "",1
			Property: "ShowTimeCode", "bool", "",0
			Property: "NearPlane", "double", "",1.000000
			Property: "FarPlane", "double", "",30000.000000
			Property: "FilmWidth", "double", "",0.816
			Property: "FilmHeight", "double", "",0.612
			Property: "FilmAspectRatio", "double", "",1.33333333333333
			Property: "FilmSqueezeRatio", "double", "",1
			Property: "FilmFormatIndex", "enum", "",4
			Property: "ViewFrustum", "bool", "",1
			Property: "ViewFrustumNearFarPlane", "bool", "",0
			Property: "ViewFrustumBackPlaneMode", "enum", "",2
			Property: "BackPlaneDistance", "double", "",100
			Property: "BackPlaneDistanceMode", "enum", "",0
			Property: "ViewCameraToLookAt", "bool", "",1
			Property: "LockMode", "bool", "",0
			Property: "LockInterestNavigation", "bool", "",0
			Property: "FitImage", "bool", "",0
			Property: "Crop", "bool", "",0
			Property: "Center", "bool", "",1
			Property: "KeepRatio", "bool", "",1
			Property: "BackgroundMode", "enum", "",0
			Property: "BackgroundAlphaTreshold", "double", "",0.5
			Property: "ForegroundTransparent", "bool", "",1
			Property: "DisplaySafeArea", "bool", "",0
			Property: "SafeAreaDisplayStyle", "enum", "",1
			Property: "SafeAreaAspectRatio", "double", "",1.33333333333333
			Property: "Use2DMagnifierZoom", "bool", "",0
			Property: "2D Magnifier Zoom", "Real", "A+",100
			Property: "2D Magnifier X", "Real", "A+",50
			Property: "2D Magnifier Y", "Real", "A+",50
			Property: "CameraProjectionType", "enum", "",1
			Property: "UseRealTimeDOFAndAA", "bool", "",0
			Property: "UseDepthOfField", "bool", "",0
			Property: "FocusSource", "enum", "",0
			Property: "FocusAngle", "double", "",3.5
			Property: "FocusDistance", "double", "",200
			Property: "UseAntialiasing", "bool", "",0
			Property: "AntialiasingIntensity", "double", "",0.77777
			Property: "UseAccumulationBuffer", "bool", "",0
			Property: "FrameSamplingCount", "int", "",7
		}
		MultiLayer: 0
		MultiTake: 0
		Hidden: "True"
		Shading: Y
		Culling: "CullingOff"
		TypeFlags: "Camera"
		GeometryVersion: 124
		Position: 0.000000,-4000.000000,0.000000
		Up: 0,0,-1
		LookAt: 0,0,0
		ShowInfoOnMoving: 1
		ShowAudio: 0
		AudioColor: 0,1,0
		CameraOrthoZoom: 1
	}
	Model: "Model::Producer Front", "Camera" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",0.000000000000000,0.000000000000000,4000.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
			Property: "Color", "Color", "A",0.8,0.8,0.8
			Property: "Roll", "Roll", "A+",0
			Property: "FieldOfView", "FieldOfView", "A+",40
			Property: "FieldOfViewX", "FieldOfView", "A+",1
			Property: "FieldOfViewY", "FieldOfView", "A+",1
			Property: "OpticalCenterX", "Real", "A+",0
			Property: "OpticalCenterY", "Real", "A+",0
			Property: "BackgroundColor", "Color", "A+",0.63,0.63,0.63
			Property: "TurnTable", "Real", "A+",0
			Property: "DisplayTurnTableIcon", "bool", "",1
			Property: "Motion Blur Intensity", "Real", "A+",1
			Property: "UseMotionBlur", "bool", "",0
			Property: "UseRealTimeMotionBlur", "bool", "",1
			Property: "ResolutionMode", "enum", "",0
			Property: "ApertureMode", "enum", "",2
			Property: "GateFit", "enum", "",0
			Property: "FocalLength", "Real", "A+",21.3544940948486
			Property: "CameraFormat", "enum", "",0
			Property: "AspectW", "double", "",320
			Property: "AspectH", "double", "",200
			Property: "PixelAspectRatio", "double", "",1
			Property: "UseFrameColor", "bool", "",0
			Property: "FrameColor", "ColorRGB", "",0.3,0.3,0.3
			Property: "ShowName", "bool", "",1
			Property: "ShowGrid", "bool", "",1
			Property: "ShowOpticalCenter", "bool", "",0
			Property: "ShowAzimut", "bool", "",1
			Property: "ShowTimeCode", "bool", "",0
			Property: "NearPlane", "double", "",1.000000
			Property: "FarPlane", "double", "",30000.000000
			Property: "FilmWidth", "double", "",0.816
			Property: "FilmHeight", "double", "",0.612
			Property: "FilmAspectRatio", "double", "",1.33333333333333
			Property: "FilmSqueezeRatio", "double", "",1
			Property: "FilmFormatIndex", "enum", "",4
			Property: "ViewFrustum", "bool", "",1
			Property: "ViewFrustumNearFarPlane", "bool", "",0
			Property: "ViewFrustumBackPlaneMode", "enum", "",2
			Property: "BackPlaneDistance", "double", "",100
			Property: "BackPlaneDistanceMode", "enum", "",0
			Property: "ViewCameraToLookAt", "bool", "",1
			Property: "LockMode", "bool", "",0
			Property: "LockInterestNavigation", "bool", "",0
			Property: "FitImage", "bool", "",0
			Property: "Crop", "bool", "",0
			Property: "Center", "bool", "",1
			Property: "KeepRatio", "bool", "",1
			Property: "BackgroundMode", "enum", "",0
			Property: "BackgroundAlphaTreshold", "double", "",0.5
			Property: "ForegroundTransparent", "bool", "",1
			Property: "DisplaySafeArea", "bool", "",0
			Property: "SafeAreaDisplayStyle", "enum", "",1
			Property: "SafeAreaAspectRatio", "double", "",1.33333333333333
			Property: "Use2DMagnifierZoom", "bool", "",0
			Property: "2D Magnifier Zoom", "Real", "A+",100
			Property: "2D Magnifier X", "Real", "A+",50
			Property: "2D Magnifier Y", "Real", "A+",50
			Property: "CameraProjectionType", "enum", "",1
			Property: "UseRealTimeDOFAndAA", "bool", "",0
			Property: "UseDepthOfField", "bool", "",0
			Property: "FocusSource", "enum", "",0
			Property: "FocusAngle", "double", "",3.5
			Property: "FocusDistance", "double", "",200
			Property: "UseAntialiasing", "bool", "",0
			Property: "AntialiasingIntensity", "double", "",0.77777
			Property: "UseAccumulationBuffer", "bool", "",0
			Property: "FrameSamplingCount", "int", "",7
		}
		MultiLayer: 0
		MultiTake: 0
		Hidden: "True"
		Shading: Y
		Culling: "CullingOff"
		TypeFlags: "Camera"
		GeometryVersion: 124
		Position: 0.000000,0.000000,4000.000000
		Up: 0,1,0
		LookAt: 0,0,0
		ShowInfoOnMoving: 1
		ShowAudio: 0
		AudioColor: 0,1,0
		CameraOrthoZoom: 1
	}
	Model: "Model::Producer Back", "Camera" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",0.000000000000000,0.000000000000000,-4000.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
			Property: "Color", "Color", "A",0.8,0.8,0.8
			Property: "Roll", "Roll", "A+",0
			Property: "FieldOfView", "FieldOfView", "A+",40
			Property: "FieldOfViewX", "FieldOfView", "A+",1
			Property: "FieldOfViewY", "FieldOfView", "A+",1
			Property: "OpticalCenterX", "Real", "A+",0
			Property: "OpticalCenterY", "Real", "A+",0
			Property: "BackgroundColor", "Color", "A+",0.63,0.63,0.63
			Property: "TurnTable", "Real", "A+",0
			Property: "DisplayTurnTableIcon", "bool", "",1
			Property: "Motion Blur Intensity", "Real", "A+",1
			Property: "UseMotionBlur", "bool", "",0
			Property: "UseRealTimeMotionBlur", "bool", "",1
			Property: "ResolutionMode", "enum", "",0
			Property: "ApertureMode", "enum", "",2
			Property: "GateFit", "enum", "",0
			Property: "FocalLength", "Real", "A+",21.3544940948486
			Property: "CameraFormat", "enum", "",0
			Property: "AspectW", "double", "",320
			Property: "AspectH", "double", "",200
			Property: "PixelAspectRatio", "double", "",1
			Property: "UseFrameColor", "bool", "",0
			Property: "FrameColor", "ColorRGB", "",0.3,0.3,0.3
			Property: "ShowName", "bool", "",1
			Property: "ShowGrid", "bool", "",1
			Property: "ShowOpticalCenter", "bool", "",0
			Property: "ShowAzimut", "bool", "",1
			Property: "ShowTimeCode", "bool", "",0
			Property: "NearPlane", "double", "",1.000000
			Property: "FarPlane", "double", "",30000.000000
			Property: "FilmWidth", "double", "",0.816
			Property: "FilmHeight", "double", "",0.612
			Property: "FilmAspectRatio", "double", "",1.33333333333333
			Property: "FilmSqueezeRatio", "double", "",1
			Property: "FilmFormatIndex", "enum", "",4
			Property: "ViewFrustum", "bool", "",1
			Property: "ViewFrustumNearFarPlane", "bool", "",0
			Property: "ViewFrustumBackPlaneMode", "enum", "",2
			Property: "BackPlaneDistance", "double", "",100
			Property: "BackPlaneDistanceMode", "enum", "",0
			Property: "ViewCameraToLookAt", "bool", "",1
			Property: "LockMode", "bool", "",0
			Property: "LockInterestNavigation", "bool", "",0
			Property: "FitImage", "bool", "",0
			Property: "Crop", "bool", "",0
			Property: "Center", "bool", "",1
			Property: "KeepRatio", "bool", "",1
			Property: "BackgroundMode", "enum", "",0
			Property: "BackgroundAlphaTreshold", "double", "",0.5
			Property: "ForegroundTransparent", "bool", "",1
			Property: "DisplaySafeArea", "bool", "",0
			Property: "SafeAreaDisplayStyle", "enum", "",1
			Property: "SafeAreaAspectRatio", "double", "",1.33333333333333
			Property: "Use2DMagnifierZoom", "bool", "",0
			Property: "2D Magnifier Zoom", "Real", "A+",100
			Property: "2D Magnifier X", "Real", "A+",50
			Property: "2D Magnifier Y", "Real", "A+",50
			Property: "CameraProjectionType", "enum", "",1
			Property: "UseRealTimeDOFAndAA", "bool", "",0
			Property: "UseDepthOfField", "bool", "",0
			Property: "FocusSource", "enum", "",0
			Property: "FocusAngle", "double", "",3.5
			Property: "FocusDistance", "double", "",200
			Property: "UseAntialiasing", "bool", "",0
			Property: "AntialiasingIntensity", "double", "",0.77777
			Property: "UseAccumulationBuffer", "bool", "",0
			Property: "FrameSamplingCount", "int", "",7
		}
		MultiLayer: 0
		MultiTake: 0
		Hidden: "True"
		Shading: Y
		Culling: "CullingOff"
		TypeFlags: "Camera"
		GeometryVersion: 124
		Position: 0.000000,0.000000,-4000.000000
		Up: 0,1,0
		LookAt: 0,0,0
		ShowInfoOnMoving: 1
		ShowAudio: 0
		AudioColor: 0,1,0
		CameraOrthoZoom: 1
	}
	Model: "Model::Producer Right", "Camera" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",4000.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
			Property: "Color", "Color", "A",0.8,0.8,0.8
			Property: "Roll", "Roll", "A+",0
			Property: "FieldOfView", "FieldOfView", "A+",40
			Property: "FieldOfViewX", "FieldOfView", "A+",1
			Property: "FieldOfViewY", "FieldOfView", "A+",1
			Property: "OpticalCenterX", "Real", "A+",0
			Property: "OpticalCenterY", "Real", "A+",0
			Property: "BackgroundColor", "Color", "A+",0.63,0.63,0.63
			Property: "TurnTable", "Real", "A+",0
			Property: "DisplayTurnTableIcon", "bool", "",1
			Property: "Motion Blur Intensity", "Real", "A+",1
			Property: "UseMotionBlur", "bool", "",0
			Property: "UseRealTimeMotionBlur", "bool", "",1
			Property: "ResolutionMode", "enum", "",0
			Property: "ApertureMode", "enum", "",2
			Property: "GateFit", "enum", "",0
			Property: "FocalLength", "Real", "A+",21.3544940948486
			Property: "CameraFormat", "enum", "",0
			Property: "AspectW", "double", "",320
			Property: "AspectH", "double", "",200
			Property: "PixelAspectRatio", "double", "",1
			Property: "UseFrameColor", "bool", "",0
			Property: "FrameColor", "ColorRGB", "",0.3,0.3,0.3
			Property: "ShowName", "bool", "",1
			Property: "ShowGrid", "bool", "",1
			Property: "ShowOpticalCenter", "bool", "",0
			Property: "ShowAzimut", "bool", "",1
			Property: "ShowTimeCode", "bool", "",0
			Property: "NearPlane", "double", "",1.000000
			Property: "FarPlane", "double", "",30000.000000
			Property: "FilmWidth", "double", "",0.816
			Property: "FilmHeight", "double", "",0.612
			Property: "FilmAspectRatio", "double", "",1.33333333333333
			Property: "FilmSqueezeRatio", "double", "",1
			Property: "FilmFormatIndex", "enum", "",4
			Property: "ViewFrustum", "bool", "",1
			Property: "ViewFrustumNearFarPlane", "bool", "",0
			Property: "ViewFrustumBackPlaneMode", "enum", "",2
			Property: "BackPlaneDistance", "double", "",100
			Property: "BackPlaneDistanceMode", "enum", "",0
			Property: "ViewCameraToLookAt", "bool", "",1
			Property: "LockMode", "bool", "",0
			Property: "LockInterestNavigation", "bool", "",0
			Property: "FitImage", "bool", "",0
			Property: "Crop", "bool", "",0
			Property: "Center", "bool", "",1
			Property: "KeepRatio", "bool", "",1
			Property: "BackgroundMode", "enum", "",0
			Property: "BackgroundAlphaTreshold", "double", "",0.5
			Property: "ForegroundTransparent", "bool", "",1
			Property: "DisplaySafeArea", "bool", "",0
			Property: "SafeAreaDisplayStyle", "enum", "",1
			Property: "SafeAreaAspectRatio", "double", "",1.33333333333333
			Property: "Use2DMagnifierZoom", "bool", "",0
			Property: "2D Magnifier Zoom", "Real", "A+",100
			Property: "2D Magnifier X", "Real", "A+",50
			Property: "2D Magnifier Y", "Real", "A+",50
			Property: "CameraProjectionType", "enum", "",1
			Property: "UseRealTimeDOFAndAA", "bool", "",0
			Property: "UseDepthOfField", "bool", "",0
			Property: "FocusSource", "enum", "",0
			Property: "FocusAngle", "double", "",3.5
			Property: "FocusDistance", "double", "",200
			Property: "UseAntialiasing", "bool", "",0
			Property: "AntialiasingIntensity", "double", "",0.77777
			Property: "UseAccumulationBuffer", "bool", "",0
			Property: "FrameSamplingCount", "int", "",7
		}
		MultiLayer: 0
		MultiTake: 0
		Hidden: "True"
		Shading: Y
		Culling: "CullingOff"
		TypeFlags: "Camera"
		GeometryVersion: 124
		Position: 4000.000000,0.000000,0.000000
		Up: 0,1,0
		LookAt: 0,0,0
		ShowInfoOnMoving: 1
		ShowAudio: 0
		AudioColor: 0,1,0
		CameraOrthoZoom: 1
	}
	Model: "Model::Producer Left", "Camera" {
		Version: 232
        Properties60:  {
            Property: "QuaternionInterpolate", "bool", "",0
            Property: "Visibility", "Visibility", "A+",1
			Property: "Lcl Translation", "Lcl Translation", "A+",-4000.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Rotation", "Lcl Rotation", "A+",0.000000000000000,0.000000000000000,0.000000000000000
			Property: "Lcl Scaling", "Lcl Scaling", "A+",1.000000000000000,1.000000000000000,1.000000000000000
            Property: "RotationOffset", "Vector3D", "",0,0,0
            Property: "RotationPivot", "Vector3D", "",0,0,0
            Property: "ScalingOffset", "Vector3D", "",0,0,0
            Property: "ScalingPivot", "Vector3D", "",0,0,0
            Property: "TranslationActive", "bool", "",0
            Property: "TranslationMin", "Vector3D", "",0,0,0
            Property: "TranslationMax", "Vector3D", "",0,0,0
            Property: "TranslationMinX", "bool", "",0
            Property: "TranslationMinY", "bool", "",0
            Property: "TranslationMinZ", "bool", "",0
            Property: "TranslationMaxX", "bool", "",0
            Property: "TranslationMaxY", "bool", "",0
            Property: "TranslationMaxZ", "bool", "",0
            Property: "RotationOrder", "enum", "",0
            Property: "RotationSpaceForLimitOnly", "bool", "",0
            Property: "AxisLen", "double", "",10
            Property: "PreRotation", "Vector3D", "",0,0,0
            Property: "PostRotation", "Vector3D", "",0,0,0
            Property: "RotationActive", "bool", "",0
            Property: "RotationMin", "Vector3D", "",0,0,0
            Property: "RotationMax", "Vector3D", "",0,0,0
            Property: "RotationMinX", "bool", "",0
            Property: "RotationMinY", "bool", "",0
            Property: "RotationMinZ", "bool", "",0
            Property: "RotationMaxX", "bool", "",0
            Property: "RotationMaxY", "bool", "",0
            Property: "RotationMaxZ", "bool", "",0
            Property: "RotationStiffnessX", "double", "",0
            Property: "RotationStiffnessY", "double", "",0
            Property: "RotationStiffnessZ", "double", "",0
            Property: "MinDampRangeX", "double", "",0
            Property: "MinDampRangeY", "double", "",0
            Property: "MinDampRangeZ", "double", "",0
            Property: "MaxDampRangeX", "double", "",0
            Property: "MaxDampRangeY", "double", "",0
            Property: "MaxDampRangeZ", "double", "",0
            Property: "MinDampStrengthX", "double", "",0
            Property: "MinDampStrengthY", "double", "",0
            Property: "MinDampStrengthZ", "double", "",0
            Property: "MaxDampStrengthX", "double", "",0
            Property: "MaxDampStrengthY", "double", "",0
            Property: "MaxDampStrengthZ", "double", "",0
            Property: "PreferedAngleX", "double", "",0
            Property: "PreferedAngleY", "double", "",0
            Property: "PreferedAngleZ", "double", "",0
            Property: "InheritType", "enum", "",0
            Property: "ScalingActive", "bool", "",0
            Property: "ScalingMin", "Vector3D", "",1,1,1
            Property: "ScalingMax", "Vector3D", "",1,1,1
            Property: "ScalingMinX", "bool", "",0
            Property: "ScalingMinY", "bool", "",0
            Property: "ScalingMinZ", "bool", "",0
            Property: "ScalingMaxX", "bool", "",0
            Property: "ScalingMaxY", "bool", "",0
            Property: "ScalingMaxZ", "bool", "",0
            Property: "GeometricTranslation", "Vector3D", "",0,0,0
            Property: "GeometricRotation", "Vector3D", "",0,0,0
            Property: "GeometricScaling", "Vector3D", "",1,1,1
            Property: "LookAtProperty", "object", ""
            Property: "UpVectorProperty", "object", ""
            Property: "Show", "bool", "",1
            Property: "NegativePercentShapeSupport", "bool", "",1
            Property: "DefaultAttributeIndex", "int", "",0
			Property: "Color", "Color", "A",0.8,0.8,0.8
			Property: "Roll", "Roll", "A+",0
			Property: "FieldOfView", "FieldOfView", "A+",40
			Property: "FieldOfViewX", "FieldOfView", "A+",1
			Property: "FieldOfViewY", "FieldOfView", "A+",1
			Property: "OpticalCenterX", "Real", "A+",0
			Property: "OpticalCenterY", "Real", "A+",0
			Property: "BackgroundColor", "Color", "A+",0.63,0.63,0.63
			Property: "TurnTable", "Real", "A+",0
			Property: "DisplayTurnTableIcon", "bool", "",1
			Property: "Motion Blur Intensity", "Real", "A+",1
			Property: "UseMotionBlur", "bool", "",0
			Property: "UseRealTimeMotionBlur", "bool", "",1
			Property: "ResolutionMode", "enum", "",0
			Property: "ApertureMode", "enum", "",2
			Property: "GateFit", "enum", "",0
			Property: "FocalLength", "Real", "A+",21.3544940948486
			Property: "CameraFormat", "enum", "",0
			Property: "AspectW", "double", "",320
			Property: "AspectH", "double", "",200
			Property: "PixelAspectRatio", "double", "",1
			Property: "UseFrameColor", "bool", "",0
			Property: "FrameColor", "ColorRGB", "",0.3,0.3,0.3
			Property: "ShowName", "bool", "",1
			Property: "ShowGrid", "bool", "",1
			Property: "ShowOpticalCenter", "bool", "",0
			Property: "ShowAzimut", "bool", "",1
			Property: "ShowTimeCode", "bool", "",0
			Property: "NearPlane", "double", "",1.000000
			Property: "FarPlane", "double", "",30000.000000
			Property: "FilmWidth", "double", "",0.816
			Property: "FilmHeight", "double", "",0.612
			Property: "FilmAspectRatio", "double", "",1.33333333333333
			Property: "FilmSqueezeRatio", "double", "",1
			Property: "FilmFormatIndex", "enum", "",4
			Property: "ViewFrustum", "bool", "",1
			Property: "ViewFrustumNearFarPlane", "bool", "",0
			Property: "ViewFrustumBackPlaneMode", "enum", "",2
			Property: "BackPlaneDistance", "double", "",100
			Property: "BackPlaneDistanceMode", "enum", "",0
			Property: "ViewCameraToLookAt", "bool", "",1
			Property: "LockMode", "bool", "",0
			Property: "LockInterestNavigation", "bool", "",0
			Property: "FitImage", "bool", "",0
			Property: "Crop", "bool", "",0
			Property: "Center", "bool", "",1
			Property: "KeepRatio", "bool", "",1
			Property: "BackgroundMode", "enum", "",0
			Property: "BackgroundAlphaTreshold", "double", "",0.5
			Property: "ForegroundTransparent", "bool", "",1
			Property: "DisplaySafeArea", "bool", "",0
			Property: "SafeAreaDisplayStyle", "enum", "",1
			Property: "SafeAreaAspectRatio", "double", "",1.33333333333333
			Property: "Use2DMagnifierZoom", "bool", "",0
			Property: "2D Magnifier Zoom", "Real", "A+",100
			Property: "2D Magnifier X", "Real", "A+",50
			Property: "2D Magnifier Y", "Real", "A+",50
			Property: "CameraProjectionType", "enum", "",1
			Property: "UseRealTimeDOFAndAA", "bool", "",0
			Property: "UseDepthOfField", "bool", "",0
			Property: "FocusSource", "enum", "",0
			Property: "FocusAngle", "double", "",3.5
			Property: "FocusDistance", "double", "",200
			Property: "UseAntialiasing", "bool", "",0
			Property: "AntialiasingIntensity", "double", "",0.77777
			Property: "UseAccumulationBuffer", "bool", "",0
			Property: "FrameSamplingCount", "int", "",7
		}
		MultiLayer: 0
		MultiTake: 0
		Hidden: "True"
		Shading: Y
		Culling: "CullingOff"
		TypeFlags: "Camera"
		GeometryVersion: 124
		Position: -4000.000000,0.000000,0.000000
		Up: 0,1,0
		LookAt: 0,0,0
		ShowInfoOnMoving: 1
		ShowAudio: 0
		AudioColor: 0,1,0
		CameraOrthoZoom: 1
	}
	Material: "Material::None__SpikeBall_png", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties60:  {
			Property: "ShadingModel", "KString", "", "Phong"
			Property: "MultiLayer", "bool", "",0
			Property: "EmissiveColor", "ColorRGB", "",0.8000,0.8000,0.8000
			Property: "EmissiveFactor", "double", "",0.0000
			Property: "AmbientColor", "ColorRGB", "",0.0000,0.0000,0.0000
			Property: "AmbientFactor", "double", "",0.5000
			Property: "DiffuseColor", "ColorRGB", "",0.8000,0.8000,0.8000
			Property: "DiffuseFactor", "double", "",1.0000
			Property: "Bump", "Vector3D", "",0,0,0
			Property: "TransparentColor", "ColorRGB", "",1,1,1
			Property: "TransparencyFactor", "double", "",0.0000
			Property: "SpecularColor", "ColorRGB", "",0.8000,0.8000,0.8000
			Property: "SpecularFactor", "double", "",0.2000
			Property: "ShininessExponent", "double", "",80.0
			Property: "ReflectionColor", "ColorRGB", "",0,0,0
			Property: "ReflectionFactor", "double", "",1
			Property: "Emissive", "ColorRGB", "",0,0,0
			Property: "Ambient", "ColorRGB", "",0.0,0.0,0.0
			Property: "Diffuse", "ColorRGB", "",0.8,0.8,0.8
			Property: "Specular", "ColorRGB", "",0.8,0.8,0.8
			Property: "Shininess", "double", "",20.0
			Property: "Opacity", "double", "",1.0
			Property: "Reflectivity", "double", "",0
		}
	}
	Video: "Video::SpikeBall_png", "Clip" {
        Type: "Clip"
        Properties60:  {
            Property: "FrameRate", "double", "",0
            Property: "LastFrame", "int", "",0
            Property: "Width", "int", "",0
            Property: "Height", "int", "",0
			Property: "Path", "charptr", "", "SpikeBall.png"
            Property: "StartFrame", "int", "",0
            Property: "StopFrame", "int", "",0
            Property: "PlaySpeed", "double", "",1
            Property: "Offset", "KTime", "",0
            Property: "InterlaceMode", "enum", "",0
            Property: "FreeRunning", "bool", "",0
            Property: "Loop", "bool", "",0
            Property: "AccessMode", "enum", "",0
        }
        UseMipMap: 0
		Filename: "SpikeBall.png"
		RelativeFilename: "C:\Users\<USER>\Dropbox\Shared Unity3D Projects\Ball Pack\Assets\Ball Pack\Blender\Materials\SpikeBall.png"
	}
	Texture: "Texture::SpikeBall_png", "TextureVideoClip" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::SpikeBall_png"
        Properties60:  {
            Property: "Translation", "Vector", "A+",0,0,0
            Property: "Rotation", "Vector", "A+",0,0,0
            Property: "Scaling", "Vector", "A+",1,1,1
			Property: "Texture alpha", "Number", "A+",0
            Property: "TextureTypeUse", "enum", "",0
            Property: "CurrentTextureBlendMode", "enum", "",1
            Property: "UseMaterial", "bool", "",0
            Property: "UseMipMap", "bool", "",0
            Property: "CurrentMappingType", "enum", "",0
            Property: "UVSwap", "bool", "",0
			Property: "WrapModeU", "enum", "",0
			Property: "WrapModeV", "enum", "",0
            Property: "TextureRotationPivot", "Vector3D", "",0,0,0
            Property: "TextureScalingPivot", "Vector3D", "",0,0,0
            Property: "VideoProperty", "object", ""
        }
		Media: "Video::SpikeBall_png"
		FileName: "SpikeBall.png"
		RelativeFilename: "C:\Users\<USER>\Dropbox\Shared Unity3D Projects\Ball Pack\Assets\Ball Pack\Blender\Materials\SpikeBall.png"
        ModelUVTranslation: 0,0
        ModelUVScaling: 1,1
        Texture_Alpha_Source: "None"
        Cropping: 0,0,0,0
    }
    Pose: "Pose::BIND_POSES", "BindPose" {
        Type: "BindPose"
        Version: 100
        Properties60:  {
        }
        NbPoseNodes: 2
		PoseNode:  {
			Node: "Model::blend_root"
			Matrix: 1.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,1.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,1.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,1.000000000000000
		}
		PoseNode:  {
			Node: "Model::SpikeBall"
			Matrix: 1.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,-0.000000043711388,-1.000000000000000,0.000000000000000,0.000000000000000,1.000000000000000,-0.000000043711388,0.000000000000000,0.000000000000000,0.000000000000000,0.000000000000000,1.000000000000000
		}
	}
    GlobalSettings:  {
        Version: 1000
        Properties60:  {
            Property: "UpAxis", "int", "",1
            Property: "UpAxisSign", "int", "",1
            Property: "FrontAxis", "int", "",2
            Property: "FrontAxisSign", "int", "",1
            Property: "CoordAxis", "int", "",0
            Property: "CoordAxisSign", "int", "",1
            Property: "UnitScaleFactor", "double", "",100
        }
    }
}

; Object relations
;------------------------------------------------------------------

Relations:  {
	Model: "Model::blend_root", "Null" {
	}
	Model: "Model::SpikeBall", "Mesh" {
	}
    Model: "Model::Producer Perspective", "Camera" {
    }
    Model: "Model::Producer Top", "Camera" {
    }
    Model: "Model::Producer Bottom", "Camera" {
    }
    Model: "Model::Producer Front", "Camera" {
    }
    Model: "Model::Producer Back", "Camera" {
    }
    Model: "Model::Producer Right", "Camera" {
    }
    Model: "Model::Producer Left", "Camera" {
    }
    Model: "Model::Camera Switcher", "CameraSwitcher" {
    }
	Material: "Material::None__SpikeBall_png", "" {
	}
	Texture: "Texture::SpikeBall_png", "TextureVideoClip" {
	}
	Video: "Video::SpikeBall_png", "Clip" {
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	Connect: "OO", "Model::blend_root", "Model::Scene"
	Connect: "OO", "Model::SpikeBall", "Model::blend_root"
	Connect: "OO", "Material::None__SpikeBall_png", "Model::SpikeBall"
	Connect: "OO", "Texture::SpikeBall_png", "Model::SpikeBall"
	Connect: "OO", "Video::SpikeBall_png", "Texture::SpikeBall_png"
}
;Takes and animation section
;----------------------------------------------------

Takes:  {
	Current: "Default Take"
	Take: "Default Take" {
		FileName: "Default_Take.tak"
		LocalTime: 0,479181389250
		ReferenceTime: 0,479181389250

        ;Models animation
        ;----------------------------------------------------
		Model: "Model::SpikeBall" {
			Version: 1.1
			Channel: "Transform" {
				Channel: "T" {
					Channel: "X" {
						Default: 0.000000000000000
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,0.000000000000000,L
						Color: 1,0,0
					}
					Channel: "Y" {
						Default: 0.000000000000000
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,0.000000000000000,L
						Color: 0,1,0
					}
					Channel: "Z" {
						Default: 0.000000000000000
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,0.000000000000000,L
						Color: 0,0,1
					}
					LayerType: 1
				}
				Channel: "R" {
					Channel: "X" {
						Default: -90.000002504348856
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,-90.000002504348856,L
						Color: 1,0,0
					}
					Channel: "Y" {
						Default: -0.000000000000000
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,-0.000000000000000,L
						Color: 0,1,0
					}
					Channel: "Z" {
						Default: 0.000000000000000
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,0.000000000000000,L
						Color: 0,0,1
					}
					LayerType: 2
				}
				Channel: "S" {
					Channel: "X" {
						Default: 1.000000000000000
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,1.000000000000000,L
						Color: 1,0,0
					}
					Channel: "Y" {
						Default: 1.000000000000000
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,1.000000000000000,L
						Color: 0,1,0
					}
					Channel: "Z" {
						Default: 1.000000000000000
						KeyVer: 4005
						KeyCount: 1
						Key: 
							1924423250,1.000000000000000,L
						Color: 0,0,1
					}
					LayerType: 3
				}
			}
		}
	}
}
;Version 5 settings
;------------------------------------------------------------------

Version5:  {
	AmbientRenderSettings:  {
		Version: 101
		AmbientLightColor: 0.0,0.0,0.0,0
	}
	FogOptions:  {
		FlogEnable: 0
		FogMode: 0
		FogDensity: 0.000
		FogStart: 5.000
		FogEnd: 25.000
		FogColor: 0.1,0.1,0.1,1
	}
	Settings:  {
		FrameRate: "24"
		TimeFormat: 1
		SnapOnFrames: 0
		ReferenceTimeIndex: -1
		TimeLineStartTime: 0
		TimeLineStopTime: 479181389250
	}
	RendererSetting:  {
		DefaultCamera: "Producer Perspective"
		DefaultViewingMode: 0
	}
}
