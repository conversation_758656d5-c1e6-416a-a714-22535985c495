{"type": "BaseDialog", "props": {"width": 720, "height": 1280}, "compId": 2, "child": [{"type": "TopInsAuto", "props": {"y": 382, "x": 0, "var": "topPanelUI", "height": 550, "runtime": "com/modules/common/TopInsAuto.ts"}, "compId": 12}, {"type": "Image", "props": {"y": 451, "x": 40, "width": 640, "skin": "common/bg_shengxing.png", "sizeGrid": "8,8,8,10", "height": 41}, "compId": 5}, {"type": "Label", "props": {"y": 459, "x": 303.5, "width": 105, "valign": "middle", "text": "效果设置", "height": 27, "fontSize": 24, "color": "#551511", "align": "center"}, "compId": 6}, {"type": "CheckBox", "props": {"y": 507, "x": 71, "width": 210, "var": "cbMusic", "skin": "common/btnSelect.png", "selected": true, "labelSize": 26, "labelColors": "#551511,#551511,#551511", "label": "音乐", "height": 64, "stateNum": 3, "labelBold": "true", "labelPadding": "10,10,10,10"}, "compId": 7}, {"type": "CheckBox", "props": {"y": 506, "x": 251, "width": 210, "var": "cbSoundEffect", "skin": "common/btnSelect.png", "selected": true, "labelSize": 26, "labelColors": "#551511,#551511,#551511", "label": "音效", "height": 64, "stateNum": 3, "labelBold": "true", "labelPadding": "10,10,10,10"}, "compId": 8}, {"type": "CheckBox", "props": {"y": 506, "x": 439, "width": 210, "var": "cbShowVIP", "skin": "common/btnSelect.png", "selected": true, "labelSize": 26, "labelColors": "#551511,#551511,#551511", "label": "显示VIP", "height": 64, "stateNum": 3, "labelBold": "true", "labelPadding": "10,10,10,10"}, "compId": 38}, {"type": "Box", "props": {"y": 680, "x": 0, "width": 731, "visible": false, "var": "city_bg", "height": 125}, "compId": 24, "child": [{"type": "Image", "props": {"y": 0, "x": 40, "width": 640, "skin": "common/bg_shengxing.png", "sizeGrid": "8,8,8,10", "height": 41}, "compId": 18}, {"type": "Label", "props": {"y": 7, "x": 303, "width": 105, "valign": "middle", "text": "主城场景", "height": 27, "fontSize": 24, "color": "#551511", "align": "center"}, "compId": 19}, {"type": "CheckBox", "props": {"y": 55, "x": 71, "width": 210, "var": "cbNow", "skin": "common/btnSelect.png", "selected": true, "labelSize": 26, "labelColors": "#551511,#551511,#551511", "label": "实时轮换", "height": 64, "stateNum": 3, "labelBold": "true", "labelPadding": "10,10,10,10"}, "compId": 20}, {"type": "CheckBox", "props": {"y": 55, "x": 251, "width": 210, "var": "cb<PERSON><PERSON>ny", "skin": "common/btnSelect.png", "selected": true, "labelSize": 26, "labelColors": "#551511,#551511,#551511", "label": "晴空", "height": 64, "stateNum": 3, "labelBold": "true", "labelPadding": "10,10,10,10"}, "compId": 21}, {"type": "CheckBox", "props": {"y": 55, "x": 381, "width": 210, "var": "cbNormal", "skin": "common/btnSelect.png", "selected": true, "labelSize": 26, "labelColors": "#551511,#551511,#551511", "label": "霞光", "height": 64, "stateNum": 3, "labelBold": "true", "labelPadding": "10,10,10,10"}, "compId": 22}, {"type": "CheckBox", "props": {"y": 55, "x": 521, "width": 210, "var": "cbNight", "skin": "common/btnSelect.png", "selected": true, "labelSize": 26, "labelColors": "#551511,#551511,#551511", "label": "夜景", "height": 64, "stateNum": 3, "labelBold": "true", "labelPadding": "10,10,10,10"}, "compId": 23}]}, {"type": "Box", "props": {"y": 570, "x": 0, "width": 731, "visible": false, "var": "perforBox", "height": 125}, "compId": 29, "child": [{"type": "Image", "props": {"y": 0, "x": 40, "width": 640, "skin": "common/bg_shengxing.png", "sizeGrid": "8,8,8,10", "height": 41}, "compId": 30}, {"type": "Label", "props": {"y": 7, "x": 303, "width": 105, "valign": "middle", "text": "性 能", "height": 27, "fontSize": 24, "color": "#551511", "align": "center"}, "compId": 31}, {"type": "CheckBox", "props": {"y": 55, "x": 71, "width": 210, "var": "cbStat", "skin": "common/btnSelect.png", "selected": true, "labelSize": 26, "labelColors": "#551511,#551511,#551511", "label": "性能面板", "height": 64, "stateNum": 3, "labelBold": "true", "labelPadding": "10,10,10,10"}, "compId": 32}, {"type": "Label", "props": {"y": 64.66666666666666, "x": 390, "var": "lbFrame", "text": "帧 率:", "fontSize": 26, "color": "#551511", "bold": true}, "compId": 37, "child": [{"type": "ComboBox", "props": {"y": -7, "x": 73, "var": "cbFrame", "skin": "common/select_frame.png", "labelSize": 24, "itemSize": 24}, "compId": 36}]}]}, {"type": "Box", "props": {"y": 796, "x": 37, "width": 645, "var": "bottomBox", "height": 80}, "compId": 26, "child": [{"type": "<PERSON><PERSON>", "props": {"y": 42, "x": 418, "var": "logOutBtn", "skin": "common/btnGreen.png", "labelSize": 22, "labelBold": true, "label": "退出游戏", "labelColors": "#b1e6bf,#b1e6bf,#b1e6bf", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 10}, {"type": "<PERSON><PERSON>", "props": {"y": 42, "x": 573, "var": "swithBtn", "skin": "common/btnYellow.png", "label": "切换账号", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 14}, {"type": "<PERSON><PERSON>", "props": {"y": 42, "x": 234, "var": "disBtn", "skin": "common/btnYellow.png", "label": "注销账号", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 17}, {"type": "<PERSON><PERSON>", "props": {"y": 42, "x": 93, "var": "exchangeBtn", "skin": "common/btnYellow.png", "label": "礼包兑换", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 25}, {"type": "<PERSON><PERSON>", "props": {"y": 40, "x": 703, "var": "clearCacheBtn", "skin": "common/btnYellow.png", "label": "清除缓存", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 39}]}], "loadList": ["res/base/TopInsAuto.scene", "common/bg_shengxing.png", "common/btnSelect.png", "common/select_frame.png", "common/btnGreen.png", "common/btnYellow.png"], "loadList3D": []}