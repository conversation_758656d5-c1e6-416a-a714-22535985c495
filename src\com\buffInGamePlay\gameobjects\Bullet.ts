import { GameObject, Component, Vector3, Damage, DamageInfoTag, BulletOnCreate, BulletOnHit, BulletOnRemoved, BulletTween } from "../types/GameTypes";
import { DamageInfo } from "../battle/DamageInfo";
import { DamageManager } from "../managers/DamageManager";
import { AddBuffInfo } from "../character/Buff";

/**
 * 子弹配置模型
 */
export class BulletModel {
    /** 子弹ID */
    public id: string;

    /** 子弹名称 */
    public name: string;

    /** 基础伤害 */
    public damage: Damage;

    /** 基础暴击率 */
    public criticalRate: number;

    /** 子弹速度 */
    public speed: number;

    /** 生存时间 (秒) */
    public lifeTime: number;

    /** 最大贯穿次数 */
    public maxPenetrate: number;

    /** 碰撞半径 */
    public collisionRadius: number;

    /** 伤害标签 */
    public damageTags: DamageInfoTag[];

    /** 附加Buff信息 */
    public addBuffs: AddBuffInfo[];

    /** 飞行轨迹函数 */
    public tween: BulletTween | null;

    /** 回调函数 */
    public onCreate: BulletOnCreate | null;
    public onHit: BulletOnHit | null;
    public onRemoved: BulletOnRemoved | null;

    constructor(
        id: string,
        name: string,
        damage: Damage,
        speed: number,
        lifeTime: number,
        criticalRate: number = 0,
        maxPenetrate: number = 0,
        collisionRadius: number = 0.1,
        damageTags: DamageInfoTag[] = [DamageInfoTag.DirectDamage]
    ) {
        this.id = id;
        this.name = name;
        this.damage = damage;
        this.speed = speed;
        this.lifeTime = lifeTime;
        this.criticalRate = criticalRate;
        this.maxPenetrate = maxPenetrate;
        this.collisionRadius = collisionRadius;
        this.damageTags = [...damageTags];
        this.addBuffs = [];

        this.tween = null;
        this.onCreate = null;
        this.onHit = null;
        this.onRemoved = null;
    }
}

/**
 * 子弹状态组件
 */
export class BulletState extends Component {
    /** 子弹配置 */
    public model: BulletModel;

    /** 发射者 */
    public launcher: GameObject | null;

    /** 目标 (如果有) */
    public target: GameObject | null;

    /** 剩余生存时间 */
    public remainingLifeTime: number;

    /** 已存在时间 */
    public elapsedTime: number = 0;

    /** 剩余贯穿次数 */
    public remainingPenetrate: number;

    /** 已命中的目标列表 */
    public hitTargets: Set<string> = new Set();

    /** 当前飞行方向 */
    public direction: Vector3;

    /** 是否已被销毁 */
    public destroyed: boolean = false;

    constructor(
        model: BulletModel,
        launcher: GameObject | null,
        direction: Vector3,
        target?: GameObject
    ) {
        super();
        this.model = model;
        this.launcher = launcher;
        this.target = target || null;
        this.direction = direction.normalized();
        this.remainingLifeTime = model.lifeTime;
        this.remainingPenetrate = model.maxPenetrate;
    }

    public start(): void {
        // 触发创建回调
        if (this.model.onCreate) {
            this.model.onCreate(this.gameObject);
        }
    }

    public update(deltaTime: number): void {
        if (this.destroyed) return;

        // 更新时间
        this.elapsedTime += deltaTime;
        this.remainingLifeTime -= deltaTime;

        // 检查生存时间
        if (this.remainingLifeTime <= 0) {
            this.destroyBullet();
            return;
        }

        // 更新位置
        this.updatePosition(deltaTime);

        // 检查碰撞
        this.checkCollisions();
    }

    /**
     * 更新子弹位置
     */
    private updatePosition(deltaTime: number): void {
        if (!this.gameObject.transform) return;

        let movement: Vector3;

        // 如果有自定义轨迹函数，使用轨迹函数
        if (this.model.tween) {
            const t = this.elapsedTime / this.model.lifeTime;
            const newPosition = this.model.tween(t, this.gameObject, this.target);
            movement = newPosition.add(this.gameObject.transform.position.multiply(-1));
        } else {
            // 直线飞行
            movement = this.direction.multiply(this.model.speed * deltaTime);
        }

        this.gameObject.transform.translate(movement);
    }

    /**
     * 检查碰撞
     */
    private checkCollisions(): void {
        // TODO: 实现碰撞检测
        // 这里应该使用Laya的碰撞检测系统
        // 检测与角色的碰撞

        // 示例代码结构：
        // const colliders = CollisionSystem.getCollidersInRadius(
        //     this.gameObject.transform.position,
        //     this.model.collisionRadius
        // );

        // for (const collider of colliders) {
        //     if (this.canHit(collider.gameObject)) {
        //         this.hitTarget(collider.gameObject);
        //     }
        // }
    }

    /**
     * 检查是否可以命中目标
     */
    private canHit(target: GameObject): boolean {
        // 不能命中自己
        if (target === this.launcher) {
            return false;
        }

        // 不能重复命中同一目标 (除非有贯穿)
        if (this.hitTargets.has(target.id) && this.remainingPenetrate <= 0) {
            return false;
        }

        // 检查目标是否有ChaState组件
        const chaState = target.getComponent(ChaState);
        if (!chaState) {
            return false;
        }

        // 检查目标是否死亡
        if (chaState.dead) {
            return false;
        }

        // 检查目标是否无敌
        if (chaState.isImmune()) {
            return false;
        }

        return true;
    }

    /**
     * 命中目标
     */
    private hitTarget(target: GameObject): void {
        // 记录命中目标
        this.hitTargets.add(target.id);

        // 创建伤害信息
        const damageInfo = new DamageInfo(
            this.launcher,
            target,
            this.model.damage,
            this.direction.magnitude() > 0 ? Math.atan2(this.direction.z, this.direction.x) * 180 / Math.PI : 0,
            this.model.criticalRate,
            this.model.damageTags
        );

        // 添加Buff信息
        for (const buffInfo of this.model.addBuffs) {
            damageInfo.addBuffToCha(buffInfo);
        }

        // 触发命中回调
        if (this.model.onHit) {
            this.model.onHit(this.gameObject, target);
        }

        // 发送伤害
        DamageManager.instance.addDamage(damageInfo);

        // 减少贯穿次数
        this.remainingPenetrate--;

        // 如果没有贯穿能力，销毁子弹
        if (this.remainingPenetrate < 0) {
            this.destroyBullet();
        }
    }

    /**
     * 销毁子弹
     */
    public destroyBullet(): void {
        if (this.destroyed) return;

        this.destroyed = true;

        // 触发销毁回调
        if (this.model.onRemoved) {
            this.model.onRemoved(this.gameObject);
        }

        // 销毁游戏对象
        this.gameObject.destroy();
    }
}

/**
 * 子弹发射器
 */
export class BulletLauncher {
    /**
     * 发射子弹
     * @param model 子弹配置
     * @param launcher 发射者
     * @param startPosition 起始位置
     * @param direction 发射方向
     * @param target 目标 (可选)
     * @returns 创建的子弹游戏对象
     */
    public static launch(
        model: BulletModel,
        launcher: GameObject | null,
        startPosition: Vector3,
        direction: Vector3,
        target?: GameObject
    ): GameObject {
        // TODO: 这里应该通过游戏对象工厂创建子弹
        // const bulletGameObject = GameObjectFactory.createBullet(model.id);

        // 临时创建游戏对象的示例代码
        const bulletGameObject: GameObject = {
            id: `bullet_${Date.now()}_${Math.random()}`,
            name: model.name,
            active: true,
            transform: {
                position: startPosition,
                rotation: Math.atan2(direction.z, direction.x) * 180 / Math.PI,
                scale: Vector3.one,
                translate: function(delta: Vector3) {
                    this.position = this.position.add(delta);
                },
                rotate: function(angle: number) {
                    this.rotation += angle;
                }
            },
            components: new Map(),
            getComponent: function<T extends Component>(type: new() => T): T | null {
                const typeName = type.name;
                return this.components.get(typeName) as T || null;
            },
            addComponent: function<T extends Component>(component: T): T {
                const typeName = component.constructor.name;
                component.gameObject = this;
                this.components.set(typeName, component);
                return component;
            },
            removeComponent: function<T extends Component>(type: new() => T): boolean {
                const typeName = type.name;
                return this.components.delete(typeName);
            },
            destroy: function() {
                this.active = false;
                // TODO: 从场景中移除
            }
        };

        // 添加子弹状态组件
        const bulletState = new BulletState(model, launcher, direction, target);
        bulletGameObject.addComponent(bulletState);

        return bulletGameObject;
    }
}