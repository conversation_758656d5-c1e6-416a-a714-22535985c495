import { com } from "../../../ui/layaMaxUI";
import { DefaultLayoutManager, XListView } from "../../baseModules/xListView/XListView";
import { XViewHelper } from "../../baseModules/xListView/XViewHelper";
import { RankBaseMiddleItem } from "./RankBaseMiddleItem";
import { RankBaseSmallItem } from "./RankBaseSmallItem";
export class RankBaseItem2 extends com.ui.res.rank.RankBaseItem2UI {
    initUI() {
        this._list = XListView.setUIXList(this, RankBaseItem2Helper, this.rankListBox);
        this._list.setLayoutManager(new DefaultLayoutManager(1));
        this._list.setElasticDistance(0);
        this._list.setSpace(10);
    }
    setDataSource(data, index, parameter) {
        let rankInfo = data;
        let rankList = [];
        for (let i = 0; i < rankInfo.dataList.length; i++) {
            rankList.push({ type: rankInfo.type, dataList: [rankInfo.dataList[i]] });
        }
        this._list.array = rankList;
    }
    Clean() {
    }
}
class RankBaseItem2Helper extends XViewHelper {
    get contextHeight() {
        return this._itemView.height;
    }
    get contextWidth() {
        return this._itemView.width;
    }
    getViewType() {
        if (this.dataSourse.type == 2) {
            return 1;
        }
        else if (this.dataSourse.type == 3) {
            return 2;
        }
    }
    createView() {
        let viewType = this.getViewType();
        switch (viewType) {
            case 1: return new RankBaseMiddleItem();
            case 2: return new RankBaseSmallItem();
        }
    }
}
