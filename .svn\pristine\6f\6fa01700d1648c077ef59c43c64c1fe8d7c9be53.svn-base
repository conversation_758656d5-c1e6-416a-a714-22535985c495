
import { Handler } from "laya/utils/Handler";
import { UrlConfig } from "../../../../game/UrlConfig";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { PanelIconMacro } from "../../../auto/PanelConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { p_auction_house_goods } from "../../../proto/common/p_auction_house_goods";
import { com } from "../../../ui/layaMaxUI";
import { StringUtil } from "../../../util/StringUtil";
import { DialogNavShow } from "../../BaseDialog";
import UITab from "../../baseModules/UITab";
import { UITabData } from "../../baseModules/UITabData";
import { CommonBottomTabItem } from "../../common/CommonBottomTabItem";
import CommonCostItem1 from "../../common/CommonCostItem1";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { ModuleCommand } from "../../ModuleCommand";
import { GoodsManager } from "../../test_bag/GoodsManager";
import { AuctionHouseDataCenter, EAuctionHouseOpType } from "../data/AuctionHouseDataCenter";
import { Event } from "laya/events/Event";
import { m_auction_house_info_toc } from "../../../proto/line/m_auction_house_info_toc";
import { m_auction_house_op_tos } from "../../../proto/line/m_auction_house_op_tos";
import { m_auction_house_op_toc } from "../../../proto/line/m_auction_house_op_toc";

export default class AuctionHouseBiddingDialog extends com.ui.res.auctionHouse.AuctionHouseBiddingDialogUI {
    constructor() {
        super();
    }
    protected buyPrice: number = 0;

    protected maxPrice: number = 0;
    protected minPrice: number = 0;

    private info: p_auction_house_goods;

    private _goodsItem: GoodsItem;

    private _goodsVO: GoodsVO;

    private _id: number = 0;

    private _costItem: CommonCostItem1;

    /**紫币 */
    private costId: number = MiscConstAuto.auction_house_use_item_id;

    initUI(): void {
        this.navShow = DialogNavShow.NONE;
        this.topIns.titleNameTxt.text = "拍卖物品";

        this._goodsItem = this.goodsItem;
        this._goodsItem.mouseEnabled = false;

        this.costId = MiscConstAuto.auction_house_use_item_id;

        this.icon1.skin = UrlConfig.getGoodsIconByTypeId(this.costId);
        this.icon2.skin = UrlConfig.getGoodsIconByTypeId(this.costId);
        this.icon3.skin = UrlConfig.getGoodsIconByTypeId(this.costId);
        this.icon4.skin = UrlConfig.getGoodsIconByTypeId(this.costId);

        this._costItem = this.costItem;
        this._costItem.setTxtStyle({ greenColor: "#0c8614" });
        this._costItem.setBg("v2_common/tongyong_di_1.png", "14,14,14,14");

        this.slider.showLabel = false;
        this.slider.changeHandler = new Handler(this, () => {
            this.slider.changeValue();
        }, null, false);

    }


    addClick(): void {
        this.addOnClick(this, this.btnDel, this.onChangeBuyNum, Event.CLICK, [1], 0);
        this.addOnClick(this, this.btnAdd, this.onChangeBuyNum, Event.CLICK, [4], 0);
        this.addOnClick(this, this.btnAdd_max, this.onChangeBuyNum, Event.CLICK, [6], 0);

        this.slider.on(Event.CHANGE, this, this.onInputChange);

        this.addOnClick(this, this.btnFiexed, this.onClickBtnFixed);
        this.addOnClick(this, this.btnBid, this.onClickBtnBid);

    }

    addEvent(): void {
        this.addEventListener(ModuleCommand.GOODS_UPDATE, this, this.refreshCost);
        this.addEventListener(ModuleCommand.UPDATE_AUCTION_HOUSE_OP, this, this.refreshView);
    }

    refreshView(param: m_auction_house_op_toc) {
        if (param && param.op_type == EAuctionHouseOpType.UOPDATE_NEW &&
            param.auction_goods && this._id > 0 && param.auction_goods.id == this._id) {

            this.initView(param.auction_goods);
        }

    }

    onOpen(param: any): void {
        if (!param) {
            this.close();
            return;
        }

        this.refreshCost();
        this.initView(param);
    }

    initView(param: p_auction_house_goods) {
        if (!param) {
            return;
        }

        this.info = param;
        this._goodsVO = GoodsVO.GetVoByPGoods(this.info.goods);

        this._id = this.info.id;

        this.refreshItemView();

        this.initSlider();
    }

    refreshItemView() {
        if (this._goodsVO) {
            this._goodsItem.SetData(this._goodsVO);
            this.lbName.text = this._goodsVO.name;

            this.lbOwnerName.text = StringUtil.Format("上架人：[{0}服]{1}", this.info.shelve_role.server_id, this.info.shelve_role.role_name);

            this.inputBidPrice.text = this.info.bidding_price + "";

            this.inputFixedPrice.text = this.info.buyout_price + "";
            this.inputFixedPrice2.text = this.info.buyout_price + "";


        } else {
            this.lbName.text = "";
            this._goodsItem.Clean();
            this.lbOwnerName.text = ""

            this.inputBidPrice.text = "";

            this.inputFixedPrice.text = "";
            this.inputFixedPrice2.text = "";
        }
    }

    initSlider() {
        this.maxPrice = this.info.buyout_price;
        this.minPrice = this.info.bidding_price + 1;

        this.slider.max = this.maxPrice;
        this.slider.min = this.minPrice;

        this.buyPrice = this.minPrice;

        this.txtTotalMoney.text = this.buyPrice + "";
        this.slider.value = this.buyPrice;
    }

    /**改变购买数量 */
    onChangeBuyNum(type: number): void {
        switch (type) {
            case 1: //减1
                this.buyPrice -= 1;
                break;
            case 2: //减10
                this.buyPrice = this.buyPrice % 10 == 0 ? this.buyPrice -= 10 : Math.floor(this.buyPrice / 10) * 10;
                break;
            case 3: //最小
                this.buyPrice = this.minPrice;
                break;
            case 4: //加1
                this.buyPrice += 1;
                break;
            case 5: //加10
                this.buyPrice = (Math.floor(this.buyPrice / 10) + 1) * 10;
                break;
            case 6: //最大
                this.buyPrice = this.maxPrice;

                break;
        }
        if (this.buyPrice < this.minPrice) {
            this.buyPrice = this.minPrice;
        }

        if (this.buyPrice > this.maxPrice) {
            this.buyPrice = this.maxPrice;
        }

        this.txtTotalMoney.text = this.buyPrice + "";
        this.slider.value = this.buyPrice;


        if (this.buyPrice == this.maxPrice) {
            this.btnBid.label = "一口价";
        } else {
            this.btnBid.label = "竞价";
        }

    }

    private onInputChange(): void {
        this.buyPrice = this.slider.value || 1;
        if (this.buyPrice < 1) {
            this.buyPrice = 1;
        }

        if (this.buyPrice > this.maxPrice) {
            this.buyPrice = this.maxPrice;
        }

        if (this.buyPrice < this.minPrice) {
            this.buyPrice = this.minPrice;
        }

        this.txtTotalMoney.text = this.buyPrice + "";
        this.slider.value = this.buyPrice;

        if (this.buyPrice == this.maxPrice) {
            this.btnBid.label = "一口价";
        } else {
            this.btnBid.label = "竞价";
        }
    }


    refreshCost() {
        this._costItem.setData(this.costId, GoodsManager.instance.GetGoodsNumByTypeId(this.costId));

    }

    onClickBtnFixed() {
        AuctionHouseDataCenter.instance.auctionHouseOpTOS(
            EAuctionHouseOpType.BUY_FIXED,
            0,
            this._id,
            0,
            this.maxPrice
        )

    }

    onClickBtnBid() {
        const isBuyFixed = this.buyPrice == this.maxPrice;
        if (isBuyFixed) {
            this.onClickBtnFixed();
            return;
        }

        AuctionHouseDataCenter.instance.auctionHouseOpTOS(
            EAuctionHouseOpType.JOIN,
            0,
            this._id,
            this.buyPrice,
        )
    }
    


}
