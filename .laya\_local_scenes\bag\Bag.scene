{"type": "BaseDialog", "props": {"width": 720, "height": 1280}, "compId": 2, "child": [{"type": "Image", "props": {"var": "bgimg", "name": "bgimg", "centerY": 40, "centerX": 0}, "compId": 9}, {"type": "Image", "props": {"y": 44, "x": 351, "width": 522, "skin": "res/ui/bag/lihui2.png", "scaleY": 0.7, "scaleX": 0.7, "height": 382}, "compId": 25, "child": [{"type": "Image", "props": {"y": 123, "x": -431, "width": 818, "skin": "res/ui/bag/tips_shop.png", "scaleY": 0.7, "scaleX": 0.7, "height": 199}, "compId": 39}, {"type": "Label", "props": {"y": 175, "x": -406, "width": 442, "var": "txtTitle", "text": "领主，神秘商人有特价道具哟！", "height": 44, "fontSize": 34, "color": "#551511", "bold": false, "align": "center"}, "compId": 26}]}, {"type": "TopInsAuto", "props": {"y": 265, "x": 0, "visible": false, "var": "topIns", "height": 880, "runtime": "com/modules/common/TopInsAuto.ts"}, "compId": 19}, {"type": "Image", "props": {"y": 309, "x": 111, "width": 721, "skin": "common/bag_bg_01.png", "sizeGrid": "160,114,73,129", "height": 836}, "compId": 14}, {"type": "Box", "props": {"y": 442, "x": 48, "width": 644, "var": "goodBox", "height": 604}, "compId": 6, "child": [{"type": "VScrollBar", "props": {"y": 0, "x": 0, "visible": false, "top": 0, "skin": "common/vscroll.png", "right": 0, "name": "bar", "bottom": 0}, "compId": 10}]}, {"type": "Box", "props": {"y": 442, "x": 48, "width": 644, "visible": false, "var": "goodBox2", "name": "goodBox2", "height": 604}, "compId": 35, "child": [{"type": "VScrollBar", "props": {"y": 0, "x": 0, "visible": false, "top": 0, "skin": "common/vscroll.png", "right": 0, "name": "bar", "bottom": 0}, "compId": 36}]}, {"type": "Box", "props": {"y": 442, "x": 48, "width": 644, "var": "masterBox", "height": 604}, "compId": 46, "child": [{"type": "VScrollBar", "props": {"y": 0, "x": 0, "visible": false, "top": 0, "skin": "common/vscroll.png", "right": 0, "name": "bar", "bottom": 0}, "compId": 47}]}, {"type": "Box", "props": {"y": 332, "x": 45, "width": 633, "var": "p_tab", "height": 71}, "compId": 15, "child": [{"type": "HScrollBar", "props": {"skin": "common/hscroll.png", "right": 0, "name": "bar", "left": 0, "bottom": 0}, "compId": 24}]}, {"type": "<PERSON><PERSON>", "props": {"x": 605, "visible": false, "var": "closeBtn", "top": 190, "stateNum": 1, "skin": "common3/btn_back3.png", "name": "close", "anchorX": 0.5, "anchorY": 0.5}, "compId": 23}, {"type": "<PERSON><PERSON>", "props": {"y": 1082, "x": 595, "width": 152, "visible": false, "var": "btn_compound", "skin": "common/btnYellow.png", "pivotY": 32, "pivotX": 75.5, "name": "btn_compound", "labelStrokeColor": "#0d0d0d", "labelStroke": 1, "labelSize": 24, "labelColors": "#ffffff,#f9bb8e,#f9bb8e", "label": "一键合成", "height": 64, "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 31}, {"type": "<PERSON><PERSON>", "props": {"y": 343, "x": 8, "width": 29, "var": "btn_left", "stateNum": 1, "skin": "heroInfo/btn_left.png", "name": "btn_left", "height": 37}, "compId": 33}, {"type": "<PERSON><PERSON>", "props": {"y": 343, "x": 684, "width": 30, "var": "btn_right", "stateNum": 1, "skin": "heroInfo/btn_right.png", "name": "btn_right", "height": 37}, "compId": 34}, {"type": "<PERSON><PERSON>", "props": {"y": 1081, "x": 432, "width": 152, "visible": false, "var": "btn_getGodEquip", "skin": "common/btnGreen.png", "pivotY": 32, "pivotX": 75.5, "labelStrokeColor": "#0d0d0d", "labelStroke": 1, "labelSize": 24, "labelColors": "#ffffff,#f9bb8e,#f9bb8e", "label": "获取神装", "height": 64, "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 41}, {"type": "<PERSON><PERSON>", "props": {"y": 1082, "x": 595, "width": 152, "visible": false, "var": "btn_sellGodEquip", "skin": "common/btnYellow.png", "pivotY": 32, "pivotX": 75.5, "labelStrokeColor": "#0d0d0d", "labelStroke": 1, "labelSize": 24, "labelColors": "#ffffff,#f9bb8e,#f9bb8e", "label": "一键出售", "height": 64, "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 42}, {"type": "Image", "props": {"y": 1060, "x": 61, "visible": false, "var": "capacity_bg", "skin": "common/bg_scenf_01.png"}, "compId": 43, "child": [{"type": "Label", "props": {"y": 9, "x": 24, "var": "capacityLabel", "text": "185/275", "fontSize": 24, "color": "#ffffff"}, "compId": 44}]}, {"type": "<PERSON><PERSON>", "props": {"y": 1081, "x": 575, "width": 199, "visible": false, "var": "btnGet", "skin": "common/btnGreen.png", "pivotY": 32, "pivotX": 99.5, "labelStrokeColor": "#0d0d0d", "labelStroke": 1, "labelSize": 24, "labelColors": "#ffffff,#f9bb8e,#f9bb8e", "label": "获取领主装备", "height": 64, "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 48}], "loadList": ["bag/lihui2.png", "bag/tips_shop.png", "res/base/TopInsAuto.scene", "common/bag_bg_01.png", "common/vscroll.png", "common/hscroll.png", "common3/btn_back3.png", "common/btnYellow.png", "heroInfo/btn_left.png", "heroInfo/btn_right.png", "common/btnGreen.png", "common/bg_scenf_01.png"], "loadList3D": []}