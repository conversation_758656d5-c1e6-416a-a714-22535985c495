import { Handler } from "laya/utils/Handler";
import { com } from "../../../ui/layaMaxUI";
import { MapItemType, MapType } from "../../map/MapConst";
import { MapRole } from "../../map/view/MapRole";
import { MapView } from "../../map/view/MapView";
import { DialogLoadingProgress } from "../../DialogLoadingProgress";
import { MapRoleVo } from "../../map/vo/MapRoleVo";
import { DataCenter } from "../../DataCenter";
import { Point } from "laya/maths/Point";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { MapItemVo } from "../../map/vo/MapItemVo";
import { ModuleCommand } from "../../ModuleCommand";
import { MatchConst } from "../../../auto/ConstAuto";
import { cfg_world_secret_treasure_boss } from "../../../cfg/vo/cfg_world_secret_treasure_boss";
import WorldTreasureItem from "../view/WorldTreasureItem";
import { MapItem } from "../../map/view/MapItem";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { p_world_secret_treasure_gdn } from "../../../proto/common/p_world_secret_treasure_gdn";
import { WorldTreasureDataCenter, WorldTreasureFightType, WorldTreasureOpType } from "../data/WorldTreasureDataCenter";
import { cfg_world_secret_treasure_boss_group } from "../../../cfg/vo/cfg_world_secret_treasure_boss_group";
import { cfg_map_item } from "../../../cfg/vo/cfg_map_item";
import { p_simp_mission } from "../../../proto/common/p_simp_mission";
import { cfg_world_secret_treasure_mission } from "../../../cfg/vo/cfg_world_secret_treasure_mission";
import MissionConst, { MissionStatus } from "../../mission/MissionConst";
import { StringUtil } from "../../../util/StringUtil";
import { UrlConfig } from "../../../../game/UrlConfig";
import { ColorUtil } from "../../../util/ColorUtil";
import { HtmlUtil } from "../../../util/HtmlUtil";
import { UIHTMLDiv } from "../../baseModules/UIHTMLDiv";
import { TipsUtil } from "../../../util/TipsUtil";
import { FightDataCenter } from "../../fight/data/FightDataCenter";
import { LayerManager } from "../../../managers/LayerManager";
import { DialogNavShow } from "../../BaseDialog";
import { Tween } from "laya/utils/Tween";
import { ESkeletonAction } from "../../baseModules/skeleton/SkeletonData";
import { GSkeleton } from "../../baseModules/skeleton/GSkeleton";

export default class WorldTreasureDialog extends com.ui.res.worldTreasure.WorldTreasureDialogUI {


    private mapView: MapView;
    private myRole: MapRole;
    private halfrelativeHeight: number;
    private halfrelativeWidth: number;
    private worldTreasureCopyItemMap: Map<number, WorldTreasureItem>;
    private MAP_ID:number;
    /** 是否追踪角色 */
    private isTrackRole: boolean = true;
    private treasureBossId:number;

    private taskInfo:p_simp_mission;
    private missCfg:cfg_world_secret_treasure_mission;

    private _playAction: GSkeleton;
    constructor(){
        super();
        this.navShow = DialogNavShow.NONE;
        this.drawCallOptimize = false;
    }
    public initUI(): void {
        this.isShowBigBg = true;

        this.height = this.relativeHeight;
        this.y = 0;

        this.halfrelativeHeight = this.relativeHeight / 2;
        this.halfrelativeWidth = this.relativeWidth / 2;
        this.mapParent.height = this.height;
        this.locationBox.mouseThrough = true;
    }
    public addEvent(): void {
        this.addEventListener(ModuleCommand.MAP_CLICK, this, this.onMapClick);
        // this.addEventListener(ModuleCommand.FIGHT_FINISH_END, this, this.fightEnd);
        this.addEventListener(ModuleCommand.MAP_ITEM_CLICK, this, this.onMapItemClick);
        //添加秘宝守护者
        this.addEventListener(ModuleCommand.UPDATE_TREASURE_BOSS,this,this.updateWordTreasureBossItem);
        //删除秘宝守护者
        this.addEventListener(ModuleCommand.DELETE_TREASURE_BOSS,this,this.deleteWordTreasureBossItem);
        //任务更新
        this.addEventListener(ModuleCommand.UPDATE_WORLD_TREASURE_TASK_INFO,this,this.refreshTaskInfo);
        //宝藏出现
        this.addEventListener(ModuleCommand.UPDATE_TREASURE_SHOW_POS,this,this.showTreasureBox);
        //前往宝藏点
        this.addEventListener(ModuleCommand.GO_TO_TREASURE_POING,this,this.goToTreasurePos);
        //前往任务目标点
        this.addEventListener(ModuleCommand.GO_TO_TREASURE_TASK_TARGET_POING,this,this.goToTaskTargetPoint);
        this.addEventListener(ModuleCommand.UPDATE_TREASURE_TASK_REMAIN_TIMES,this,this.refreshTreasureRemainTimes);
    }
    public addClick(): void {
        this.addOnClick(this, this.tasksp, this.onMissionBtn);
        this.addOnClick(this, this.treasureMapBtn, this.onOpenTreasureMap);
        this.addOnClick(this,this.gotoBtn,this.goToLink)
    }
    public onOpen(param: any): void {
        if (LayerManager.rebackFight(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE)) {
            this.close();
            return;
        }
        this.MAP_ID = MiscConstAuto.world_secret_treasure_map_id;
        this.createrMap();
    }
    refreshTaskInfo() : void {
        this.taskInfo = WorldTreasureDataCenter.instance.taskMissionInfo;
        if (!this.taskInfo || WorldTreasureDataCenter.instance.isTaskAllCompleted()) {
            this.lbtitle.visible = false;
            this.gotoBtn.visible = false;
            this.lbDesc.text = "今日任务已完成";
            return;
        }
        this.missCfg = CfgCacheMapMgr.cfg_world_secret_treasure_missionCache.get(this.taskInfo.id);
        this.refreshMissionShow();
    }
    //-----------地图相关------------------
    private createrMap() {
        this.mapView = new MapView();
        this.mapParent.addChild(this.mapView);
        this.mapView.height = this.mapParent.height - 50;
        this.mapView.onOpen({
            map_type_id: MapType.MAP_TYPE_STAGE_COPY, map_id: this.MAP_ID,
            completeHandler: new Handler(this, this.onMapCreated),
        });
        DialogLoadingProgress.instance.Show(true);
        this.visible = false;
    }

    private onMapCreated(): void {
        this.visible = true;
        DialogLoadingProgress.instance.Close();
        
        this.addWorldTreasureItem();
        this.addRole();
        WorldTreasureDataCenter.instance.m_world_secret_treasure_op_tos(WorldTreasureOpType.ENTER_MAP);
        this.timer.frameLoop(1, this, this.onFrameUpdate);
    }
    /** 在场景上添加当前系统相关的item */
    private addWorldTreasureItem() {
        this.worldTreasureCopyItemMap = new Map<number, WorldTreasureItem>();
        this.mapView.itemPosMap.forEach((item, key) => {
            let bosscfg: cfg_world_secret_treasure_boss = CfgCacheMapMgr.cfg_world_secret_treasure_boss_mapCache.get(item.mapItemVo.itemCfg.item_id);
            if (bosscfg) {
                let copyItem: WorldTreasureItem = new WorldTreasureItem();
                copyItem.SetData(bosscfg, item);
                copyItem.pos(-50, -100);
                item.addChild(copyItem);
                this.worldTreasureCopyItemMap.set(bosscfg.id, copyItem);
            }
        })
    } 
    /** 在场景上添加秘宝守护者 */
    private updateWordTreasureBossItem(bossList:p_world_secret_treasure_gdn[]){
        if (!this.mapView) {
            return;
        }
        bossList.forEach((boosInfo:p_world_secret_treasure_gdn)=>{
            const bossId = boosInfo.id;
            if (this.worldTreasureCopyItemMap.has(bossId)) {
                let item = this.worldTreasureCopyItemMap.get(bossId);
                item.mapItem.mapItemVo.setShow(undefined);
                this.mapView.removeMapItem(item.mapItem.mapItemVo);
                this.worldTreasureCopyItemMap.delete(bossId);
                //数据上移除
                WorldTreasureDataCenter.instance.treasureBossMap.delete(bossId);
            }
            /**创建新的boss */
            this.createWordTreasureBossItem(boosInfo);

        });
        this.setLocationArrow();
    }
    private createPosFromInfo(posArray: number[]) {
        const x = posArray[0];
        const y = posArray[1];
        return {
            pos: Point.create().setTo(x, y),
            strPos: `${x}|${y}`
        };
    }
    private createWordTreasureBossItem(boosInfo:p_world_secret_treasure_gdn){
        const bossId = boosInfo.id;
        let cfgGroup:cfg_world_secret_treasure_boss_group = CfgCacheMapMgr.cfg_world_secret_treasure_boss_groupCache.get(boosInfo.type_id);
        if (!cfgGroup) {
            return;
        }
        const element_id = cfgGroup.element_id;
        const bosscfg: cfg_world_secret_treasure_boss = CfgCacheMapMgr.cfg_world_secret_treasure_bossCache.get(element_id);
        if (!bosscfg) {
            return;
        }
        const { pos, strPos } = this.createPosFromInfo(boosInfo.pos);
        this.mapView.addMapItemAndPos(bosscfg.map_item_id,pos,bossId);
        if (this.mapView.itemPosMap.has(strPos)) {
            const item: MapItem = this.mapView.itemPosMap.get(strPos);
            item.mapItemVo.setShow(true);
            let copyItem: WorldTreasureItem = new WorldTreasureItem();
            copyItem.SetData(bosscfg, item);
            copyItem.pos(-50, -100); // 设置位置偏移
            item.addChild(copyItem);
            this.worldTreasureCopyItemMap.set(bossId, copyItem);
            WorldTreasureDataCenter.instance.treasureBossMap.set(bossId,boosInfo);
        }
    }
    /** 在场景上删除秘宝守护者 */
    private deleteWordTreasureBossItem(bossList:p_world_secret_treasure_gdn[]){
        if (!this.mapView) {
            return;
        }
        bossList.forEach((boosInfo:p_world_secret_treasure_gdn)=>{
            const bossId = boosInfo.id;
            if (this.worldTreasureCopyItemMap.has(bossId)) {
                let item = this.worldTreasureCopyItemMap.get(bossId);
                item.mapItem.mapItemVo.setShow(undefined);
                this.mapView.removeMapItem(item.mapItem.mapItemVo);
                this.worldTreasureCopyItemMap.delete(bossId);
                //数据上移除
                WorldTreasureDataCenter.instance.treasureBossMap.delete(bossId);
            }
        });
        this.setLocationArrow();
    } 
    /** 在场景上显示/隐藏 宝藏宝箱 */
    private showTreasureBox(param:{pos:number[],isShow:boolean}){
        if (!this.mapView) {
            return;
        }
        const pos = param.pos;
        const isShow = param.isShow
        if (pos.length < 2) {
            return;
        }
        const strPos:string = pos[0] + "|" + pos[1];
        if (this.mapView.itemPosMap.has(strPos)) {
            const item = this.mapView.itemPosMap.get(strPos);
            item.mapItemVo.setShow(isShow);
            let bosscfg: cfg_world_secret_treasure_boss = CfgCacheMapMgr.cfg_world_secret_treasure_boss_mapCache.get(item.mapItemVo.itemCfg.item_id);
            if (bosscfg) {
                WorldTreasureDataCenter.instance.treasureElementId = bosscfg.id;
                this.dispatchEvent(ModuleCommand.UPDATE_TREASURE_SHOW_MAP);
            }
        }
    }
    /**前往宝藏点 */          
    private goToTreasurePos(){
        const pos = WorldTreasureDataCenter.instance.treasurePos;
        if (pos.length === 0) {
            return;
        }
        const strPos:string = pos[0] + "|" + pos[1];
        const item = this.mapView.itemPosMap.get(strPos);
        if (item) {
            this.RoleGoTo(item.mapItemVo,false);
        }
    }
    //------------------场景刷新相关-------------------------------
    private _tick: number = 0;
    private onFrameUpdate(): void {
        let interval_ms = this.timer.delta;
        let interval_s = interval_ms / 1000;

        this._tick += interval_ms;

        if (this._tick % 100 < interval_ms) {
            this.onFrameUpdate_100ms(100);
        }

        if (this.mapView && this.myRole && this.isTrackRole) {
            this.mapView.moveTileMapViewPort(this.myRole.x - this.halfrelativeWidth, this.myRole.y - this.halfrelativeHeight);
        }
    }

    private onFrameUpdate_100ms(interval_ms: number): void {
        if (this.worldTreasureCopyItemMap) {
            this.worldTreasureCopyItemMap.forEach(item => {
                item.checkinShow();
            });
        }
    }

     //----------------------角色显示相关---------------------------
     private addRole() {
        let pos = this.getRolePos();
        let roledata = this.getRoleData(DataCenter.myRoleID, DataCenter.myappearance, 0.5, pos, DataCenter.myRoleName);
        this.myRole = this.mapView.addRole(roledata);
        this.setLocationArrow();
    }
    private getRolePos():Point{
        if (!this.mapView) {
            return;
        }
        let pos = this.mapView.mapVo.mapCfg.birth_point.split("|").map(Number);
        if (WorldTreasureDataCenter.instance.playerFightPos) {
            pos = [WorldTreasureDataCenter.instance.playerFightPos.x, WorldTreasureDataCenter.instance.playerFightPos.y];
        }
        return new Point(pos[0],pos[1]);
    }
    private getRoleData(roleid: number, appearance: number, scaleSize: number, tile_pos: Point, roleName: string): MapRoleVo {
        let data: MapRoleVo = new MapRoleVo();
        data.roleid = roleid;
        data.appearance = appearance;
        data.scaleSize = scaleSize;
        data.tilePos = tile_pos;
        data.roleName = roleName;
        return data;
    }

     //-----------------------地图元素点击相关-----------------------
    /** 点击场景 */
    private onMapClick(pos: Point) {
        if (this.mapView) {
            this.timer.clear(this, this.fetchItem);
            this.mapView.roleMoveto(DataCenter.myRoleID, pos, new Handler(this, this.moveComplete),0.3);
            if (this.mapView.isCanMovePoint(DataCenter.myRoleID, pos)) {
                this.setLocationArrow();
            }
        }
    }

    /** 点击地图元素 */
    private onMapItemClick(itemvo: MapItemVo) {
        console.log("--cola-- ~ itemvo:", itemvo.itemCfg.item_id);
        this.RoleGoTo(itemvo);
    }
    /**设置方位箭头 */
    private setLocationArrow(){
        this.treasureBossId = this.getTreasureBossId();
        let item = this.worldTreasureCopyItemMap.get(this.treasureBossId);
        if (!item) {
            this.locationBox.visible = false;
            return;
        }
        const headUrl = UrlConfig.GOODS_RES_URL + item.bossCfg.monster_head + ".png";
        this.imgIcon.skin = headUrl;
        //是否在视野范围内
        const contains:boolean = item.mapItem.getIsShowInScreen();
        this.locationBox.visible = !contains;
        if (!contains) {
            const arrowPos = this.getEdgePoint({x:this.myRole.x,y:this.myRole.y},{x:item.mapItem.x,y:item.mapItem.y});
            if (arrowPos) {
                Tween.clearAll(this.locationBox);
                Tween.to(this.locationBox, { x: arrowPos.x, y: arrowPos.y }, 500);
            }
            this.imgLocation.rotation = this.calculateDirectionAngle(this.myRole.x,this.myRole.y,item.mapItem.x,item.mapItem.y);
        }
        else{
            this.locationBox.visible = false;
        }
    }
    /**
     * 计算目标方向与屏幕边缘的交点
     * @param playerPos 玩家世界坐标
     * @param targetPos 目标世界坐标
     * @param screenWidth 屏幕宽度（如 1280）
     * @param screenHeight 屏幕高度（如 720）
     * @returns 屏幕边缘交点 {x, y}，或 null（不可见）
     */
    getEdgePoint(
        playerPos: { x: number, y: number },
        targetPos: { x: number, y: number },
        ): { x: number, y: number } | null {
        const dx = targetPos.x - playerPos.x;
        const dy = targetPos.y - playerPos.y;
        const screenHeight = this.height;
        const screenWidth = this.width;
        if (dx === 0 && dy === 0) return null; // 同一点
        // 屏幕中心点（假设玩家在屏幕中心）
        const centerX = this.width / 2;
        const centerY = this.height / 2;
        // 方向向量（单位化）
        const length = Math.sqrt(dx * dx + dy * dy);
        const dirX = dx / length;
        const dirY = dy / length;
        // 求与四边的交点 t 值
        const tTop = (0 - centerY) / dirY;
        const tBottom = (screenHeight - centerY) / dirY;
        const tLeft = (0 - centerX) / dirX;
        const tRight = (screenWidth - centerX) / dirX;
        const intersections = [];
        // 上边
        if (tTop > 0) {
            const x = centerX + dirX * tTop;
            if (x >= 0 && x <= screenWidth) {
                intersections.push({ x, y: 0 });
            }
        }
        // 下边
        if (tBottom > 0) {
            const x = centerX + dirX * tBottom;
            if (x >= 0 && x <= screenWidth) {
                intersections.push({ x, y: screenHeight });
            }
        }
        // 左边
        if (tLeft > 0) {
            const y = centerY + dirY * tLeft;
            if (y >= 0 && y <= screenHeight) {
                intersections.push({ x: 0, y });
            }
        }
        // 右边
        if (tRight > 0) {
            const y = centerY + dirY * tRight;
            if (y >= 0 && y <= screenHeight) {
                intersections.push({ x: screenWidth, y });
            }
        }
        /**增加偏移量 */
        const ux = dx / length;
        const uy = dy / length;
        const pos = intersections.length > 0 ? intersections[0] : null;
        if (pos) {
            // 沿反方向移动 padding 像素
            const newX = pos.x - ux * 60;
            const newY = pos.y - uy * 55;
            return {x: newX, y:newY}
        }
        return null
    }
    /**
     * @returns 返回一个距离玩家最近的bossID
     */
    private getTreasureBossId(): number | undefined {
        if (WorldTreasureDataCenter.instance.treasureBossMap.size === 0) {
            return undefined;
        }
        let minDistanceSq = Infinity;
        let closestBossId: number | undefined = null;
        const playerPos = this.mapView.getTilePositionByPos(this.myRole.x, this.myRole.y);//真实坐标
        for (const [booId, boosInfo] of WorldTreasureDataCenter.instance.treasureBossMap) {
            const bossPos = boosInfo.pos;
            const dx = bossPos[0] - playerPos.x;
            const dy = bossPos[1] - playerPos.y;
            const distanceSq = dx * dx + dy * dy;
            if (distanceSq < minDistanceSq) {
                minDistanceSq = distanceSq;
                closestBossId = booId;
            }
        }
        return closestBossId;
    }
    /**
     * 计算从起始向量到目标向量的角度（0~360度）
     * @param startVector 起始向量
     * @param targetVector 目标向量
     * @returns 角度值（0~360度）
     */
     calculateDirectionAngle(x1: number, y1: number, x2: number, y2: number): number {
        const dx = x2 - x1;
        const dy = y2 - y1;
        const magnitude = Math.sqrt(dx * dx + dy * dy);
        const cosTheta = dx / magnitude;
        const sinTheta = dy / magnitude;
        let angle = Math.atan2(sinTheta, cosTheta) * (180 / Math.PI);
        //图片偏移
        angle -= 270;
        if (angle < 0) {
            angle += 360;
        }
        return angle;
    }
    private RoleGoTo(itemvo: MapItemVo,isTriggerCallback:boolean = true) {
        if (this.mapView) {
            this.timer.clear(this, this.fetchItem);
            const handler:Handler | null = isTriggerCallback ? new Handler(this, this.moveComplete, [itemvo]) : null;
            this.mapView.roleGoto(DataCenter.myRoleID, itemvo,handler, 0.3);
        }
    }

    /** 行走以后回调 */
    private moveComplete(itemvo: MapItemVo) {
        if (!itemvo || !itemvo.is_show) {
            return;
        }
        let mapItemcfg: cfg_map_item = CfgCacheMapMgr.cfg_map_itemCache.get(itemvo.itemCfg.item_id);
        let bosscfg: cfg_world_secret_treasure_boss = CfgCacheMapMgr.cfg_world_secret_treasure_boss_mapCache.get(itemvo.itemCfg.item_id);
        if (!bosscfg) {
            return;
        }
        let playerPos = this.mapView.getTilePositionByPos(this.myRole.x, this.myRole.y);//真实坐标，对应配置表的坐标
        if (mapItemcfg) {
            if (mapItemcfg.item_type === MapItemType.ITEM_TYPE_NPC) {
                //如果是接取状态接任务npc/任务将交付完成任务npc
                if ((this.missCfg.targetNpcId === bosscfg.id && this.taskInfo.status === MissionStatus.PRE_ACCEPT ) || (this.missCfg.completeNpcId === bosscfg.id && WorldTreasureDataCenter.instance.checkMissionComplete())) {
                    //对话
                    this.dispatchEvent(ModuleCommand.OPEN_WORLD_TREASURE_TALK_DIALOG, { data: bosscfg });
                    return;
                }
                else{
                    this.talkShow(bosscfg);
                }
            }
            else if (mapItemcfg.item_type === MapItemType.ITEM_TYPE_BOSS) {
                this.dispatchEvent(ModuleCommand.OPEN_WORLD_TREASURE_BOSS_BATTLE_DIALOG,{bossId:itemvo.soldId,playerPos:playerPos});
            }
            else if (mapItemcfg.item_type === MapItemType.ITEM_TYPE_SOLDIER) {
                if (this.missCfg.condition === bosscfg.map_item_id  && !WorldTreasureDataCenter.instance.checkMissionComplete() && this.taskInfo.status === MissionStatus.ACCEPT) {
                    this.enterFight(bosscfg);
                }
                else{
                    this.talkShow(bosscfg);
                }
            }
            else if (mapItemcfg.item_type === MapItemType.ITEM_TYPE_GOODS) {
                //采集物与任务条件是否相同，并且该任务没有完成
                if (this.missCfg.condition === bosscfg.id  && !WorldTreasureDataCenter.instance.checkMissionComplete() && this.taskInfo.status === MissionStatus.ACCEPT) {
                    this.collectGoods(bosscfg);
                }
                else{
                    this.talkShow(bosscfg);
                }
            }
            else if (mapItemcfg.item_type === MapItemType.ITEM_TYPE_SECTET_TREASURE) {
                WorldTreasureDataCenter.instance.m_world_secret_treasure_op_tos(WorldTreasureOpType.UNEARTh_TREASURE,[playerPos.x,playerPos.y]);
            }

        }
    }
    /**拾取道具 */
    private collectGoods(bosscfg: cfg_world_secret_treasure_boss){
        const worldTreasureItem:WorldTreasureItem = this.worldTreasureCopyItemMap.get(bosscfg.id);
        if (worldTreasureItem) {
            this._playAction = this.showGSkeleton(worldTreasureItem, "cszl_goods", this._playAction, { isLoop: false, x: 130, y: 50, isBoxCenter: false });
        }
        this.myRole.ChangeState(ESkeletonAction.ATTACK);
        this.clearTimer(this, this.fetchItem);
        this.timerOnce(1000, this, this.fetchItem, [bosscfg]);
    }
    private fetchItem(bosscfg: cfg_world_secret_treasure_boss) {
        if (this._playAction) {
            this._playAction.removeSelf();
            this._playAction.destroy();
            this._playAction = null;
        }
        this.myRole.ChangeState(ESkeletonAction.STAND);
        WorldTreasureDataCenter.instance.m_simp_mission_collect_tos(bosscfg.id);
    }
    private onOpenTreasureMap(){
        this.dispatchEvent(ModuleCommand.OPEN_WORLD_TREASURE_MAP_DIALOG);
    }
    /**任务点击 */
    private onMissionBtn() {
        if (WorldTreasureDataCenter.instance.isTaskAllCompleted()) {
            TipsUtil.showTips("今日秘宝任务已完成！")
            return;
        }
        this.dispatchEvent(ModuleCommand.OPEN_WORLD_TREASURE_TASK_DIALOG);
      
    }
    /**前往任务目标点 */          
    private goToTaskTargetPoint(strPos:string){
        if (!this.mapView) {
            return;
        }
        if (WorldTreasureDataCenter.instance.isTaskAllCompleted()) {
            return;
        }
        const item = this.mapView.itemPosMap.get(strPos);
        if (item && item.mapItemVo) {
            let mapItemcfg: cfg_map_item = CfgCacheMapMgr.cfg_map_itemCache.get(item.mapItemVo.itemCfg.item_id);
            const isTriggerCallback:boolean = mapItemcfg && mapItemcfg.item_type === MapItemType.ITEM_TYPE_NPC;
            this.RoleGoTo(item.mapItemVo,isTriggerCallback);
        }
    }
    private refreshMissionShow() {
        this.lbtitle.visible = true;
        this.gotoBtn.visible = true;
        if (this.missCfg) {
            this.lbDesc.text = this.missCfg.event_name;
            this.lbDesc.color = "#ffffff";
            if (this.taskInfo.status == MissionStatus.PRE_ACCEPT) {
                this.task_status.visible = false;
            } else if (WorldTreasureDataCenter.instance.checkMissionComplete()) {
                this.task_status.visible = false;
                this.lbDesc.color = "#2cc600";
                if (this.taskInfo.status == MissionStatus.ACC_REWARD && this.missCfg.event_type == MissionConst.MISSION_TYPE_COLLECT_GOOD) {
                    const str:string = this.missCfg.event_name + "(" + this.taskInfo.cur_progress + "/" + this.missCfg.max_progress + ")";
                    this.lbDesc.text = str;
                }
            } else {
                this.task_status.visible = true;
                if (this.missCfg.event_type == MissionConst.MISSION_TYPE_STAGE_COPY_MONSTER) {
                    this.task_status.skin = UrlConfig.STAGECOPY_ASSETS_URL + "cszl_45.png";
                } else if (this.missCfg.event_type == MissionConst.MISSION_TYPE_STAGE_COPY_TALK) {
                    this.task_status.skin = UrlConfig.STAGECOPY_ASSETS_URL + "cszl_46.png";
                } else if (this.missCfg.event_type == MissionConst.MISSION_TYPE_COLLECT_GOOD) {
                    const str:string = this.missCfg.event_name + "(" + this.taskInfo.cur_progress + "/" + this.missCfg.max_progress + ")";
                    this.lbDesc.text = str;
                    this.task_status.skin = UrlConfig.STAGECOPY_ASSETS_URL + "cszl_43.png";
                }
            }
        }
    }
    private refreshTreasureRemainTimes(){
        const completeTimes:number = MiscConstAuto.secret_treasure_mission_daily_times - WorldTreasureDataCenter.instance.remainTimes;
        this.lbtotalProgress.text = StringUtil.FormatArr("今日秘宝任务进度({0}/{1})",[completeTimes,MiscConstAuto.secret_treasure_mission_daily_times]);
        this.refreshTaskInfo();
    }
    goToLink(e:Event){
        e.stopPropagation && e.stopPropagation();
        if (this.taskInfo && this.missCfg) {
            if (this.taskInfo.status == MissionStatus.PRE_ACCEPT) {
                this.goToBoss(this.missCfg.targetNpcId);
            } else if (WorldTreasureDataCenter.instance.checkMissionComplete()) {
                this.goToBoss(this.missCfg.completeNpcId);
            } else {
                if (this.missCfg.event_type == MissionConst.MISSION_TYPE_STAGE_COPY_MONSTER || this.missCfg.event_type == MissionConst.MISSION_TYPE_COLLECT_GOOD) {
                    this.goToBoss(this.missCfg.condition);
                } else if (this.missCfg.event_type == MissionConst.MISSION_TYPE_STAGE_COPY_TALK) {
                    this.goToBoss(this.missCfg.completeNpcId);
                }else{

                }
            }
        }
    }

    private goToBoss(id: number) {
        let bosscfg: cfg_world_secret_treasure_boss = CfgCacheMapMgr.cfg_world_secret_treasure_bossCache.get(id);
        if (!bosscfg) {
            bosscfg = CfgCacheMapMgr.cfg_world_secret_treasure_boss_mapCache.get(id);
        }
        if (!bosscfg) {
            return;
        }
        let itemcfg = CfgCacheMapMgr.cfg_map_itemCache.get(bosscfg.map_item_id);
        if (itemcfg) {
            this.goToTaskTargetPoint(itemcfg.pos);
        }
    }
    private enterFight(bosscfg:cfg_world_secret_treasure_boss){
        let lineUpVo = WorldTreasureDataCenter.instance.createLineUpVo();
        let playerPos = this.mapView.getTilePositionByPos(this.myRole.x, this.myRole.y);//真实坐标，对应配置表的坐标
        FightDataCenter.instance.m_fight_start_tos(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE, bosscfg.id, {
            target_arg:[WorldTreasureFightType.MONSTER,playerPos.x,playerPos.y],
            lineUpVo:lineUpVo
        });
    }
    private talkShow(bosscfg: cfg_world_secret_treasure_boss) {
        let worldTreasureItem = this.worldTreasureCopyItemMap.get(bosscfg.id);
        if (worldTreasureItem) {
            worldTreasureItem.setTalk(bosscfg.talk);
        }
    }
}