import { Point } from "laya/maths/Point";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { cfg_map_item } from "../../../cfg/vo/cfg_map_item";
import { com } from "../../../ui/layaMaxUI";
import { GameUtil } from "../../../util/GameUtil";
import { GSkeleton } from "../../baseModules/skeleton/GSkeleton";
import { ESkeletonType } from "../../baseModules/skeleton/SkeletonData";
import { MapItemType } from "../../map/MapConst";
import { MapItem } from "../../map/view/MapItem";
import { MissionStatus } from "../../mission/MissionConst";
import { ModuleCommand } from "../../ModuleCommand";
import { cfg_world_secret_treasure_boss } from "../../../cfg/vo/cfg_world_secret_treasure_boss";
import { p_simp_mission } from "../../../proto/common/p_simp_mission";
import { cfg_world_secret_treasure_mission } from "../../../cfg/vo/cfg_world_secret_treasure_mission";
import { WorldTreasureDataCenter } from "../data/WorldTreasureDataCenter";
//界面类都是每次打开每次新建的
export default class WorldTreasureItem extends com.ui.res.stagecopy.StageCopyItemUI {
    private _bosscfg: cfg_world_secret_treasure_boss;
    private mapcfg: cfg_map_item;
    private _boxSk: GSkeleton;
    private _mapItem: MapItem;
    private taskInfo:p_simp_mission;
    private missCfg:cfg_world_secret_treasure_mission;
    constructor() {
        super();
        //有大底图的都要单独加载，不要打包到图集
    }
    isSelect: boolean;
    Clean(): void {
    }
    initUI(): void {
        this.bubbleBox.visible = false;
        this.missiontipspic.skin = "";
    }
    public get mapItem(){
        return this._mapItem
    }
    public get bossCfg(){
        return this._bosscfg;
    }
    /**添加事件**/
    addClick(): void {
        this.addOnClick(this, this.clickbtn, this.onClick);
    }
    addEvent(): void {
        // this.addEventListener(ModuleCommand.STAGECOPY_INFO, this, this.checkinShow);
        //任务更新
        this.addEventListener(ModuleCommand.UPDATE_WORLD_TREASURE_TASK_INFO,this,this.checkinShow);
    }

    private refreshData() {
        this.taskInfo = WorldTreasureDataCenter.instance.taskMissionInfo;
        if (!this.taskInfo || WorldTreasureDataCenter.instance.isTaskAllCompleted()) {
            return;
        }
        this.missCfg = CfgCacheMapMgr.cfg_world_secret_treasure_missionCache.get(this.taskInfo.id);
        if (this.mapcfg.item_type == MapItemType.ITEM_TYPE_GOODS) {
            if (this.missCfg.condition === this._bosscfg.id  && !WorldTreasureDataCenter.instance.checkMissionComplete() && this.taskInfo.status === MissionStatus.ACCEPT) {
                if (!this._boxSk) {
                    this._boxSk = GameUtil.createAndShowSkeleton("cszl_flash", ESkeletonType.UI_EFFECT, this, { isLoop: true, px: 120, py: 140 });
                }
            }
            else{
                this.cleanEffect();
            }
        }
        else if (this.mapcfg.item_type === MapItemType.ITEM_TYPE_BOSS) {
            if (!this._boxSk) {
                this._boxSk = GameUtil.createAndShowSkeleton("cszl_flash", ESkeletonType.UI_EFFECT, this, { isLoop: true, px: 120, py: 140 });
            }
        } 
        else if (this.mapcfg.item_type == MapItemType.ITEM_TYPE_SOLDIER) {
            if (this.missCfg.condition === this._bosscfg.map_item_id  && !WorldTreasureDataCenter.instance.checkMissionComplete() && this.taskInfo.status === MissionStatus.ACCEPT) {
                if (!this._boxSk) {
                    this._boxSk = GameUtil.createAndShowSkeleton("cszl_flash", ESkeletonType.UI_EFFECT, this, { isLoop: true, px: 120, py: 140 });
                }
            } else {
                this.cleanEffect();
            }
        }
        else if(this.mapcfg.item_type === MapItemType.ITEM_TYPE_NPC) {
            if (this.missCfg.targetNpcId === this._bosscfg.id && this.taskInfo.status === MissionStatus.PRE_ACCEPT ) {
                this.missiontipspic.skin = "stagecopy/npc_accept.png";
            }
            else if (this.missCfg.completeNpcId == this._bosscfg.id && WorldTreasureDataCenter.instance.checkMissionComplete()) {
                this.missiontipspic.skin = "stagecopy/npc_complete.png";
            } else {
                this.missiontipspic.skin = "";
            }
        }
    }

    public checkinShow() {
        if (this._mapItem && this._mapItem.visible && this._mapItem.isClose == false) {
            this.refreshData();
        } else {
            this.cleanEffect();
        }
    }

    private cleanEffect() {
        if (this._boxSk) {
            this._boxSk.removeSelf();
            this._boxSk.destroy();
            this._boxSk = null;
        }
    }

    private onClick(): void {
    }

    public setBtnlabel(label: string) {
    }

    public setTalk(talk:string) { 
        this.setTalkPos();
        this.bubbleBox.visible = true;
        this.bubble.text = talk;
        this.bubbleBox.height = Math.ceil(talk.length / 11) * 22 + 15;
        this.setDuration(3000);
    }

    public setDuration(duration: number): void {
        this.timer.once(duration, this, this.hideTalk);
    }
    private setTalkPos(){
        if (this.mapcfg.item_type === MapItemType.ITEM_TYPE_BOSS || this.mapcfg.item_type === MapItemType.ITEM_TYPE_NPC) {
            this.bubbleBox.pos(176,-16);
        }else{
            this.bubbleBox.pos(116,34);
        }
    }
    private hideTalk() { 
        this.bubbleBox.visible = false;
    }

    public SetData(cfgData: cfg_world_secret_treasure_boss, mapItem: MapItem): void {
        this._bosscfg = cfgData;
        this._mapItem = mapItem;
        this.mapcfg = CfgCacheMapMgr.cfg_map_itemCache.get(this._bosscfg.map_item_id);
        this.checkinShow();
    }
}