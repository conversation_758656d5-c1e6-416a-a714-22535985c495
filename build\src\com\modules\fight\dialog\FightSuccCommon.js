import { HTMLStyle } from "laya/html/utils/HTMLStyle";
import { Button } from "laya/ui/Button";
import { Label } from "laya/ui/Label";
import { GlobalConfig } from "../../../../game/GlobalConfig";
import { UrlConfig } from "../../../../game/UrlConfig";
import { LangConst } from "../../../auto/LangConst";
import { ConfigManager } from "../../../managers/ConfigManager";
import { LayerManager } from "../../../managers/LayerManager";
import { m_family_boss_fight_result_toc } from "../../../proto/line/m_family_boss_fight_result_toc";
import { m_fight_simp_result_toc } from "../../../proto/line/m_fight_simp_result_toc";
import { m_god_trial_fight_result_toc } from "../../../proto/line/m_god_trial_fight_result_toc";
import { m_modular_activity_maze_fight_result_toc } from "../../../proto/line/m_modular_activity_maze_fight_result_toc";
import { m_red_cliff_fight_result_toc } from "../../../proto/line/m_red_cliff_fight_result_toc";
import { m_story_maze_fight_result_toc } from "../../../proto/line/m_story_maze_fight_result_toc";
import { m_team_xswh_fight_result_toc } from "../../../proto/line/m_team_xswh_fight_result_toc";
import { m_test_tower_fight_result_toc } from "../../../proto/line/m_test_tower_fight_result_toc";
import { m_xswh_fight_result_toc } from "../../../proto/line/m_xswh_fight_result_toc";
import { com } from "../../../ui/layaMaxUI";
import { ColorUtil } from "../../../util/ColorUtil";
import { DateUtil } from "../../../util/DateUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { CommonButton } from "../../BaseDialog";
import UIButton from "../../baseModules/UIButton";
import { UIHTMLDiv } from "../../baseModules/UIHTMLDiv";
import CommonRewardList from "../../common/CommonRewardList";
import { CommonBuyTimesDataCenter } from "../../commonBuyTimes/data/CommonBuyTimesDataCenter";
import { CrossTestTowerDataCenter, CrossTowerType } from "../../crossTestTower/data/CrossTestTowerDataCenter";
import { GoodsVO } from "../../goods/GoodsVO";
import { LineUpMgr } from "../../lineUp/mgr/LineUpMgr";
import { MazeDataCenter } from "../../maze/data/MazeDataCenter";
import { MiscConst } from "../../misc_config/MiscConst";
import { SettingDataCenter } from "../../setting/data/SettingDataCenter";
import { StoryGameMgr } from "../../storyGame/StoryGameMgr";
import { TestTowerDataCenter, TowerType } from "../../testTower/data/TestTowerDataCenter";
import { FightDataCenter } from "../data/FightDataCenter";
import FightSuccTopIns from "../view/FightSuccTopIns";
import { TdTrialDataCenter } from "../../tdTrial/data/TdTrialDataCenter";
import { GuideMgr } from "../../guide/GuideMgr";
import { EBuyTimesType, MatchConst } from "../../../auto/ConstAuto";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { TestTowerUtil } from "../../testTower/util/TestTowerUtil";
import { StringUtil } from "../../../util/StringUtil";
export default class FightSuccCommon extends com.ui.res.fightsucc.FightSuccCommonUI {
    constructor() {
        super();
        // private randomBossFight: m_random_boss_fight_result_toc;
        // private worldBossFight: m_world_boss_fight_result_toc;
        this.match_type = 0;
        this._closeTick = 3;
        this._isDoNext = false;
        this.resName = UrlConfig.FIGHT_SUCC_RES;
        this.mouseThrough = true;
        this.navShow = 0;
    }
    getClassName() {
        return "FightSuccCommon";
    }
    checkGuide() {
        super.checkGuide();
    }
    onOpen(param) {
        this.autoCheck.visible = false;
        if (param instanceof m_fight_simp_result_toc) {
            this.match_type = param.match_type;
            this.setGoodsList(GoodsVO.GetPItemToVosHaveRate(param.rewards));
            if (param.is_crush) {
                this.labPass.visible = true;
                this.labPass.text = StringUtil.Format("本次战斗从{0}关挑战到{1}关", [param.crush_passes[0], param.crush_passes[1]]);
            }
            this.refreshFightNextBtn();
        }
        else if (param instanceof m_red_cliff_fight_result_toc) {
            this.match_type = MatchConst.MATCH_TYPE_RED_CLIFF;
            this.setGoodsList(GoodsVO.GetPItemToVosHaveRate(param.rewards));
            this.addRedCliffBtn(param);
        }
        else if (param instanceof m_test_tower_fight_result_toc) {
            if (param.type == TowerType.TOWER_TYPE_0) {
                this.match_type = MatchConst.MATCH_TYPE_TEST_TOWER;
            }
            else if (param.type == TowerType.TOWER_TYPE_1) {
                this.match_type = MatchConst.MATCH_TYPE_GHOST_TOWER;
            }
            else if (param.type == TowerType.TOWER_TYPE_2) {
                this.match_type = MatchConst.MATCH_TYPE_SKY_TOWER;
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_1) {
                this.match_type = MatchConst.MATCH_TYPE_NATION_TOWER_1;
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_2) {
                this.match_type = MatchConst.MATCH_TYPE_NATION_TOWER_2;
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_3) {
                this.match_type = MatchConst.MATCH_TYPE_NATION_TOWER_3;
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_4) {
                this.match_type = MatchConst.MATCH_TYPE_NATION_TOWER_4;
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_5) {
                this.match_type = MatchConst.MATCH_TYPE_NATION_TOWER_5;
            }
            this.setOtherGoodsList(param);
            if (param.type == TowerType.TOWER_TYPE_0 || param.type == TowerType.TOWER_TYPE_1 || param.type == TowerType.TOWER_TYPE_2) {
                this._fightSuccTopIns.returnBtn.visible = false;
                this._fightSuccTopIns.closeBtn.x = 220;
                if (TestTowerDataCenter.isCrushFight == false) {
                    this.addTestTowerBtn(param);
                }
            }
            else {
                if (CrossTestTowerDataCenter.isCrushFight == false) {
                    this.addTestTowerBtn(param);
                }
            }
            this.timeImg.visible = true;
            this.timeLabel.text = window.iLang.L2_TONG_GUAN_SHI_JIAN_ch05.il() + DateUtil.GetHMS(param.pass_time);
            if (param.type == TowerType.TOWER_TYPE_0) {
                let info = TestTowerDataCenter.instance.getInfo(param.type);
                if (info.fight_status == 1) {
                    this.autoCheck.selected = true;
                }
                else {
                    this.autoCheck.selected = false;
                }
                if (TestTowerDataCenter.isNotnewtest == true && info.cur_floor + 1 > MiscConstAuto.test_tower_auto_need_floor) {
                    this.autoCheck.visible = true;
                }
                else {
                    this.autoCheck.visible = false;
                    this.autoCheck.selected = false;
                }
            }
            else if (param.type == TowerType.TOWER_TYPE_1 || param.type == TowerType.TOWER_TYPE_2) {
                let info = TestTowerDataCenter.instance.getInfo(param.type);
                if (info.fight_status == 1) {
                    this.autoCheck.selected = true;
                }
                else {
                    this.autoCheck.selected = false;
                }
                if (TestTowerDataCenter.isNotnewtest == true) {
                    this.autoCheck.visible = true;
                }
                else {
                    this.autoCheck.visible = false;
                    this.autoCheck.selected = false;
                }
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_5 || param.type == CrossTowerType.CROSS_TOWER_TYPE_4 || param.type == CrossTowerType.CROSS_TOWER_TYPE_3 || param.type == CrossTowerType.CROSS_TOWER_TYPE_2 || param.type == CrossTowerType.CROSS_TOWER_TYPE_1) {
                let info = TestTowerDataCenter.instance.getInfo(param.type);
                if (info.fight_status == 1) {
                    this.autoCheck.selected = true;
                }
                else {
                    this.autoCheck.selected = false;
                }
                if (CrossTestTowerDataCenter.isNotnewtest == true) {
                    this.autoCheck.visible = true;
                }
                else {
                    this.autoCheck.visible = false;
                    this.autoCheck.selected = false;
                }
            }
        }
        else if (param instanceof m_family_boss_fight_result_toc) {
            this.match_type = MatchConst.MATCH_TYPE_FAMILY_BOSS;
            this.setGoodsList(GoodsVO.GetPItemToVosHaveRate(param.rewards));
            this.timeImg.visible = true;
            this.timeLabel.text = window.iLang.L2_ZUI_GAO_SHANG_HAI_ch05.il() + param.last_hurt;
        }
        else if (param instanceof m_xswh_fight_result_toc) {
            this.match_type = MatchConst.MATCH_TYPE_XSWH;
            this.setGoodsList(GoodsVO.GetPItemToVos(param.rewards));
            this.timeImg.visible = true;
            this.timeLabel.text = window.iLang.L2_ZUI_GAO_SHANG_HAI_ch05.il() + param.max_hurt;
        }
        else if (param instanceof m_team_xswh_fight_result_toc) {
            this.match_type = MatchConst.MATCH_TYPE_TEAM_XSWH;
            this.setGoodsList(GoodsVO.GetPItemToVos(param.rewards));
            this.timeImg.visible = true;
            this.timeLabel.text = window.iLang.L2_ZUI_GAO_SHANG_HAI_ch05.il() + param.max_hurt;
        }
        else if (param instanceof m_modular_activity_maze_fight_result_toc) {
            this.match_type = MatchConst.MATCH_TYPE_MAZE_ACT;
            let goodsVoList = GoodsVO.GetPItemToVos(param.rewards);
            this.lbEmptyTip.visible = goodsVoList.length <= 0;
            this.lbEmptyTip.text = window.iLang.L2_SHOU_TONG_JIANG_LI_WU_FA_CHONG_FU_HUO_QU.il();
            this.setGoodsList(goodsVoList);
        }
        else if (param instanceof m_story_maze_fight_result_toc) {
            this.match_type = MatchConst.MATCH_TYPE_STORY_MAZE;
            let goodsVoList = GoodsVO.GetPItemToVos(param.rewards);
            this.lbEmptyTip.visible = goodsVoList.length <= 0;
            this.lbEmptyTip.text = window.iLang.L2_SHOU_TONG_JIANG_LI_WU_FA_CHONG_FU_HUO_QU.il();
            this.setGoodsList(goodsVoList);
        }
        else if (param instanceof m_god_trial_fight_result_toc) {
            this.match_type = MatchConst.MATCH_TYPE_GOD_TRIAL;
            let isExitBattle = !!param["_exit_battle_"];
            this._fightSuccTopIns.isShowHuiFang = !isExitBattle;
            let goodsVoList = [];
            let rewards = param.daily_rewards;
            for (let i = 0; i < rewards.length; ++i) {
                let id = rewards[i].key;
                let num = rewards[i].val;
                goodsVoList.push(GoodsVO.GetVoByTypeId(id, num));
            }
            this.lbEmptyTip.visible = goodsVoList.length <= 0;
            this.lbEmptyTip.text = window.iLang.L2_JIN_RI_YI_HUO_DE_DANG_QIAN_BO_CI_JIANG_LI.il();
            this._fightSuccTopIns.closeBtn.x = 220;
            this._fightSuccTopIns.returnBtn.visible = false;
            this.setGoodsList(goodsVoList);
        }
        else {
            this.data = param;
            this.match_type = this.data.match_type;
        }
        if (this._fightSuccTopIns instanceof FightSuccTopIns) {
            this._fightSuccTopIns.setMatchType(this.match_type);
            this._fightSuccTopIns.setParentDialog(this);
            this._fightSuccTopIns.initFightClick(this);
            this._fightSuccTopIns.setMvpHeroInfo(this.match_type);
        }
        // if (this.match_type == GameConst.MATCH_TYPE_TM_BOSS_TEAM || this.match_type == GameConst.MATCH_TYPE_TM_MULTI_BOSS_TEAM) {
        //     this.tongji.visible = false;
        // }
        this._closeTick = MiscConst.battle_success_close_tick;
        if (param instanceof m_test_tower_fight_result_toc && (TestTowerDataCenter.isNotnewtest == true || CrossTestTowerDataCenter.isNotnewtest == true)) {
            this.timerLoop(1000, this, this.refreshCloseTick2);
        }
        else {
            this.timerLoop(1000, this, this.refreshCloseTick);
        }
        // if (this.match_type == GameConst.MATCH_TYPE_TEST_TOWER || this.match_type == GameConst.MATCH_TYPE_SP_TEST_TOWER || this.match_type == GameConst.MATCH_TYPE_SKY_TEST_TOWER) {
        //     if (param instanceof m_test_tower_fight_result_toc && TestTowerDataCenter.isNotnewtest == true) {
        //         this.timerLoop(1000, this, this.refreshCloseTick2);
        //     } else {
        //         this.timerLoop(1000, this, this.refreshCloseTick);
        //     }
        // } else
        // if (this.match_type == GameConst.MATCH_TYPE_NATION_TOWER_1 || this.match_type == GameConst.MATCH_TYPE_NATION_TOWER_2 || this.match_type == GameConst.MATCH_TYPE_NATION_TOWER_3 || this.match_type == GameConst.MATCH_TYPE_NATION_TOWER_4 || this.match_type == GameConst.MATCH_TYPE_NATION_TOWER_5) {
        //     if (param instanceof m_test_tower_fight_result_toc && CrossTestTowerDataCenter.isNotnewtest == true) {
        //         this.timerLoop(1000, this, this.refreshCloseTick2);
        //     } else {
        //         this.timerLoop(1000, this, this.refreshCloseTick);
        //     }
        // }
        if (this.match_type == MatchConst.MATCH_TYPE_CONTEST || this.match_type == MatchConst.MATCH_TYPE_CROSS_FRIEND_CONTEST) {
            this.lbCloseTick.visible = false;
        }
        else if (this.match_type == MatchConst.MATCH_TYPE_TD_TRIAL) {
            if (param.is_success == true) {
                this.clearTimer(this, this.refreshCloseTick);
                this.lbCloseTick.text = window.iLang.L2_P0_MIAO_HOU_JIN_RU_XIA_YI_GUAN.il([this._closeTick]);
                this.timerLoop(1000, this, this.refreshCloseTd);
                this._fightSuccTopIns.returnBtn.label = window.iLang.L2_TIAO_ZHAN_XIA_YI_CENG.il();
            }
        }
        else if (this.match_type == MatchConst.MATCH_TYPE_TD_MAIN) {
            if (param.is_success == true) {
                this._fightSuccTopIns.closeBtn.visible = false;
                this._fightSuccTopIns.returnBtn.x = 220;
                // this._fightSuccTopIns.clickTipsIcon.visible = false;
                this.setGoodsListHeight(450);
                this.clearTimer(this, this.refreshCloseTick);
                this.lbCloseTick.text = "";
                // this.timerLoop(1000, this, this.refreshCloseTd);
                this._fightSuccTopIns.returnBtn.label = window.iLang.L2_XIA_YI_GUAN.il();
                // if (TdMainDataCenter.ins.isGuideAllFinish == false) {
                this._fightSuccTopIns.returnBtn.visible = false;
                // }
            }
        }
        else if (this.match_type == MatchConst.MATCH_TYPE_TD_DAILY_COPY) {
            if (param.is_success == true) {
                this._fightSuccTopIns.closeBtn.visible = false;
                this._fightSuccTopIns.returnBtn.x = 220;
            }
        }
        let matchTypeCfg = ConfigManager.cfg_match_typeCache.get(this.match_type);
        this.matchTypeLabel.text = FightDataCenter.getMatchName(this.match_type);
        let stat_list = FightDataCenter.FIGHT_STATS_INFO_CACHE.get(this.match_type);
        let hasFightData = stat_list && stat_list.length > 0;
        this.tongji.visible = matchTypeCfg && matchTypeCfg.is_can_analy == 1 && hasFightData;
        let isExitBattle = !!param["_exit_battle_"];
        this.huifang.visible = !isExitBattle
            && hasFightData
            && matchTypeCfg.is_can_review == 1
            && !GlobalConfig.isReviewStaus_w9
            && StoryGameMgr.ins.isReview() == false;
        let currGuide = GuideMgr.ins.curCfg;
        let isHide = false;
        if (currGuide && currGuide.ui_name != this.name) {
            isHide = true;
        }
        GuideMgr.ins.setHideGuideUI(isHide, this.name);
    }
    refreshCloseTd() {
        if (TdTrialDataCenter.ins.autoSel) {
            this.lbCloseTick.visible = true;
            this.lbCloseTick.text = window.iLang.L2_P0_MIAO_HOU_JIN_RU_XIA_YI_GUAN.il([this._closeTick -= 1]);
            if (this._closeTick <= 0) {
                // this.dispatchEvent(ModuleCommand.TD_NEXT_FIGHT);
                this.close();
            }
        }
        else {
            this.lbCloseTick.visible = true;
            this.lbCloseTick.text = window.iLang.L2_P0_MIAO_HOU_GUAN_BI.il([this._closeTick -= 1]);
            if (this._closeTick <= 0) {
                this.close();
            }
        }
    }
    refreshCloseTick() {
        if (this.visible == false) {
            return;
        }
        let lineUpVo = LineUpMgr.instance.getLineUpVo(this.match_type);
        if (lineUpVo && lineUpVo.isShowAuto && this.autoCheck.selected) {
            this.lbCloseTick.visible = false;
            this.autoCheck.label = window.iLang.L2_P0_MIAO_HOU_JIN_RU_XIA_YI_GUAN.il([this._closeTick -= 1]);
            if (this._closeTick <= 0) {
                if (lineUpVo.checkIsCanAuto(true)) {
                    this.onClickBtnNext();
                }
                else {
                    this.close();
                }
            }
        }
        else {
            this.lbCloseTick.visible = true;
            this.lbCloseTick.text = window.iLang.L2_P0_MIAO_HOU_GUAN_BI.il([this._closeTick -= 1]);
            if (this._closeTick <= 0) {
                this.close();
            }
        }
    }
    refreshCloseTick2() {
        // if (GlobalConfig.isYeGame) {
        //     return ;
        // }
        //有可能正在查看其他内容比如统计列表等,这里就会隐藏,隐藏的时候不再即时,以防自动关闭 统计界面
        if (this.visible == false) {
            return;
        }
        if (this.autoCheck.selected) {
            this.lbCloseTick.visible = false;
            this.autoCheck.label = window.iLang.L2_P0_MIAO_HOU_JIN_RU_XIA_YI_GUAN.il([this._closeTick -= 1]);
            if (this._closeTick <= 0) {
                this.OnClickTestTowerBtnDoNext();
            }
        }
        else {
            this.lbCloseTick.visible = true;
            this.autoCheck.label = window.iLang.L2_DIAN_JI_KAI_QI_ZI_DONG_ZHAN_DOU.il();
            this.lbCloseTick.text = window.iLang.L2_P0_MIAO_HOU_GUAN_BI.il([this._closeTick -= 1]);
            if (this._closeTick <= 0) {
                // this.close();
                if (TestTowerDataCenter.instance.auto_succ_info != null) {
                    this.lbCloseTick.text = LangConst.L2_P0_MIAO_HOU_GUAN_BI.il(0);
                    this.dispatchEvent("OPEN_TEST_TOWER_AUTO_SUCC_DIALOG" /* OPEN_TEST_TOWER_AUTO_SUCC_DIALOG */, TestTowerDataCenter.instance.auto_succ_info);
                    TestTowerDataCenter.instance.auto_succ_info = null;
                }
                else {
                    this.close();
                }
            }
        }
    }
    refreshFightNextBtn() {
        if (this._fightSuccTopIns instanceof FightSuccTopIns) {
            let lineUpVo = LineUpMgr.instance.getLineUpVo(this.match_type);
            if (lineUpVo && lineUpVo.isShowAuto) {
                this._fightSuccTopIns.nextBtn.visible = true;
                this._fightSuccTopIns.returnBtn.visible = false;
                this.autoCheck.visible = true;
                this.autoCheck.label = window.iLang.L2_DIAN_JI_KAI_QI_ZI_DONG_ZHAN_DOU.il();
                this.autoCheck.selected = lineUpVo.checkIsCanAuto();
                this.addOnClick(this, this._fightSuccTopIns.nextBtn, this.onClickBtnNext);
            }
        }
    }
    setGoodsListHeight(height) {
        this._uiList.height = height;
        this._fightSuccTopIns.height = height + 250;
        this._fightSuccTopIns.btnGrp.height = height - 140;
    }
    addTestTowerBtn(vo) {
        let btn1 = new Button(CommonButton.BtnYellow, window.iLang.L_OK.il());
        btn1.labelSize = 24;
        btn1.labelColors = "#50361d,#50361d,#50361d";
        btn1.stateNum = 3;
        btn1.anchorX = 0.5;
        btn1.anchorY = 0.5;
        btn1.width = 160;
        btn1.height = 60;
        UIButton.ResetButtonLabel(btn1);
        this._fightSuccTopIns.addChild(btn1);
        btn1.left = 154;
        btn1.bottom = 40;
        this.addOnClick(this, btn1, this.OnClickTestTowerBtnDo);
        let length = ConfigManager.cfg_test_towerCache.size;
        let info = TestTowerDataCenter.instance.getInfo(vo.type);
        let isMaxFloor = length < info.cur_floor + 1;
        if (!isMaxFloor && vo.is_highest) {
            let btn2 = new Button(CommonButton.BtnGreen, window.iLang.L2_TIAO_ZHAN_XIA_YI_CENG.il());
            btn2.labelSize = 24;
            btn2.labelColors = "#50361d,#50361d,#50361d";
            btn2.stateNum = 3;
            btn2.anchorX = 0.5;
            btn2.anchorY = 0.5;
            btn2.width = 160;
            btn2.height = 60;
            this._fightSuccTopIns.addChild(btn2);
            UIButton.ResetButtonLabel(btn2);
            btn2.right = 154;
            btn2.bottom = 40;
            this.addOnClick(this, btn2, this.OnClickTestTowerBtnDoNext);
            this._fightSuccTopIns.addChild(btn2);
            this._fightSuccTopIns.closeBtn.visible = false;
            this._fightSuccTopIns.returnBtn.visible = false;
        }
        else {
            btn1.visible = false;
        }
        this.showPrivilegebg.visible = true;
        this.showOtherDesc.visible = true;
        this.showOtherDesc.innerHTML = TestTowerDataCenter.instance.GetFloorRewadStr(vo.challenge_floor);
    }
    addRedCliffBtn(vo) {
        let btn1 = new Button(CommonButton.BtnYellow, window.iLang.L_OK.il());
        btn1.labelSize = 24;
        btn1.labelColors = "#50361d,#50361d,#50361d";
        btn1.stateNum = 3;
        btn1.anchorX = 0.5;
        btn1.anchorY = 0.5;
        btn1.width = 160;
        btn1.height = 60;
        UIButton.ResetButtonLabel(btn1);
        this._fightSuccTopIns.addChild(btn1);
        btn1.left = 154;
        btn1.bottom = 40;
        this.addOnClick(this, btn1, this.OnClickTestTowerBtnDo);
        let btn2 = new Button(CommonButton.BtnGreen, window.iLang.L2_TIAO_ZHAN_XIA_YI_CENG.il());
        btn2.labelSize = 24;
        btn2.labelColors = "#50361d,#50361d,#50361d";
        btn2.stateNum = 3;
        btn2.anchorX = 0.5;
        btn2.anchorY = 0.5;
        btn2.width = 160;
        btn2.height = 60;
        this._fightSuccTopIns.addChild(btn2);
        UIButton.ResetButtonLabel(btn2);
        btn2.right = 154;
        btn2.bottom = 40;
        this.addOnClick(this, btn2, this.OnClickRedCliffBtnDoNext);
        this._fightSuccTopIns.addChild(btn2);
        this._fightSuccTopIns.closeBtn.visible = false;
        this._fightSuccTopIns.returnBtn.visible = false;
        // }
        // else {
        //btn1.x = 360;
        //}
        // this.showPrivilegebg.visible = true;
        // this.showOtherDesc.visible = true;
        // this.showOtherDesc.innerHTML = TestTowerDataCenter.instance.GetFloorRewadStr(vo.challenge_floor);
    }
    OnClickTestTowerBtnDoNext() {
        this._isDoNext = true;
        // if(this.autoCheck.selected){
        //     this.dispatchEvent(ModuleCommand.FIGHT_TEST_TOWER_NEXT_FLOOR, true);
        // } else{
        if (this.match_type == MatchConst.MATCH_TYPE_TEST_TOWER || this.match_type == MatchConst.MATCH_TYPE_GHOST_TOWER || this.match_type == MatchConst.MATCH_TYPE_SKY_TOWER) {
            this.dispatchEvent("FIGHT_TEST_TOWER_NEXT_FLOOR" /* FIGHT_TEST_TOWER_NEXT_FLOOR */, this.match_type);
        }
        else if (this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_1 || this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_2 || this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_3 || this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_4 || this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_5) {
            this.dispatchEvent("FIGHT_CROSS_TEST_TOWER_NEXT_FLOOR" /* FIGHT_CROSS_TEST_TOWER_NEXT_FLOOR */, this.match_type);
        }
        // }
        this.close();
    }
    OnClickTestTowerBtnDo() {
        let towerType = TestTowerUtil.matchTypeToTowerType(this.match_type);
        let isTestTower = TestTowerUtil.isTestTower(towerType);
        let info = TestTowerDataCenter.instance.getInfo(towerType);
        let auto_succ_info = isTestTower ? TestTowerDataCenter.instance.auto_succ_info : CrossTestTowerDataCenter.instance.auto_succ_info;
        if (auto_succ_info && info && info.cur_floor == auto_succ_info.end_floor) {
            this.dispatchEvent("OPEN_TEST_TOWER_AUTO_SUCC_DIALOG" /* OPEN_TEST_TOWER_AUTO_SUCC_DIALOG */, auto_succ_info);
        }
        else {
            if (typeof (towerType) == "number") {
                SettingDataCenter.instance.m_test_tower_op_tos(towerType, 1, 0);
            }
            this.close();
        }
    }
    OnClickRedCliffBtnDoNext() {
        let timesVo = CommonBuyTimesDataCenter.instance.getBuyTimesVoByType(EBuyTimesType.RED_CLIFF);
        if (timesVo && timesVo.remain_times > 0) {
            this._isDoNext = true;
        }
        this.dispatchEvent("FIGHT_RED_CLIFF_NEXT_FLOOR" /* FIGHT_RED_CLIFF_NEXT_FLOOR */);
        this.close();
    }
    onClickBtnNext() {
        this.dispatchEvent("FIGHT_NEXT_PASS" /* FIGHT_NEXT_PASS */ + this.match_type, this.match_type);
    }
    /**世界boss 造成伤害显示 */
    setWorldBossHurt() {
        let lbHurt = new Label();
        lbHurt.fontSize = 22;
        lbHurt.color = ColorUtil.FONT_ORANGE;
        lbHurt.centerX = 0;
        lbHurt.y = 397;
        let lbTotalHurt = new Label();
        lbTotalHurt.fontSize = 22;
        lbTotalHurt.color = ColorUtil.FONT_ORANGE;
        lbTotalHurt.centerX = 0;
        lbTotalHurt.y = 432;
        lbHurt.text = window.iLang.L2_BEN_CI_SHANG_HAI_ch10.il() + 0;
        lbTotalHurt.text = window.iLang.L2_ZONG_JI_SHANG_HAI_ch10.il() + 0;
        this.addChild(lbHurt);
        this.addChild(lbTotalHurt);
    }
    setGoodsList(goodsVos) {
        this.titleIcon.visible = true;
        this.list.visible = true;
        if (goodsVos.length > 0) {
            if (this._uiList == null) {
                this._uiList = new CommonRewardList();
                this._uiList.size(this.list.width, this.list.height);
                this.list.addChild(this._uiList);
            }
            this._uiList.setData(goodsVos, this.parameter);
        }
    }
    setOtherGoodsList(param) {
        let goodsVos = [];
        // if (param instanceof m_team_boss_fight_result_toc) {
        //     for (let index = 0; index < param.rewards.length; index++) {
        //         const item: p_item = param.rewards[index];
        //         goodsVos.push(GoodsVO.GetVoByTypeId(item.type_id, item.num));
        //         if (param.match_type == GameConst.MATCH_TYPE_TM_MULTI_BOSS_TEAM && param.role_num >= 3) {
        //             this.parameter = { showTip1: true }
        //         }
        //     }
        // }
        if (param instanceof m_test_tower_fight_result_toc) {
            if (param.type == TowerType.TOWER_TYPE_0) {
                if (param.is_highest == true) {
                    if (param.rewards.length > 3) {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                    }
                    else {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                    }
                }
                else {
                    this.parameter = { show_imgZhen: false, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                }
                goodsVos = GoodsVO.GetPItemToVosHaveRate(param.rewards);
            }
            else if (param.type == TowerType.TOWER_TYPE_1) {
                if (param.is_highest == true) {
                    this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                }
                else {
                    this.parameter = { show_imgZhen: false, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                }
                goodsVos = GoodsVO.GetPItemToVosHaveRate(param.rewards);
            }
            else if (param.type == TowerType.TOWER_TYPE_2) {
                if (param.is_highest == true) {
                    this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                }
                else {
                    this.parameter = { show_imgZhen: false, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                }
                goodsVos = GoodsVO.GetPItemToVosHaveRate(param.rewards);
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_1) {
                if (param.is_highest == true) {
                    if (param.rewards.length > 3) {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                    }
                    else {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                    }
                }
                else {
                    this.parameter = { show_imgZhen: false, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                }
                goodsVos = GoodsVO.GetPItemToVosHaveRate(param.rewards);
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_2) {
                if (param.is_highest == true) {
                    if (param.rewards.length > 3) {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                    }
                    else {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                    }
                }
                else {
                    this.parameter = { show_imgZhen: false, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                }
                goodsVos = GoodsVO.GetPItemToVosHaveRate(param.rewards);
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_3) {
                if (param.is_highest == true) {
                    if (param.rewards.length > 3) {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                    }
                    else {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                    }
                }
                else {
                    this.parameter = { show_imgZhen: false, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                }
                goodsVos = GoodsVO.GetPItemToVosHaveRate(param.rewards);
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_4) {
                if (param.is_highest == true) {
                    if (param.rewards.length > 3) {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                    }
                    else {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                    }
                }
                else {
                    this.parameter = { show_imgZhen: false, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                }
                goodsVos = GoodsVO.GetPItemToVosHaveRate(param.rewards);
            }
            else if (param.type == CrossTowerType.CROSS_TOWER_TYPE_5) {
                if (param.is_highest == true) {
                    if (param.rewards.length > 3) {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 2 };
                    }
                    else {
                        this.parameter = { show_imgZhen: true, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                    }
                }
                else {
                    this.parameter = { show_imgZhen: false, title: window.iLang.L2_SHOU_TONG.il(), show_imgZhennum: 1 };
                }
                goodsVos = GoodsVO.GetPItemToVosHaveRate(param.rewards);
            }
        }
        this.setGoodsList(goodsVos);
    }
    // private updateReward(param: any): void {
    //     if (param.get_type != GameConst.MATCH_TYPE_DAILY_FB)
    //         return;
    //     this.titleIcon.visible = true;
    //     this.list.visible = true;
    //     let goodsVos: GoodsVO[] = param.goods;
    //     if (goodsVos.length > 0) {
    //         if (this._uiList == null) {
    //             this._uiList = new CommonRewardList();
    //             this._uiList.size(this.list.width, 140);
    //             this.list.addChild(this._uiList);
    //         }
    //         this._uiList.setData(goodsVos);
    //     }
    // }
    initUI() {
        this._fightSuccTopIns = this.fightSuccTopIns;
        this.titleIcon.visible = false;
        this.list.visible = false;
        this.showPrivilegebg.visible = false;
        this.showPrivilege.visible = false;
        this.showOtherDesc.visible = false;
        UIHTMLDiv.SetUIHTMLDiv(this.showPrivilege, 22, ColorUtil.FONT_GREEN, 8, HTMLStyle.ALIGN_CENTER);
        UIHTMLDiv.SetUIHTMLDiv(this.showOtherDesc, 22, "#ffffff", 8, HTMLStyle.ALIGN_CENTER);
        // this.tongji.visible = false;
        //this._uiList = UIList.SetUIList(this, this.list, GoodsItem);
        //this._uiList.isBoxCenter = true;
        //this._uiList.SetRepeat(6, 1);
        //this._uiList.SetSpace(20);
    }
    addClick() {
        // this.addOnClick(this, this.tongji, this.onClickAnaly);
        // this.addOnClick(this, this.huifang, this.onClickReview);
        this.addOnClick(this, this.autoCheck, this.onClickAutoCheck);
    }
    onClickAutoCheck() {
        let val = this.autoCheck.selected ? 1 : 0;
        if (this.match_type == MatchConst.MATCH_TYPE_TEST_TOWER || this.match_type == MatchConst.MATCH_TYPE_GHOST_TOWER || this.match_type == MatchConst.MATCH_TYPE_SKY_TOWER) {
            SettingDataCenter.instance.m_test_tower_op_tos(TestTowerDataCenter.instance.nowTowerType, 1, val);
        }
        else if (this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_1 || this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_2 || this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_3 || this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_4 || this.match_type == MatchConst.MATCH_TYPE_NATION_TOWER_5) {
            SettingDataCenter.instance.m_test_tower_op_tos(CrossTestTowerDataCenter.instance.nowTowerType, 1, val);
        }
        else {
            let lineUpVo = LineUpMgr.instance.getLineUpVo(this.match_type);
            if (lineUpVo && lineUpVo.checkIsCanClickAuto(true)) {
                lineUpVo.setFightAuto(this.autoCheck.selected, false);
            }
        }
    }
    onClickAnaly() {
        this.dispatchEvent("OPEN_FIGHT_STAT_DIALOG" /* OPEN_FIGHT_STAT_DIALOG */, this.match_type);
    }
    onClickReview() {
        LayerManager.toReview(this.match_type);
        this.close("hold");
    }
    addEvent() {
        //    this.addEventListener(ModuleCommand.CLOSE_ALL_SHARE_DIALOG,this,this.close)
    }
    trueClose(type = null) {
        super.close(type);
    }
    destroy(destroyChild = true) {
        super.destroy(destroyChild);
        TipsUtil.clearPopUpWindows();
        GuideMgr.ins.setHideGuideUI(false, this.name);
        this.checkNavShow(true);
    }
    close(type = null) {
        this.trueClose(type);
    }
    onClosed(type = null) {
        if (type != "hold") {
            // if (this.match_type == GameConst.MATCH_TYPE_TM_MULTI_BOSS_TEAM && type == Dialog.CANCEL) {
            //     return;
            // }
            if (LayerManager.runningFight(this.match_type)) {
                if (type != "cancel" && (this._isDoNext || !FightDataCenter.instance.checkFightClose(this.match_type))) {
                }
            }
            else {
                //主题玩法特殊处理
                if (this.match_type == MatchConst.MATCH_TYPE_MAZE_ACT) {
                    let bigType = MazeDataCenter.instance.fightToc_big_type;
                    let dataVo = MazeDataCenter.instance.getDataVo(bigType);
                    if (dataVo) {
                        dataVo.isStopChoose = false;
                    }
                    if (MazeDataCenter.instance.isCanChooseSpoils(bigType)) {
                        this.dispatchEvent("OPEN_CHOOSE_SPOILS_DIALOG" /* OPEN_MAZE_CHOOSE_SPOILS_DIALOG */, { big_type: bigType });
                    }
                }
                else if (this.match_type == MatchConst.MATCH_TYPE_TD_DAILY_COPY) {
                    this.dispatchEvent("CLOSE_TD_DAILY_COPY_DIALOG" /* CLOSE_TD_DAILY_COPY_DIALOG */);
                }
            }
            // this.dispatchEvent(ModuleCommand.ON_FIGHT_SUCC_DIALOG_CLOSED,this.match_type);
        }
    }
}
