{"type": "BaseDialog", "props": {"width": 2500, "height": 1280}, "compId": 2, "child": [{"type": "Box", "props": {"y": 0, "var": "boxEditor", "left": 0, "mouseThrough": true}, "compId": 121, "child": [{"type": "Image", "props": {"y": 0, "x": -7, "width": 734, "skin": "common/img_13.png", "height": 65, "sizeGrid": "4,4,4,4"}, "compId": 88}, {"type": "BattleEditorRoleNameItem", "props": {"y": 17, "x": 40, "var": "uiLeftRoleNameItem", "runtime": "com.ui.res.w3.BattleEditorRoleNameItemUI"}, "compId": 99}, {"type": "<PERSON><PERSON>", "props": {"y": 32, "x": 665, "skin": "common3/btn_back3.png", "scaleY": 0.8, "scaleX": 0.8, "name": "close", "stateNum": 1, "anchorX": 0.5, "anchorY": 0.5}, "compId": 3}, {"type": "<PERSON><PERSON>", "props": {"y": 98, "x": 656, "width": 154, "var": "btnBackMode", "stateNum": 3, "skin": "common/btnGreen.png", "scaleY": 0.8, "scaleX": 0.8, "pivotY": 30, "pivotX": 77, "label": "后台播放", "height": 60, "labelSize": 24, "labelColors": "#b1e6bf,#b1e6bf,#b1e6bf", "anchorX": 0.5, "anchorY": 0.5}, "compId": 170}, {"type": "Sprite", "props": {"y": 82, "x": 0, "width": 376, "var": "skillList", "height": 567}, "compId": 40, "child": [{"type": "Image", "props": {"top": 0, "skin": "common/content_bg2.png", "right": 0, "left": 0, "bottom": 0, "alpha": 0.6, "sizeGrid": "14,14,14,14"}, "compId": 42}, {"type": "Image", "props": {"top": 1, "skin": "common/content_bg2.png", "right": 0, "left": 0, "height": 30, "alpha": 1, "sizeGrid": "14,14,14,14"}, "compId": 51, "child": [{"type": "Label", "props": {"y": 1, "x": 0, "width": 146, "valign": "middle", "text": "技能列表", "langTag": "JI_NENG_LIE_BIAO", "height": 30, "fontSize": 26, "color": "#ffffff", "bold": false, "align": "center"}, "compId": 52}, {"type": "CheckBox", "props": {"y": 7, "x": 132, "width": 231, "var": "cbOnlySelfSkills", "skin": "battleEditor/checkbox_16.png", "selected": true, "labelSize": 22, "labelColors": "#ffa913,#ffa913,#ffa913", "labelBold": true, "label": "只显示自身拥有的技能", "height": 21}, "compId": 23}]}, {"type": "Box", "props": {"width": 400, "var": "skillListBox", "top": 59, "right": 0, "left": 0, "height": 430, "bottom": 30}, "compId": 43, "child": [{"type": "VScrollBar", "props": {"top": 1, "skin": "common/vscroll.png", "right": 0, "name": "bar", "bottom": 0}, "compId": 44}]}, {"type": "Label", "props": {"x": 0, "width": 717, "var": "lbSkillDesc", "valign": "middle", "text": "选中技能描述技能选中技能描述技能", "langTag": "XUAN_ZHONG_JI_NENG_MIAO_SHU", "height": 23, "fontSize": 20, "color": "#8ef44b", "bottom": 5, "bold": false, "align": "left"}, "compId": 72}, {"type": "TextInput", "props": {"width": 371, "var": "txtSkillFilter", "top": 31, "strokeColor": "#000000", "stroke": 0, "skin": "common/textinput.png", "promptColor": "#E0EDFB", "prompt": "输入id或名称筛选,用逗号隔开", "padding": "0,0,0,10", "left": 0, "height": 28, "fontSize": 20, "color": "#E0EDFB", "align": "left", "bold": "true", "sizeGrid": "8,8,8,8,1"}, "compId": 100}]}, {"type": "Sprite", "props": {"y": 114, "x": 383, "width": 335, "var": "skillInfo", "height": 525}, "compId": 41, "child": [{"type": "Image", "props": {"top": 0, "skin": "common/content_bg2.png", "right": 0, "left": 0, "bottom": 0, "alpha": 0.6, "sizeGrid": "14,14,14,14"}, "compId": 46}, {"type": "Image", "props": {"y": 0, "x": 0, "top": 0, "skin": "common/content_bg2.png", "right": 0, "left": 0, "height": 30, "alpha": 1, "sizeGrid": "14,14,14,14"}, "compId": 53, "child": [{"type": "Label", "props": {"valign": "middle", "top": 0, "text": "所选技能数据", "right": 0, "left": 0, "langTag": "SUO_XUAN_JI_NENG_SHU_JU", "fontSize": 26, "color": "#ffffff", "bottom": 0, "bold": false, "align": "center"}, "compId": 54}]}, {"type": "Box", "props": {"var": "skillInfoBox", "top": 30, "right": 0, "left": 0, "bottom": 0}, "compId": 47, "child": [{"type": "VScrollBar", "props": {"top": 0, "skin": "common/vscroll.png", "right": 0, "name": "bar", "bottom": 0}, "compId": 48}]}]}, {"type": "Sprite", "props": {"y": 650, "x": 0, "width": 378, "var": "skillEffectList", "height": 466}, "compId": 30, "child": [{"type": "Image", "props": {"top": 2, "skin": "common/content_bg2.png", "right": 0, "left": 0, "bottom": 0, "alpha": 0.6, "sizeGrid": "14,14,14,14"}, "compId": 31}, {"type": "Image", "props": {"top": 2, "skin": "common/content_bg2.png", "right": 0, "left": 0, "height": 30, "alpha": 1, "sizeGrid": "14,14,14,14"}, "compId": 55, "child": [{"type": "Label", "props": {"y": 3, "x": 10, "valign": "middle", "text": "技能效果列表", "langTag": "JI_NENG_XIAO_GUO_LIE_BIAO", "fontSize": 26, "color": "#ffffff", "bold": false, "align": "center"}, "compId": 56}, {"type": "CheckBox", "props": {"y": 8, "x": 178, "width": 231, "var": "cbOnlyBelongSkill", "skin": "battleEditor/checkbox_16.png", "selected": true, "labelSize": 22, "labelColors": "#ffa913,#ffa913,#ffa913", "labelBold": true, "label": "只显示所选技能相关", "height": 21}, "compId": 71}]}, {"type": "Box", "props": {"width": 400, "var": "effectListBox", "top": 64, "right": 0, "left": 0, "height": 426, "bottom": 26}, "compId": 32, "child": [{"type": "VScrollBar", "props": {"top": 2, "skin": "common/vscroll.png", "right": 0, "name": "bar", "bottom": 0}, "compId": 33}]}, {"type": "Label", "props": {"y": 439, "x": 0, "width": 717, "var": "lbEffectDesc", "valign": "middle", "text": "选中特效描述选中特效描述选中特效描述", "langTag": "XUAN_ZHONG_TE_XIAO_MIAO_SHU", "height": 23, "fontSize": 20, "color": "#8ef44b", "bold": false, "align": "left"}, "compId": 73}, {"type": "TextInput", "props": {"width": 371, "var": "txtEffectFilter", "top": 32, "strokeColor": "#000000", "stroke": 0, "skin": "common/textinput.png", "promptColor": "#E0EDFB", "prompt": "输入id或名称筛选,用逗号隔开", "padding": "0,0,0,10", "left": 0, "height": 28, "fontSize": 20, "color": "#E0EDFB", "bold": false, "align": "left", "sizeGrid": "8,8,8,8,1"}, "compId": 101}]}, {"type": "Sprite", "props": {"y": 649, "x": 385, "width": 333, "var": "effectInfo", "height": 449}, "compId": 20, "child": [{"type": "Image", "props": {"top": 0, "skin": "common/content_bg2.png", "right": 0, "left": 0, "bottom": 0, "alpha": 0.6, "sizeGrid": "14,14,14,14"}, "compId": 15}, {"type": "Image", "props": {"top": 1, "skin": "common/content_bg2.png", "right": 0, "left": 0, "height": 30, "alpha": 1, "sizeGrid": "14,14,14,14"}, "compId": 57, "child": [{"type": "Label", "props": {"valign": "middle", "top": 1, "text": "所选效果数据", "right": 0, "left": 0, "langTag": "SUO_XUAN_XIAO_GUO_SHU_JU", "fontSize": 26, "color": "#ffffff", "bottom": 0, "bold": false, "align": "center"}, "compId": 58}]}, {"type": "Box", "props": {"var": "effectInfoBox", "top": 31, "right": 0, "left": 0, "bottom": 0}, "compId": 16, "child": [{"type": "VScrollBar", "props": {"top": 0, "skin": "common/vscroll.png", "right": 0, "name": "bar", "bottom": 0}, "compId": 17}]}]}, {"type": "Label", "props": {"y": 14, "x": 257, "width": 55, "text": "英雄\\nscale", "langTag": "REN_ZHE", "height": 44, "fontSize": 22, "font": "Microsoft YaHei", "color": "#ffffff", "bold": false, "align": "center"}, "compId": 74}, {"type": "Label", "props": {"y": 11, "x": 391, "width": 63, "text": "英雄\\n皮肤", "langTag": "REN_ZHE_PI_FU", "height": 44, "fontSize": 22, "font": "Microsoft YaHei", "color": "#ffffff", "bold": false, "align": "center"}, "compId": 124, "child": [{"type": "ComboBox", "props": {"y": -6, "x": 63, "width": 150, "var": "cb<PERSON><PERSON>", "skin": "common/bg24.png", "selectedIndex": 0, "labels": "label1,label2", "labelSize": 24, "itemSize": 24, "height": 50}, "compId": 125}]}, {"type": "TextInput", "props": {"y": 14, "x": 320, "width": 62, "var": "holeScale", "text": "1", "skin": "common/textinput.png", "height": 36, "fontSize": 22, "font": "Microsoft YaHei", "color": "#08f454", "promptColor": "#b9977e", "bold": "true", "padding": "0,0,0,10", "sizeGrid": "8,8,8,8,1"}, "compId": 75}, {"type": "Label", "props": {"y": 14, "x": 395, "width": 63, "visible": false, "text": "小兵\\nscale", "langTag": "XIAO_BING", "height": 44, "fontSize": 22, "font": "Microsoft YaHei", "color": "#ffffff", "bold": false, "align": "center"}, "compId": 78}, {"type": "TextInput", "props": {"y": 15, "x": 460, "width": 62, "visible": false, "var": "soldierScale", "text": "1", "skin": "common/textinput.png", "height": 36, "fontSize": 22, "font": "Microsoft YaHei", "color": "#08f454", "promptColor": "#b9977e", "bold": "true", "padding": "0,0,0,10", "sizeGrid": "8,8,8,8,1"}, "compId": 79}, {"type": "CheckBox", "props": {"y": 13, "x": 537, "width": 145, "visible": false, "var": "showSoldier", "skin": "battleEditor/checkbox.png", "scaleY": 0.6, "scaleX": 0.6, "labelSize": 35, "labelPadding": "0,0,0,10", "labelFont": "Microsoft YaHei", "labelColors": "#ffffff,#ffffff,#ffffff", "labelBold": true, "label": "显示\\n小兵", "height": 66}, "compId": 87}]}, {"type": "Box", "props": {"width": 1800, "var": "boxBottom", "right": 0, "left": 0, "height": 166, "bottom": 0}, "compId": 127, "child": [{"type": "<PERSON><PERSON>", "props": {"y": 25, "x": 84, "width": 163, "var": "hideAllBtn", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "隐藏编辑框", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 50}, {"type": "<PERSON><PERSON>", "props": {"y": 25, "x": 230, "var": "playBtn", "skin": "common/btnYellow.png", "labelPadding": "0,0,0,-20", "label": "预览", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 9, "child": [{"type": "CheckBox", "props": {"y": 31, "x": 73, "width": 55, "var": "cbForeverPlay", "skin": "battleEditor/checkbox_16.png", "labelSize": 18, "labelColors": "#000000,#000000,#000000", "labelBold": true, "label": "循环", "height": 21}, "compId": 68}]}, {"type": "ComboBox", "props": {"y": 0, "x": 514, "var": "cbFightMode", "skin": "common/select_frame.png", "labelSize": 24, "itemSize": 24}, "compId": 169}, {"type": "Box", "props": {"y": 55, "x": 8, "width": 679, "height": 57}, "compId": 135, "child": [{"type": "TextInput", "props": {"y": 1, "x": -2, "width": 161, "var": "inputAtkNum", "skin": "common/textinput.png", "promptColor": "#a1a1a1", "prompt": "攻击数量", "height": 36, "fontSize": 22, "font": "Microsoft YaHei", "color": "#08f454", "bold": "true", "padding": "0,0,0,10", "sizeGrid": "8,8,8,8,1"}, "compId": 140}, {"type": "TextInput", "props": {"y": 1, "x": 178.5, "width": 161, "var": "inputSkillCdMs", "skin": "common/textinput.png", "promptColor": "#a1a1a1", "prompt": "该技能cd(毫秒)", "height": 36, "fontSize": 22, "font": "Microsoft YaHei", "color": "#08f454", "bold": "true", "padding": "0,0,0,10", "sizeGrid": "8,8,8,8,1"}, "compId": 137}, {"type": "ComboBox", "props": {"y": -8.166666666666664, "x": 502.5, "var": "cbPlaySkillType", "skin": "common/select_frame.png", "labelSize": 24, "itemSize": 24}, "compId": 168}]}, {"type": "Box", "props": {"y": 110, "x": 9, "width": 541, "var": "boxMonster", "height": 57}, "compId": 131, "child": [{"type": "TextInput", "props": {"y": 9, "x": -5, "width": 149, "var": "inputMinMonsterNum", "skin": "common/textinput.png", "promptColor": "#a1a1a1", "prompt": "最少怪物数量", "height": 36, "fontSize": 22, "font": "Microsoft YaHei", "color": "#08f454", "bold": "true", "padding": "0,0,0,10", "sizeGrid": "8,8,8,8,1"}, "compId": 134}, {"type": "TextInput", "props": {"y": 9, "x": 158, "width": 134, "var": "inputAddMonsterId", "skin": "common/textinput.png", "promptColor": "#a1a1a1", "prompt": "随机怪物id", "height": 36, "fontSize": 22, "font": "Microsoft YaHei", "color": "#08f454", "bold": "true", "padding": "0,0,0,10", "sizeGrid": "8,8,8,8,1"}, "compId": 128}, {"type": "TextInput", "props": {"y": 9, "x": 301, "width": 98, "var": "inputAddMonsterNum", "skin": "common/textinput.png", "prompt": "怪物数量", "height": 36, "fontSize": 22, "font": "Microsoft YaHei", "color": "#08f454", "promptColor": "#b9977e", "bold": "true", "padding": "0,0,0,10", "sizeGrid": "8,8,8,8,1"}, "compId": 132}, {"type": "<PERSON><PERSON>", "props": {"y": 27, "x": 481.5, "width": 143, "var": "btnAddMonstter", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 71.5, "label": "添加怪物", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 133}]}, {"type": "<PERSON><PERSON>", "props": {"y": 134, "x": 642, "width": 163, "var": "btnFuncPanel", "skin": "common/btnGreen.png", "pivotY": 30, "pivotX": 81.5, "label": "打开功能面板", "height": 60, "labelSize": 24, "labelColors": "#b1e6bf,#b1e6bf,#b1e6bf", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 146}]}, {"type": "Box", "props": {"y": -300, "x": 1430, "width": 1075, "var": "boxFuncPanel", "height": 1280}, "compId": 144, "child": [{"type": "Image", "props": {"width": 1075, "skin": "common/black.png", "sizeGrid": "10,10,10,10", "right": 0, "left": 0, "height": 476, "bottom": 0, "alpha": 0.7}, "compId": 145}, {"type": "Image", "props": {"width": 1075, "top": 0, "skin": "common/img_select3.png", "sizeGrid": "10,10,10,10", "right": 0, "left": 0, "height": 1280, "bottom": 0, "alpha": 0.7}, "compId": 167}, {"type": "Image", "props": {"y": 800, "x": 8, "width": 1058, "skin": "common/item_select.png", "sizeGrid": "18,18,34,18,1", "height": 144}, "compId": 152, "child": [{"type": "<PERSON><PERSON>", "props": {"y": 37, "x": 100, "width": 163, "var": "btnAddPoint", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "添加路点", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 141}, {"type": "<PERSON><PERSON>", "props": {"y": 37, "x": 258, "width": 163, "var": "btnDelPoint", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "删除路点", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 142}, {"type": "<PERSON><PERSON>", "props": {"y": 102, "x": 598, "width": 163, "var": "btnSavePoint", "skin": "common/btnGreen.png", "pivotY": 30, "pivotX": 81.5, "label": "保存路点", "height": 60, "labelSize": 24, "labelColors": "#b1e6bf,#b1e6bf,#b1e6bf", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 147}, {"type": "<PERSON><PERSON>", "props": {"y": 37, "x": 588, "width": 163, "var": "btnStartPoint", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "开始寻路", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 148}, {"type": "<PERSON><PERSON>", "props": {"y": 37, "x": 751, "width": 163, "var": "btnPatrol", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "开始巡逻", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 149}, {"type": "<PERSON><PERSON>", "props": {"y": 97, "x": 100, "width": 163, "var": "btnAddPointGroup", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "添加路点组", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 172}, {"type": "<PERSON><PERSON>", "props": {"y": 97, "x": 263, "width": 163, "var": "btnDelPointGroup", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "删除路点组", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 173}, {"type": "Label", "props": {"y": 15, "x": 339.5, "width": 63, "valign": "middle", "text": "地图", "langTag": "REN_ZHE_PI_FU", "height": 44, "fontSize": 22, "font": "Microsoft YaHei", "color": "#ffffff", "bold": false, "align": "center"}, "compId": 175, "child": [{"type": "ComboBox", "props": {"y": 3.5, "x": 63, "width": 96, "var": "cbMap", "skin": "common/select_frame.png", "labelSize": 24, "itemSize": 24, "height": 37}, "compId": 174}]}, {"type": "Label", "props": {"y": 76, "x": 344.5, "width": 63, "valign": "middle", "text": "路点\\n范围", "langTag": "REN_ZHE_PI_FU", "height": 44, "fontSize": 22, "font": "Microsoft YaHei", "color": "#ffffff", "bold": false, "align": "center"}, "compId": 177, "child": [{"type": "TextInput", "props": {"y": 4, "x": 53, "width": 120, "var": "inputPointRange", "skin": "common/textinput.png", "promptColor": "#a1a1a1", "prompt": "路点范围", "height": 36, "fontSize": 22, "font": "Microsoft YaHei", "color": "#08f454", "bold": "true", "padding": "0,0,0,10", "sizeGrid": "8,8,8,8,1"}, "compId": 171}]}]}, {"type": "Image", "props": {"y": 952, "x": 14, "width": 1047, "skin": "common/item_select.png", "sizeGrid": "18,18,34,18,1", "height": 144}, "compId": 153, "child": [{"type": "<PERSON><PERSON>", "props": {"y": 37, "x": 100, "width": 163, "var": "btnAddHeroPoint", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "添加\\n武将出生点", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 154}, {"type": "<PERSON><PERSON>", "props": {"y": 37, "x": 258, "width": 163, "var": "btnDelHeroPoint", "skin": "common/btnYellow.png", "pivotY": 30, "pivotX": 81.5, "label": "删除\\n武将出生点", "height": 60, "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 155}, {"type": "<PERSON><PERSON>", "props": {"y": 37, "x": 418, "width": 163, "var": "btnSaveHeroPoint", "skin": "common/btnGreen.png", "pivotY": 30, "pivotX": 81.5, "label": "保存\\n武将出生点", "height": 60, "labelSize": 24, "labelColors": "#b1e6bf,#b1e6bf,#b1e6bf", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 156}]}, {"type": "Image", "props": {"y": 1098, "x": 14, "width": 1047, "skin": "common/item_select.png", "sizeGrid": "18,18,34,18,1", "height": 182}, "compId": 163, "child": [{"type": "<PERSON><PERSON>", "props": {"y": 144, "x": 103, "width": 165, "var": "btnCommit", "skin": "common/btnYellow.png", "pivotY": 23.5, "pivotX": 76.5, "labelSize": 20, "label": "提交所有修改", "height": 50, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 159}, {"type": "<PERSON><PERSON>", "props": {"y": 95, "x": 101, "width": 165, "var": "btnRevertOri", "skin": "common/btnYellow.png", "pivotY": 17, "pivotX": 81, "labelSize": 20, "label": "恢复到原始配置", "height": 50, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 160}, {"type": "<PERSON><PERSON>", "props": {"y": 46, "x": 101, "width": 165, "var": "btnRevertToLastCommit", "skin": "common/btnYellow.png", "pivotY": 17, "pivotX": 79.5, "labelSize": 20, "label": "恢复到上一次提交", "height": 50, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 161}, {"type": "<PERSON><PERSON>", "props": {"y": 144, "x": 294, "width": 165, "var": "btnSkEditor", "skin": "common/btnYellow.png", "pivotY": 23.5, "pivotX": 76.5, "labelSize": 20, "label": "显示动画预览", "height": 50, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5}, "compId": 126}]}]}], "loadList": ["common/img_13.png", "res/w3/BattleEditorRoleNameItem.scene", "common3/btn_back3.png", "common/btnGreen.png", "common/content_bg2.png", "battleEditor/checkbox_16.png", "common/vscroll.png", "common/textinput.png", "common/bg24.png", "battleEditor/checkbox.png", "common/btnYellow.png", "common/select_frame.png", "common/black.png", "common/img_select3.png", "common/item_select.png"], "loadList3D": []}