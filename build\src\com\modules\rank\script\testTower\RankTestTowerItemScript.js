import { DateUtil } from "../../../../util/DateUtil";
import { BaseRankScript } from "../BaseRankScript";
/**试练塔排行榜item */
export class RankTestTowerItemScript extends BaseRankScript {
    get tempView() {
        return super.tempView;
    }
    /**排行数值 关卡数*/
    get txtSortVal() {
        if (!this.tempView)
            return null;
        return this.tempView.txtPassNum;
    }
    /**用时*/
    get txtUseTime() {
        if (!this.tempView)
            return null;
        return this.tempView.txtUseTime;
    }
    /**勋章 */
    get medalslist() {
        if (!this.tempView)
            return null;
        return this.tempView.medalslist;
    }
    get headBox() {
        if (!this.tempView)
            return null;
        return this.tempView.headBox;
    }
    get txtName() {
        if (!this.tempView)
            return null;
        return this.tempView.txtName;
    }
    onAwake() {
        super.onAwake();
        this.isShowFcRank = true;
    }
    updateRankItemView(itemVo) {
        super.updateRankItemView(itemVo);
        if (!itemVo)
            return;
        // 用时消耗
        // this.setUseTimeStr(DateUtil.GetHMS(itemVo.getOtherParamByKey(RankOtherParamsConst.USE_TIME)));
        this.setUseTimeStr(DateUtil.GetHMS(itemVo.getEntityExt(0)));
    }
    /**设置用时消耗 */
    setUseTimeStr(text) {
        if (this.txtUseTime) {
            this.txtUseTime.text = text;
        }
    }
}
