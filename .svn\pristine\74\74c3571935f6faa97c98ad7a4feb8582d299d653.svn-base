import { Event } from "laya/events/Event";
import { UrlConfig } from "../../../../game/UrlConfig";
import { MiscConstAuto } from "../../../auto/MiscConstAuto";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { p_auction_house_goods } from "../../../proto/common/p_auction_house_goods";
import { com } from "../../../ui/layaMaxUI";
import { DateUtil } from "../../../util/DateUtil";
import { HtmlUtil } from "../../../util/HtmlUtil";
import { StringUtil } from "../../../util/StringUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { IUIListItem } from "../../baseModules/IUListItem";
import { UIListItemData } from "../../baseModules/UIListItemData";
import { DataCenter } from "../../DataCenter";
import GoodsItem from "../../goods/GoodsItem";
import { GoodsVO } from "../../goods/GoodsVO";
import { AuctionHouseDataCenter, EAuctionHouseOpType, EMyAuctionPageType } from "../data/AuctionHouseDataCenter";
import { ModuleCommand } from "../../ModuleCommand";


export default class AuctionHouseItem extends com.ui.res.auctionHouse.AuctionHouseAuctionItemUI implements IUIListItem {
    isSelect: boolean;
    private OPType: number = 0;
    private type: number = 0;

    private goodInfo: p_auction_house_goods;


    private _goodsItem: GoodsItem;

    private _goodVO: GoodsVO;

    /**消耗货币 */
    private costTypeId: number = MiscConstAuto.auction_house_use_item_id;


    initUI(): void {
        this._goodsItem = this.goodsItem;

        this.costTypeId = MiscConstAuto.auction_house_use_item_id;


        this.SetUIHTMLDiv(this.lbFixedPrice, 20, "#855033");
        this.SetUIHTMLDiv(this.lbNowPrice, 20, "#855033");

    }

    addClick(): void {
        this.addOnClick(this, this.btnGet, this.OnClickBtnGet)
        this.addOnClick(this, this.imgBg, this.OnClickAll).setIsClickScale(false);

    }

    addEvent(): void {

    }

    UpdateItem(itemData: UIListItemData, select: boolean): void {
        if (!itemData.data) { return; }
        this.goodInfo = itemData.data;

        if (itemData.parameter && itemData.parameter.OPType) {
            this.OPType = itemData.parameter.OPType;
        }
        if (itemData.parameter && itemData.parameter.type) {
            this.type = itemData.parameter.type;
        }


        this.initBaseInfo();
        this.initType();

    }

    initBaseInfo() {
        this._goodVO = GoodsVO.GetVoByPGoods(this.goodInfo.goods);
        this._goodsItem.SetData(this._goodVO);
        this.lbName.text = this._goodVO.name;

        if (this.goodInfo.shelve_role) {
            this.lbOwner.text = StringUtil.Format("上架人：[{0}服]{1}", this.goodInfo.shelve_role.server_id, this.goodInfo.shelve_role.role_name);
        } else {
            this.lbOwner.text = "";
        }

        if (this.goodInfo.buyout_price > 0) {
            let cost = HtmlUtil.GetGoodsHtmlImg(this.costTypeId, 35, 35);
            this.lbFixedPrice.innerHTML = StringUtil.Format("一口价：{0}{1}", cost, this.goodInfo.buyout_price);
        } else {
            this.lbFixedPrice.innerHTML = "";
        }


        this.timer.loop(1000, this, this.setTimer).runImmediately();
    }

    setTimer() {
        let endTime = this.goodInfo.time;
        let nowTime = DataCenter.serverTimeSeconds;
        let time = endTime - nowTime;
        if (time > 0) {
            this.lbTime.text = DateUtil.GetDhOrHM(time, "{0}时{1}分", "{0}分");

        } else {
            this.lbTime.text = "已结束";
            this.timer.clear(this, this.setTimer);
        }
    }

    initType() {
        //--------------物品拍卖----------------
        if (this.OPType == EAuctionHouseOpType.VIEW_AUCTION_INFO) {
            if (this.goodInfo.bidding_price > 0) {
                let cost = HtmlUtil.GetGoodsHtmlImg(this.costTypeId, 35, 35);
                this.lbNowPrice.innerHTML = StringUtil.Format("当前竞拍：{0}{1}", cost, this.goodInfo.bidding_price);

            } else {
                this.lbNowPrice.innerHTML = ""
            }
            if (this.goodInfo.bidding_role.role_id > 0) {
                this.lbBiddingName.text = StringUtil.Format("竞拍人：[{0}]服{1}", this.goodInfo.bidding_role.server_id, this.goodInfo.bidding_role.role_name);
            } else {
                this.lbBiddingName.text = ""
            }

            if (this.goodInfo.bidding_role.role_id == DataCenter.myRoleID) {
                this.imgSign.skin = "auctionHouse/img_pr.png";
            } else {
                if (this.goodInfo.bidding_role.role_id > 0) {
                    this.imgSign.skin = "auctionHouse/img_bidding.png";
                } else {
                    this.imgSign.skin = "";
                }
            }



        }

        //--------------我的拍卖----------------
        else if (this.OPType == EAuctionHouseOpType.VIEW_MY_ITEMS) {
            if (this.goodInfo.bidding_price > 0) {
                let cost = HtmlUtil.GetGoodsHtmlImg(this.costTypeId, 35, 35);
                this.lbNowPrice.innerHTML = StringUtil.Format("当前竞拍：{0}{1}", cost, this.goodInfo.bidding_price);

            } else {
                this.lbNowPrice.innerHTML = ""

            }
            if (this.goodInfo.bidding_price > 0) {
                this.lbBiddingName.text = StringUtil.Format("竞拍人：[{0}]服{1}", this.goodInfo.bidding_role.server_id, this.goodInfo.bidding_role.role_name);
            } else {
                this.lbBiddingName.text = ""
            }

            if (this.goodInfo.bidding_role.role_id == DataCenter.myRoleID) {
                this.imgSign.skin = "auctionHouse/img_pr.png";
            } else {
                if (this.goodInfo.bidding_role.role_id > 0) {
                    this.imgSign.skin = "auctionHouse/img_bidding.png";
                } else {
                    this.imgSign.skin = "";
                }
            }

            if (this.type == EMyAuctionPageType.LIST) {
                this.btnGet.label = "下架";
                this.btnGet.skin = "v2_common/btn_green.png";
                this.lbBiddingName.text = "";
                this.btnGet.visible = this.goodInfo.bidding_role.role_id <= 0
            }

        }

        //--------------我的账户----------------
        else if (this.OPType == EAuctionHouseOpType.MY_ITEM_LIST) {
            if (this.goodInfo.bidding_price > 0) {
                let cost = HtmlUtil.GetGoodsHtmlImg(this.costTypeId, 35, 35);
                this.lbNowPrice.innerHTML = StringUtil.Format("竞价：{0}{1}", cost, this.goodInfo.bidding_price);

            } else {
                this.lbNowPrice.innerHTML = "";
            }

            this.lbBiddingName.text = "";
            this.imgSign.skin = "";


            this.btnGet.visible = true;
            this.btnGet.label = "领取";
            this.btnGet.skin = "v2_common/btn_yellow.png";
        }
    }


    OnClickBtnGet() {
        if (this.OPType == EAuctionHouseOpType.VIEW_MY_ITEMS && this.type == EMyAuctionPageType.LIST) {
            TipsUtil.showDialog(this, StringUtil.Format("是否下架{0}", this._goodVO.name), null, () => {
                AuctionHouseDataCenter.instance.auctionHouseOpTOS(EAuctionHouseOpType.DELIST, this.type, this.goodInfo.id);
            })
            return;
        }

        if (this.OPType == EAuctionHouseOpType.MY_ITEM_LIST) {
            // 领取界面
            let obj = {
                type: this.type,
                info: this.goodInfo,
            }

            this.dispatchEvent(ModuleCommand.OPEN_AUCTION_HOUSE_GET_DIALOG, obj);
            return;
        }
    }

    OnClickAll() {
        if (this.OPType == EAuctionHouseOpType.VIEW_AUCTION_INFO) {
            //打开拍卖界面
            this.dispatchEvent(ModuleCommand.OPEN_AUCTION_HOUSE_BIDDING_DIALOG, this.goodInfo);
        }
    }

    Clean(): void {
        this.lbBiddingName.text = "";
        this.lbNowPrice.innerHTML = "";
        this.lbName.text = "";
        this.lbOwner.text = "";
        this.lbFixedPrice.innerHTML = "";
        this.lbTime.text = "";
        this.timer.clear(this, this.setTimer);
        this.imgSign.skin = "";
        this._goodsItem.Clean();
        this.btnGet.visible = false;
        this.btnGet.label = "";

    }



}
