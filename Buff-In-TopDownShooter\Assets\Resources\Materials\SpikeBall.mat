%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SpikeBall
  m_Shader: {fileID: 3, guid: 0000000000000000f000000000000000, type: 0}
  m_ShaderKeywords: 
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: a7620eecb715ccf409f41bda9e06f7cc, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _Shininess: 0.3202985
    m_Colors:
    - _Color: {r: 0.8, g: 0.8, b: 0.8, a: 1}
    - _SpecColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!1002 &2100001
EditorExtensionImpl:
  serializedVersion: 6
