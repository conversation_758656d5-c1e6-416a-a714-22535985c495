import { BaseLineUpLimitHelper } from "../../lineUp/helper/BaseLineUpLimitHelper";
import { MatchConst } from "../../../auto/ConstAuto";
export class WorldTreasureLineUpLimitHelper extends BaseLineUpLimitHelper {
    constructor() {
        super(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE);
    }
    checkIsShowAssistant() {
        return false;
    }
    checkIsShowUseDefaultCheckBox() {
        return false;
    }
    checkIsUnlockTeam(team) {
        return true;
    }
}
