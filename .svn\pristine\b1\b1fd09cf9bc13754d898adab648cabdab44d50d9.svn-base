import { Point } from "laya/maths/Point";
import { PanelIconMacro } from "../../../auto/PanelConstAuto";
import { Connection } from "../../../net/Connection";
import { p_simp_mission } from "../../../proto/common/p_simp_mission";
import { p_world_secret_treasure_gdn } from "../../../proto/common/p_world_secret_treasure_gdn";
import { m_world_secret_treasure_gdn_update_toc } from "../../../proto/line/m_world_secret_treasure_gdn_update_toc";
import { m_world_secret_treasure_op_tos } from "../../../proto/line/m_world_secret_treasure_op_tos";
import { ArrayUtil } from "../../../util/ArrayUtil";
import { RedPointMgr } from "../../redPoint/RedPointMgr";
import { XmlFormatVo } from "../../../util/XmlFormatVo";
import MissionConst, { MissionStatus } from "../../mission/MissionConst";
import { cfg_world_secret_treasure_mission } from "../../../cfg/vo/cfg_world_secret_treasure_mission";
import { CfgCacheMapMgr } from "../../../cfg/CfgCacheMapMgr";
import { GoodsManager } from "../../test_bag/GoodsManager";
import { m_simp_mission_accept_tos } from "../../../proto/line/m_simp_mission_accept_tos";
import { m_simp_mission_finish_tos } from "../../../proto/line/m_simp_mission_finish_tos";
import { m_simp_mission_collect_tos } from "../../../proto/line/m_simp_mission_collect_tos";
import { m_simp_mission_fetch_tos } from "../../../proto/line/m_simp_mission_fetch_tos";
import { LineUpUtil } from "../../lineUp/util/LineUpUtil";
import { LineUpCheckUtil } from "../../lineUp/util/LineUpCheckUtil";
import { MatchConst } from "../../../auto/ConstAuto";
import { StringUtil } from "../../../util/StringUtil";
import { TipsUtil } from "../../../util/TipsUtil";
import { Handler } from "laya/utils/Handler";
import { LineUpVO } from "../../lineUp/vo/LineUpVO";
import { DispatchManager } from "../../../managers/DispatchManager";
import { ModuleCommand } from "../../ModuleCommand";
export enum WorldTreasureOpType{
    COMPLETE_ALL_TASK = 1,//一键完成任务
    REFRESH_TASK = 2,//刷新任务
    OPEN_TREASURE_MAP = 3,//开宝藏图
    UNEARTh_TREASURE = 4,//挖宝
    ENTER_MAP = 5,//进入地图
    EXIT_MAP = 6,//退出地图

}
export enum WorldTreasureFightType{
    MONSTER = 1,//小怪
    BOSS = 2,//守护者boss
}
export enum WorldTreasureBossType{
    UPDATE_BOSS_INFO = 1,//更新/添加
    DELETE_BOSS = 2,//删除
}
export class WorldTreasureDataCenter {
    private static _instance: WorldTreasureDataCenter = null;
    static get instance(): WorldTreasureDataCenter {
        if (WorldTreasureDataCenter._instance == null) {
            WorldTreasureDataCenter._instance = new WorldTreasureDataCenter();
        }
        return WorldTreasureDataCenter._instance;
    }

    public dataList:string[] = [];
    public static TREASURE_MAP_MISSION_RED_POINT: number = 1;
    private _playerFightPos: Point;//记录战斗时玩家的坐标
    private _treasurePos:number[];//藏宝图宝藏坐标
    public treasureElementId:number;
    private _taskMissionInfo: p_simp_mission;
    private _taskMissionCfg:cfg_world_secret_treasure_mission;
    private _remainTimes: number = 0;//剩余任务次数
    private _remain_add_reward_times = 0;//守护者奖励额外产出次数
    private _treasure_boosMap: Map<number, p_world_secret_treasure_gdn> = new Map();
    /**设置藏宝图守护者信息 */
    public setTreasureBoosMap(value:p_world_secret_treasure_gdn[]) {
        this._treasure_boosMap = ArrayUtil.getMapByArray(value, "id");
    }
    public get treasureBossMap(){
        return this._treasure_boosMap;
    }
    public setTreasurePos(pos:number[]){
        this._treasurePos = pos;
    }
    public get treasurePos(){
        return this._treasurePos;
    }
    public get playerFightPos(): Point {
        return this._playerFightPos;
    }
    public get remainAddRewardTimes() {
        return this._remain_add_reward_times;
    }
    public set remainAddRewardTimes(value) {
        this._remain_add_reward_times = value;
    }
    public setplayerFightPos(value: number[] | null) {
        this._playerFightPos = null
        if (value && value.length >= 2) {
            this._playerFightPos = new Point().setTo(value[0],value[1]);
        }
    }
    /**任务相关 */
    /** 1 targe 2 complete */
    public getNowMissionTalkList(talkText:string):XmlFormatVo[] { 
        return XmlFormatVo.GetVoArr(talkText);
    }
    public get remainTimes(): number {
        return this._remainTimes;
    }
    public set remainTimes(value: number) {
        this._remainTimes = value;
    }
    public isTaskAllCompleted():boolean {
        return this._remainTimes === 0;
    }
    public setTaskMission(dataList: p_simp_mission[]) {
        if (dataList.length > 0) {
            this._taskMissionInfo = dataList[0];
            this._taskMissionCfg = CfgCacheMapMgr.cfg_world_secret_treasure_missionCache.get(this._taskMissionInfo.id);
        }
    }
    public get taskMissionInfo(): p_simp_mission {
        return this._taskMissionInfo;
    }
    public updateTaskMission(data: p_simp_mission) {
        this._taskMissionInfo = data;
        this._taskMissionCfg = CfgCacheMapMgr.cfg_world_secret_treasure_missionCache.get(this._taskMissionInfo.id);
    }
    public checkMissionComplete(): boolean {
        if (this._taskMissionInfo.status == MissionStatus.ACC_REWARD) {
            return true;
        }
        if (this._taskMissionCfg && this._taskMissionInfo.status == MissionStatus.ACCEPT) {
            if (this._taskMissionCfg.event_type == MissionConst.MISSION_TYPE_STAGE_COPY_TALK) {
                return true;
            } else if (this._taskMissionCfg.event_type == MissionConst.MISSION_TYPE_COLLECT_GOOD) {
                if (this.taskMissionInfo.cur_progress >= this._taskMissionCfg.max_progress) {
                    return true;
                }
            }
        }
        return false;
    }
    public createLineUpVo():LineUpVO{
        let lineUpVo = LineUpUtil.createVO(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE);
        lineUpVo.confirmHandler = Handler.create(this,(param:any)=>{
            let emptyId = LineUpCheckUtil.getLineUpEmptyTeamId(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE)
            if (emptyId > 0) {
                let teamName =  LineUpUtil.getLineUpIndexName(MatchConst.MATCH_TYPE_WORLD_SECRET_TREASURE, emptyId);
                const str = StringUtil.FormatArr("{0}为空，需要至少上阵一个英雄",[teamName])
                TipsUtil.showTips(str);
            }
            else{
                param && param.callBack.run();
            }
        },[],false);
        return lineUpVo;
    }
    /**------------------------------------协议相关--------------------------- */
    /**
     * @param op_type 1:一键完成任务 2:刷新任务 3:开宝藏图 4:挖宝 5:进入地图 6:退出地图
     * @param param_list 
     */
    public m_world_secret_treasure_op_tos(op_type:number, param_list:number[] = []) : void {
        let tos : m_world_secret_treasure_op_tos = new m_world_secret_treasure_op_tos();
        tos.op_type = op_type;
        tos.param_list = param_list;
        Connection.instance.sendMessage(tos);
    }
    m_simp_mission_accept_tos(id: number): void {
        var tos: m_simp_mission_accept_tos = new m_simp_mission_accept_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_WORLD_TREASURE;
        tos.id = id;
        tos.big_type = PanelIconMacro.WORLD_SECRET_TREASURE;
        Connection.instance.sendMessage(tos);
    }

    m_simp_mission_finish_tos(id: number): void {
        var tos: m_simp_mission_finish_tos = new m_simp_mission_finish_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_WORLD_TREASURE;
        tos.id = id;
        tos.big_type =PanelIconMacro.WORLD_SECRET_TREASURE;
        Connection.instance.sendMessage(tos);
    }
    m_simp_mission_collect_tos(id:number){
        var tos: m_simp_mission_collect_tos = new m_simp_mission_collect_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_WORLD_TREASURE;
        tos.id = id;
        Connection.instance.sendMessage(tos);
    }
    m_simp_mission_fetch_tos(id: number): void {
        var tos: m_simp_mission_fetch_tos = new m_simp_mission_fetch_tos();
        tos.mission_type = MissionConst.MISSION_TYPE_WORLD_TREASURE;
        tos.id = id;
        Connection.instance.sendMessage(tos);
    }
}