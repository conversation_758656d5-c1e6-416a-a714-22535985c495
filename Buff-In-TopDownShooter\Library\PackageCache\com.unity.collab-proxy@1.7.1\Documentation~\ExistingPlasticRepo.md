# Getting started with an existing Plastic SCM repository

1. Download and install the Plastic SCM [Cloud Client](https://www.plasticscm.com/download/).

2. Open the Plastic SCM Cloud Client and log in.

   ![Plastic sign-in page](images/SignIn.png)

3. Join the Plastic **Organization** (this is different from your Unity organization) where the existing repository is located).

4. Select the workflow that fits your needs.

* **Developer Workflow**

  With this workflow, you can work with branching and merging. To check in your changes straight to your cloud repository, create your workspace using the centralized tab.

  ![Developer workflow](images/DeveloperWorkflow.png)

* **Gluon Workflow**

    This workflow tailored for artists allows you to pick the files you want to work on and check them back in without updating your whole workspace. To work inside the Unity Editor, make sure to configure Gluon to pull down all Unity project files required to open a Unity project.

  ![Gluon workflow](images/GluonWorkflow.png)

5. Create a Workspace.

* If your Unity project files are already on your machine, select the directory path corresponding to your Unity project's root.
* If your Unity project files are not on your machine, you can choose any location in which to create your workspace.

6. Update or Configure your Workspace.

* If you select the **Gluon Workflow** , configure your workspace to pull down all Unity project files required to open a Unity project.
* If you choose the **Developer Workflow** , update your workspace to ensure your project is up to date with all incoming changes.

7. Open your Unity project through the Hub.

   **Note**: Unity will add the ability to join projects directly from the Unity Hub in a future version.

8. Select **Window** &gt; **Plastic SCM**.

## Accessing the Plastic SCM Window

You can access the **Plastic SCM** window in the Unity Editor by selecting **Window** &gt; **Plastic SCM**.

![Plastic SCM window](images/AccessingPlastic.png)
