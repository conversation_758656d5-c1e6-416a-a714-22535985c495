{"type": "BaseDialog", "props": {"width": 720, "height": 1280}, "compId": 1, "child": [{"type": "TopInsAuto", "props": {"y": 100, "x": 0, "height": 1080, "runtime": "com/modules/common/TopInsAuto.ts"}, "compId": 55}, {"type": "Image", "props": {"y": 162, "x": 31, "width": 658, "skin": "common/content_bg2.png", "height": 874, "sizeGrid": "14,14,14,14"}, "compId": 60}, {"type": "Box", "props": {"y": 311, "x": 43}, "compId": 46, "child": [{"type": "Image", "props": {"y": 0, "x": 0, "width": 411, "skin": "common/content_bg.png", "sizeGrid": "5,5,5,5", "height": 83}, "compId": 47}, {"type": "TextInput", "props": {"y": 8, "x": 74.5, "width": 262, "var": "editBoxCdn", "text": "默认地址", "skin": "common/textinput.png", "sizeGrid": "5,5,5,5", "height": 29, "fontSize": 20, "color": "#2d2d2c", "align": "center", "promptColor": "#b9977e", "bold": "true", "padding": "0,0,0,10"}, "compId": 48}, {"type": "<PERSON><PERSON>", "props": {"y": 23, "x": 371, "var": "cdnportbtn", "skin": "common/btnBlue.png", "scaleY": 0.5, "scaleX": 0.5, "label": "确定", "labelSize": 24, "labelColors": "#1d2d43,#1d2d43,#1d2d43", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 49}, {"type": "Label", "props": {"y": 11, "x": 10, "width": 29, "valign": "middle", "text": "cdnurl", "height": 23, "fontSize": 20, "color": "#2d2d2c"}, "compId": 50}, {"type": "Label", "props": {"y": 11, "x": 145, "width": 29, "visible": false, "valign": "middle", "text": "端口", "height": 23, "fontSize": 20, "color": "#eee403"}, "compId": 51}, {"type": "TextInput", "props": {"y": 8, "x": 187, "width": 78, "visible": false, "var": "cdninput", "text": "6789", "skin": "common/emptyBg.png", "sizeGrid": "5,5,5,5", "height": 29, "fontSize": 20, "color": "#f4c509", "align": "center"}, "compId": 52}, {"type": "ComboBox", "props": {"y": 48, "x": 13, "width": 379, "var": "cdnSelectBox", "skin": "common/combobox.png", "sizeGrid": "0,20,0,0", "scrollBarSkin": "common/vscroll.png", "height": 23}, "compId": 53}]}, {"type": "Box", "props": {"y": 196, "x": 48.5, "var": "topIns"}, "compId": 13, "child": [{"type": "Image", "props": {"y": 0, "x": -6, "width": 626, "skin": "common/textinput.png", "sizeGrid": "5,5,5,5", "height": 83, "promptColor": "#b9977e", "bold": "true", "fontSize": 20, "color": "#825b3f", "padding": "0,0,0,10"}, "compId": 7}, {"type": "TextInput", "props": {"y": 8, "x": 24, "width": 262, "var": "editBoxIP", "text": "127.0.0.1", "skin": "common/textinput.png", "sizeGrid": "5,5,5,5", "height": 29, "fontSize": 20, "color": "#2d2d2c", "align": "center", "promptColor": "#b9977e", "bold": "true", "padding": "0,0,0,10"}, "compId": 8}, {"type": "<PERSON><PERSON>", "props": {"y": 44, "x": 548, "var": "ipportbtn", "skin": "common/btnBlue.png", "scaleY": 1, "scaleX": 1, "pivotY": 43.5, "pivotX": 65, "label": "确定", "labelSize": 24, "labelColors": "#1d2d43,#1d2d43,#1d2d43", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 9}, {"type": "Label", "props": {"y": 11, "x": 3, "width": 29, "valign": "middle", "text": "IP", "height": 23, "fontSize": 20, "color": "#2d2d2c"}, "compId": 10}, {"type": "Label", "props": {"y": 11, "x": 145, "width": 29, "visible": false, "valign": "middle", "text": "端口", "height": 23, "fontSize": 20, "color": "#eee403"}, "compId": 11}, {"type": "TextInput", "props": {"y": 8, "x": 187, "width": 78, "visible": false, "var": "labelPort", "text": "6789", "skin": "common/emptyBg.png", "sizeGrid": "5,5,5,5", "height": 29, "fontSize": 20, "color": "#f4c509", "align": "center"}, "compId": 12}, {"type": "ComboBox", "props": {"y": 48, "x": 24, "width": 260, "var": "ipSelectBox", "skin": "common/combobox.png", "sizeGrid": "0,20,0,0", "scrollBarSkin": "common/vscroll.png", "height": 23}, "compId": 28}, {"type": "TextInput", "props": {"y": 8, "x": 338, "width": 77, "var": "inputPort", "type": "number", "text": "6797", "skin": "common/textinput.png", "sizeGrid": "5,5,5,5", "height": 29, "fontSize": 20, "color": "#2d2d2c", "align": "center", "promptColor": "#b9977e", "bold": "true", "padding": "0,0,0,10"}, "compId": 67}, {"type": "Label", "props": {"y": 11, "x": 291, "width": 29, "valign": "middle", "text": "端口", "height": 23, "fontSize": 20, "color": "#2d2d2c"}, "compId": 68}]}, {"type": "Box", "props": {"y": 427, "x": 43, "width": 341, "height": 52}, "compId": 14, "child": [{"type": "Image", "props": {"width": 338, "skin": "common/content_bg.png", "sizeGrid": "5,5,5,5", "height": 47}, "compId": 15}, {"type": "TextInput", "props": {"y": 8, "x": 88, "width": 170, "var": "payTI", "text": "0.1", "skin": "common/textinput.png", "sizeGrid": "5,5,5,5", "restrict": "0123456789.", "height": 29, "fontSize": 20, "color": "#000000", "align": "center", "promptColor": "#b9977e", "bold": "true", "padding": "0,0,0,10"}, "compId": 16}, {"type": "<PERSON><PERSON>", "props": {"y": 22, "x": 297, "var": "payBtn", "skin": "common/btnBlue.png", "scaleY": 0.5, "scaleX": 0.5, "label": "充值", "labelSize": 24, "labelColors": "#1d2d43,#1d2d43,#1d2d43", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 17}, {"type": "Label", "props": {"y": 11, "x": 3, "width": 29, "valign": "middle", "text": "充值额度", "height": 23, "fontSize": 20, "color": "#2d2d2c", "bold": false}, "compId": 18}]}, {"type": "<PERSON><PERSON>", "props": {"y": 709, "x": 105, "var": "test1", "skin": "common/btnBlue.png", "label": "test1", "labelSize": 24, "labelColors": "#1d2d43,#1d2d43,#1d2d43", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 19}, {"type": "<PERSON><PERSON>", "props": {"y": 709, "x": 556, "var": "test4", "skin": "common/btnYellow.png", "label": "test4", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 20}, {"type": "<PERSON><PERSON>", "props": {"y": 709, "x": 405, "var": "test3", "skin": "common/btnBlue.png", "label": "打印缓存", "labelSize": 24, "labelColors": "#1d2d43,#1d2d43,#1d2d43", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 21}, {"type": "<PERSON><PERSON>", "props": {"y": 791, "x": 252, "var": "test6", "skin": "common/btnYellow.png", "label": "test6", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 22}, {"type": "<PERSON><PERSON>", "props": {"y": 791, "x": 105, "var": "test5", "skin": "common/btnBlue.png", "label": "test5", "labelSize": 24, "labelColors": "#1d2d43,#1d2d43,#1d2d43", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 23}, {"type": "<PERSON><PERSON>", "props": {"y": 791, "x": 405, "var": "test7", "skin": "common/btnBlue.png", "label": "test7", "labelSize": 24, "labelColors": "#1d2d43,#1d2d43,#1d2d43", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 24}, {"type": "<PERSON><PERSON>", "props": {"y": 710, "x": 252, "var": "test2", "skin": "common/btnYellow.png", "label": "test2", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 25}, {"type": "Box", "props": {"y": 506, "x": 43, "width": 343, "height": 54}, "compId": 29, "child": [{"type": "Image", "props": {"width": 338, "skin": "common/content_bg.png", "sizeGrid": "5,5,5,5", "height": 47}, "compId": 30}, {"type": "TextInput", "props": {"y": 8, "x": 115, "width": 146, "var": "wxPayTi", "text": "0.1", "skin": "common/textinput.png", "sizeGrid": "5,5,5,5", "restrict": "0123456789.", "height": 29, "fontSize": 20, "color": "#2d2d2c", "align": "center", "promptColor": "#b9977e", "bold": "true", "padding": "0,0,0,10"}, "compId": 31}, {"type": "<PERSON><PERSON>", "props": {"y": 24, "x": 300, "var": "wxPayBtn", "skin": "common/btnBlue.png", "scaleY": 0.5, "scaleX": 0.5, "label": "充值", "labelSize": 24, "labelColors": "#1d2d43,#1d2d43,#1d2d43", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 32}, {"type": "Label", "props": {"y": 11, "x": 3, "width": 29, "valign": "middle", "text": "wx充值额度", "height": 23, "fontSize": 20, "color": "#010101", "bold": false}, "compId": 33}]}, {"type": "Label", "props": {"y": 162, "x": 99, "width": 522, "text": "请注意：本界面所有功能仅限研发商开发测试用。", "strokeColor": "#0f0e0e", "stroke": 1, "height": 34, "fontSize": 20, "color": "#ff0905", "bold": false, "align": "center"}, "compId": 34}, {"type": "Label", "props": {"y": 576, "x": 99, "width": 522, "text": "请注意：本界面所有功能仅限研发商开发测试用。", "strokeColor": "#0f0e0e", "stroke": 1, "height": 34, "fontSize": 20, "color": "#ff0905", "bold": false, "align": "center"}, "compId": 35}, {"type": "<PERSON><PERSON>", "props": {"y": 311, "x": 598, "var": "protoBtn", "skin": "common/btnYellow.png", "labelSize": 20, "label": "协议开关", "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 37}, {"type": "<PERSON><PERSON>", "props": {"y": 791, "x": 559, "var": "btnGzip", "skin": "common/btnYellow.png", "label": "gzip测试", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 63}, {"type": "<PERSON><PERSON>", "props": {"y": 869, "x": 105, "var": "btnError1", "skin": "common/btnYellow.png", "label": "异常测试", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 64}, {"type": "<PERSON><PERSON>", "props": {"y": 869, "x": 252, "var": "btnError2", "skin": "common/btnYellow.png", "label": "异常测试2", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 65}, {"type": "<PERSON><PERSON>", "props": {"y": 869, "x": 405, "var": "btnSoundTest", "skin": "common/btnYellow.png", "label": "音效测试", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 66}, {"type": "<PERSON><PERSON>", "props": {"y": 944, "x": 105, "var": "btnStat", "skin": "common/btnYellow.png", "label": "性能面板", "labelSize": 24, "labelColors": "#50361d,#50361d,#50361d", "stateNum": 3, "anchorX": 0.5, "anchorY": 0.5, "width": 130, "height": 60}, "compId": 69}, {"type": "TextInput", "props": {"y": 610, "x": 50, "width": 615, "skin": "common/progress5.png", "sizeGrid": "5,5,5,5", "height": 29, "fontSize": 20, "color": "#2d2d2c", "align": "center", "prompt": "本地配置表目录,例如:F:\\trunk\\w6d\\config\\trunk\\iex", "promptColor": "#676767", "var": "inputLocalConfigDir"}, "compId": 70}], "loadList": ["res/base/TopInsAuto.scene", "common/content_bg2.png", "common/content_bg.png", "common/textinput.png", "common/btnBlue.png", "common/emptyBg.png", "common/combobox.png", "common/vscroll.png", "common/btnYellow.png", "common/progress5.png"], "loadList3D": []}